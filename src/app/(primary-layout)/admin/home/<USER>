"use client";
import React, { useState, memo, useEffect } from "react";
import { Button } from "antd";
import { useRouter } from "next/navigation";
import { getCompanyInfo } from "@/services/corporation";
import { IconSvg } from "@/components";

interface CompanyInfo {
  corporation?: {
    businessAddress: string;
    corporationId: number;
    corporationName: string;
    corporationType: number;
    creditStatus: number;
    industryType: string;
  };
  corporationAsset?: {
    accountNumber: number;
    worksNumber: number;
  };
  departmentOverview?: {
    corporationUserNumber: number;
    departmentNumber: number;
  };
}

const ManagementInfo = () => {
  const router = useRouter();
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({});

  useEffect(() => {
    async function fetchData() {
      const { data = {} } = await getCompanyInfo();
      if (data?.code === 0) {
        setCompanyInfo(data.result);
      }
    }
    fetchData();
  }, []);

  const getLabel = (ct: any) => {
    if (ct === 0) {
      return "个人账号";
    }
    if (ct === 1) {
      return "公司";
    }
    if (ct === 2) {
      return "个人主体";
    }
    return "";
  };

  const corporation: any = companyInfo?.corporation || {};
  const corporationType: any = corporation?.corporationType;
  return (
    <div className="h-full">
      <div
        className="pt-[22px] pr-[24px] pb-[18px] pl-[24px] rounded bg-contain text-white"
        style={{
          background:
            "url(https://static.creativebooster.com/cb-static/website/images/base-info-title-bg.png) no-repeat center right, linear-gradient(45deg, #2d57fa 0%, #4f46e4 100%)",
        }}
      >
        <h2 className="leading-6 text-[16px] text-white">Hi</h2>
        <p className="leading-6 text-[14px] opacity-85">
          欢迎使用酷悟酷博旗下Creative
          Booster创意一站式营销数字平台，助力您的出海之路。
        </p>
      </div>
      <div className="flex justify-between gap-6 mt-6">
        <div className="flex-1 bg-black/5 py-[30px] px-10 text-[14px]">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-[30px] h-[30px] rounded-full bg-[#4DD4C4]">
              <IconSvg type="icon-qiye" className="text-[16px] text-white" />
            </div>
            <span className="ml-2.5 text-[16px]">
              {corporation?.corporationName}
            </span>
            {corporation?.creditStatus === 1 && (
              <span className="ml-[10px] w-[64px] h-[24px] bg-[#9df2af] rounded text-[#00b42a] flex items-center justify-center text-[12px]">
                <IconSvg type="icon-chenggong2" className="mr-[3px]" />
                已认证
              </span>
            )}
          </div>
          <ul className="mt-[30px]">
            <li className="leading-6 mb-[14px]">
              团队编号：{corporation?.corporationId}
            </li>
            <li className="leading-6 mb-[14px]">
              认证类型：
              {getLabel(corporationType)}
            </li>
            <li className="leading-6 mb-[14px]">
              认证主体：{corporation?.corporationName}
            </li>
          </ul>
          <p className="leading-[21px] mb-6 text-black/45">
            *如需变更企业信息，请联系平台客服
          </p>
          {corporationType === 2 && (
            <Button
              type="primary"
              onClick={() => {
                // TODO: 跳转到企业认证页面
                router.push("/certification/corporation");
              }}
            >
              升级企业认证
            </Button>
          )}
        </div>
        <div className="flex-1 bg-black/5 px-[71px] py-10">
          {companyInfo?.departmentOverview && (
            <div
              className="relative overflow-hidden p-[30px] rounded-[10px] border border-solid before:absolute before:content-[''] before:w-1 before:h-full before:left-0 before:top-0 before:bg-[#5f5af6]"
              style={{ borderColor: "rgba(0, 0, 0, 0.12)" }}
            >
              <div className="flex items-center mb-10">
                <div className="flex items-center justify-center w-[40px] h-[40px] rounded-[10px] bg-[#5F5AF6]">
                  <IconSvg
                    type="icon-chengyuantongji"
                    className="text-[18px] text-white"
                  />
                </div>
                <span className="ml-2.5">成员统计</span>
              </div>
              <ul className="flex text-[14px]">
                <li className="mr-[95px]">
                  <div className="text-[20px] mb-2">
                    {companyInfo?.departmentOverview.departmentNumber}
                  </div>
                  <div className="text-black/45">部门</div>
                </li>
                <li>
                  <div className="text-[20px] mb-2">
                    {companyInfo?.departmentOverview.corporationUserNumber}
                  </div>
                  <div className="text-black/45">员工</div>
                </li>
              </ul>
            </div>
          )}
          {companyInfo?.corporationAsset && (
            <div className="p-[30px] rounded-[10px] border border-solid boder-[rgba(0, 0, 0, 0.12)] border-left-[#ff9800]">
              <div className="flex items-center mb-10">
                <div className="flex items-center justify-center w-[40px] h-[40px] rounded-[10px] bg-[#FF9800]">
                  <IconSvg
                    type="icon-zichantongji"
                    className="text-[18px] text-white"
                  />
                </div>
                <span className="ml-2.5">资产统计</span>
              </div>
              <ul className="flex text-[14px]">
                <li className="mr-[95px]">
                  <div className="text-[20px] mb-2">
                    {companyInfo?.corporationAsset?.accountNumber}
                  </div>
                  <div className="text-black/45">广告账号</div>
                </li>
                <li>
                  <div className="text-[20px] mb-2">
                    {companyInfo?.corporationAsset?.worksNumber}
                  </div>
                  <div className="text-black/45">创意作品</div>
                </li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default memo(ManagementInfo);
