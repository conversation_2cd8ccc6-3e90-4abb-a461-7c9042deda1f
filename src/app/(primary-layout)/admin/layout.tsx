"use client";
import { useMemo } from "react";
import { corporationMenus } from "@/utils/routes";
import AsideLayout from "@/components/aside-layout";
import "@m-design/mui/dist/mui.min.css";
import '@/style/pu.scss'

export default function Layout({ children }: { children: React.ReactNode }) {
  const menus: any = useMemo(
    () => corporationMenus.filter((item: any) => item.type === 3),
    [corporationMenus]
  );

  return <AsideLayout menus={menus}>{children}</AsideLayout>;
}
