"use client";
import React from "react";
import { observer } from "mobx-react-lite";
import { PU_VERSION } from "@/constants";
import PURender from "@/components/pu-render";
import globalStore from "@/mobx/global-store";
import config from "@/utils/dynamic-config";

const ManagementDepartment = observer(() => {
  const token = globalStore.getChannelToken();
  const userInfo = globalStore.userInfo;

  const activeCorporationId = globalStore.activeCorporationId;

  return (
    <div className="h-full">
      <PURender
        loadMui
        url={PU_VERSION.SINO_PU_MEMBER_MANAGEMENT.REMOTE}
        scope={PU_VERSION.SINO_PU_MEMBER_MANAGEMENT.SCOPE}
        module="./member-management"
        params={{
          env: config.APP_ENV,
          domainCode: "cb",
          tenantSource: "5",
          corporationId: activeCorporationId,
          userId: userInfo.channelId,
          token,
        }}
      />
    </div>
  );
});

export default ManagementDepartment;
