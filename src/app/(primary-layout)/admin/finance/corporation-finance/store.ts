import { makeAutoObservable } from 'mobx'
import { DictTabs } from './const'
import { getWalletAllBalance } from '@/services/corporation'

class MyStore {
  constructor() {
    makeAutoObservable(this)
  }

  typeTabs: any[] = DictTabs

  activeTab: any = DictTabs[0].value // 目前选中,默认第一个tab

  walletCount: any = 0 // 素材点数账户余额
  walletCNYCount: any = 0 // 人民币账户余额

  // 获取账户余额
  getMyWalletHandler = async () => {
    const { data } = await getWalletAllBalance({})
    if (data.code === 0 && Array.isArray(data.result)) {
      data.result.forEach((item: any) => {
        if (item.walletCoinCode === 'CBC') {
          this.walletCount = item.balance
        }
        if (item.walletCoinCode === 'CNY') this.walletCNYCount = item.balance
      })
    }
  }

  // 设置activeTab字段
  setActiveTab = (val: any) => {
    this.activeTab = val
  }
}

export default new MyStore()
