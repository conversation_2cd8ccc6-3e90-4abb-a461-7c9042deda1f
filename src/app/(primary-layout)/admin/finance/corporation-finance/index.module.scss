.wallet {
  :global {
    .ant-menu-submenu {
      .custom-sub-menu-icon {
        color: #fff !important;
        transform: rotate(0deg);
      }
    }
    .ant-menu-submenu-open {
      .custom-sub-menu-icon {
        color: #fff !important;
        transform: rotate(180deg);
      }
    }

    .rightContent {
      padding-right: 34px;
      .search {
        margin-left: -11px;
      }
    }
    .top {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      .left, .right {
        padding: 20px 20px 10px 20px;
        background: rgba(0, 0, 0, 0.05);
        width: calc(50% - 5px);
      }
      // height: 180px;
      .top-title {
        font-size: 16px;
      }
      .top-number {
        margin-right: 20px;
        font-size: 35px;
      }
      .top-button {
        position: relative;
        .ant-btn {
          width: 94px;
          // background: #0e3b8f !important;
          // color: #94a7cc;
        }
        .remark {
          position: absolute;
          left: -7px;
          bottom: -30px;
          font-size: 12px;
          color: #999;
          width: 200px;
        }
      }
    }
    .middle {
      font-size: 14px;

      .ant-tabs-tab-active {
        .ant-tabs-tab-btn {
          color: #0055fe;
        }
      }
      .ant-tabs-ink-bar {
        background: #0055fe;
        height: 3px;
      }
    }
    .ant-tabs-top > .ant-tabs-nav::before,
    .ant-tabs-bottom > .ant-tabs-nav::before,
    .ant-tabs-top > div > .ant-tabs-nav::before,
    .ant-tabs-bottom > div > .ant-tabs-nav::before {
      border-bottom-width: 0;
    }
  }
}
.invoice-application {
  padding: 20px 28px;
  :global {
    .title {
      text-align: center;
      font-weight: 600;
      line-height: 16px;
      font-size: 14px;
    }
    .remark {
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      color: rgba(0, 0, 0, .65);
      line-height: 16px;
    }
  }
}
