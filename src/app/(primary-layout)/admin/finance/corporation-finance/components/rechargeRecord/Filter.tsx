import React from 'react'
import { getDateRangeByDays } from '@/utils'
import { SelectChannel, SelectMoneyType, SelectDay } from '../select'

const Filter = ({ onSearch, form }: any) => {

  // 搜索
  const handleSearch = () => {
    const data = form.getFieldsValue()
    if (data.duringDate) {
      const [dates] = getDateRangeByDays(Number(data.duringDate))
      const beginDate: any = dates[0]
      const endDate: any = dates[1]
      data.tradeMinTime = beginDate.startOf('day').valueOf()
      data.tradeMaxTime = endDate.endOf('day').valueOf()
    }
    onSearch(data)
  }

  const reset = () => {
    form.resetFields()
    handleSearch()
  }

  const resetShow = () => {
    const data = form.getFieldsValue()
    return Object.values(data).some((e: any) => e)
  }

  return (
    <>
      <SelectChannel handleSearch={handleSearch} />
      <SelectMoneyType handleSearch={handleSearch} />
      <SelectDay label='充值日期' handleSearch={handleSearch} />
      {resetShow() && (
        <div className="reset" onClick={() => reset()}>
          重置
        </div>
      )}
    </>
  )
}
export default Filter
