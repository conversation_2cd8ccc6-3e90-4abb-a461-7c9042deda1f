import * as React from 'react'
import { Table } from 'antd'
import dayjs from 'dayjs'
import { MapChannel, MapMoneyType } from '../../const'
import { columnsCalc } from '../../const'

export default ({ ajaxData, ...props }: any) => {
  const columns = columnsCalc([
    {
      title: '订单ID',
      dataIndex: 'orderId',
      render: (v: string) => v,
    },
    {
      title: '客户ID',
      dataIndex: 'customerId',
      render: (v: string) => v,
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      render: (v: string) => v || '-',
    },
    {
      title: '充值金额',
      dataIndex: 'tradeAmount',
      render: (v: string) => v,
    },
    {
      title: '货币类型',
      dataIndex: 'currencyType',
      render: (v: string) => MapMoneyType[v],
    },
    {
      title: '充值渠道',
      dataIndex: 'tradeChannel',
      render: (v: string) => MapChannel[v],
    },
    {
      title: '创建时间',
      dataIndex: 'tradeTime',
      render: (v: string) => {
        return v ? dayjs(Number(v)).format('YYYY-MM-DD HH:mm:ss') : '-'
      },
    },
    {
      title: '充值备注',
      dataIndex: 'remark',
      render: (v: any) => {
        return v || '-'
      },
    },
  ])

  return <Table rowKey="orderId" columns={columns} {...props} />
}
