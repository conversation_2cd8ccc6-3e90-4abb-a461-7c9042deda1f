import React, { useMemo } from 'react'
import { Form } from 'antd'
import Select from '@/components/AntdSelect'
import { DATE_ENUM_OPTION } from '@/constants'
import { getDateRangeByDays } from '@/utils'
import { DictChannel, DictMoneyType, DictPayType } from '../../const'

// 充值渠道
export const SelectChannel = ({ handleSearch }: any) => {

  const options = useMemo(() => {
    return DictChannel
  }, [])

  return (
    <Form.Item name="tradeChannel">
      <Select
        allowClear
        dropdownMatchSelectWidth={false}
        bordered={false}
        placeholder='充值渠道'
        options={options}
        onChange={handleSearch}
      />
    </Form.Item>
  )
}

// 货币类型
export const SelectMoneyType = ({ handleSearch }: any) => {

  const options = useMemo(() => {
    return DictMoneyType
  }, [])
  return (
    <Form.Item name="walletCoinCode">
      <Select
        allowClear
        dropdownMatchSelectWidth={false}
        bordered={false}
        placeholder='货币类型'
        options={options}
        onChange={handleSearch}
      />
    </Form.Item>
  )
}

// 消费类型
export const SelectPayType = ({ handleSearch }: any) => {

  const options = useMemo(() => {
    return DictPayType
  }, [])

  return (
    <Form.Item name="consumeType">
      <Select
        allowClear
        dropdownMatchSelectWidth={false}
        bordered={false}
        placeholder='消费类型'
        options={options}
        onChange={handleSearch}
      />
    </Form.Item>
  )
}

// 时间选择
export const SelectDay = ({ label, handleSearch }: any) => {

  const options = useMemo(() => {
    return DATE_ENUM_OPTION
  }, [])
  return (
    <Form.Item name="duringDate">
      <Select
        allowClear
        dropdownMatchSelectWidth={false}
        bordered={false}
        placeholder={label}
        options={options}
        onChange={(v: any) => {
          if (v) {
            const [dates] = getDateRangeByDays(Number(v))
            const beginDate: any = dates[0]
            const endDate: any = dates[1]
            handleSearch({
              tradeMinTime: beginDate.startOf('day').valueOf(),
              tradeMaxTime: endDate.endOf('day').valueOf(),
            })
          } else {
            handleSearch({
              tradeMinTime: undefined,
              tradeMaxTime: undefined,
            })
          }
        }}
      />
    </Form.Item>
  )
}
