/*
 * @Author: miroku.yang
 * @Date: 2023-12-30 12:13:54
 * @LastEditors: 
 * @LastEditTime: 2024-04-27 10:05:18
 * @Description: 
 */
import * as React from 'react'
import dayjs from 'dayjs';
import { Table } from 'antd'
import useMyPreview from '@/components/useMyPreview'
import { columnsCalc, MapMoneyType } from '../../const'

export default ({ ajaxData, ...props }: any) => {
  const { show, render } = useMyPreview()

  const columns = columnsCalc([
    {
      title: '订单ID',
      dataIndex: 'orderId',
      render: (v: string) => v,
    },
    {
      title: '客户ID',
      dataIndex: 'customerId',
      render: (v: string) => v,
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      render: (v: string) => v || '-',
    },
    {
      title: '消费人',
      dataIndex: 'operatorName',
      render: (v: string) => v || '-',
    },
    {
      title: '消费金额',
      dataIndex: 'tradeAmount',
      render: (v: string) => v,
    },
    {
      title: '货币类型',
      dataIndex: 'currencyType',
      render: (v: string) => MapMoneyType[v],
    },
    {
      title: '消费类型',
      dataIndex: 'consumeTypeDesc',
      render: (v: string) => v,
    },
    {
      title: '付费作品ID',
      dataIndex: 'workDetailList',
      render: (v: any[]) => {
        return (v || []).map((e: any) =>
          e.resizeImageUrl ? (
            <span key={e.id} style={{ marginRight: 5 }}>
              <a
                onClick={() => {
                  show(e)
                }}
              >
                {e.id || '-'}
              </a>
            </span>
          ) : (
            <span key={e.id} style={{ marginRight: 5 }}>{e.id || '-'}</span>
          ),
        )
      },
    },
    {
      title: '消费时间',
      dataIndex: 'tradeTime',
      render: (v: string) => {
        return v ? dayjs(Number(v)).format('YYYY-MM-DD HH:mm:ss') : '-'
      },
    },
  ])


  return (
    <>
      <Table rowKey="orderId" columns={columns} {...props} />
      {render()}
    </>
  )
}
