'use client'
import React, { useEffect, useRef, useState } from 'react'
import { But<PERSON>, Tabs, Dropdown } from 'antd'
import { observer, useLocalObservable } from 'mobx-react-lite'
import { getList } from '@/services/corporation'
import { DictTabs } from './const'
import MyComplexPage from '@/components/MyComplexPage'
import ConsumeRecordList from './components/consumeRecord/List'
import ConsumeRecordFilter from './components/consumeRecord/Filter'
import RechargeRecordList from './components/rechargeRecord/List'
import RechargeRecordFilter from './components/rechargeRecord/Filter'
import { AuthorizedButton } from '@/components/Authorized'
import myStore from './store'
import styles from './index.module.scss'

const { TabPane } = Tabs

const Wallet = (props: any) => {
  const mobx = useLocalObservable(() => myStore)
  const refComplexPage = useRef<any>()

  const [open, setOpen] = useState(false)

  useEffect(() => {
    // 查询余额
    mobx.getMyWalletHandler()
    return () => {
      mobx.setActiveTab('RECHARGE')
    }
  }, [])

  return (
    <div className={styles.wallet}>
      <div className="rightContent">
        <div className="top">
          <div className="left">
            <div className="top-title">人民币账户余额</div>
            <div
              style={{ display: 'flex', justifyContent: 'center', marginTop: 20, marginBottom: 30 }}
            >
              <div className="top-number">
                <span style={{ marginRight: 10 }}>¥</span>
                {mobx.walletCNYCount}
              </div>
              <div className="top-button">
                <Dropdown
                  placement="bottom"
                  overlayStyle={{
                    minWidth: 176,
                    height: 248,
                    background: 'white',
                    borderRadius: 4,
                    border: '1px solid #e8e8e8',
                  }}
                  open={open}
                  onOpenChange={open => {
                    setOpen(open)
                  }}
                  dropdownRender={() => {
                    return (
                      <div className={styles['invoice-application']}>
                        <div className="title">Creative Booster</div>
                        <div className="title">专属客服</div>
                        <img
                          style={{ marginTop: 12, marginBottom: 12 }}
                          width={120}
                          height={120}
                          src="//static.creativebooster.com/ad-web/pay.png"
                        />
                        <div className="remark">当前仅支持对公打款</div>
                        <div className="remark">请添加客服微信</div>
                      </div>
                    )
                  }}
                >
                  <div style={{ display: 'inline-block' }}>
                    <Button type="primary" disabled>
                      <span className="ml-10 mr-10">充值</span>
                    </Button>
                  </div>
                </Dropdown>
              </div>
            </div>
          </div>
          <div className="right">
            <div className="top-title">素材点数账户余额</div>
            <div
              style={{ display: 'flex', justifyContent: 'center', marginTop: 20, marginBottom: 30 }}
            >
              <div className="top-number">{mobx.walletCount}</div>
              <div className="top-button">
                <AuthorizedButton code="BUTTON_MANAGEMENT_CORPORATION_FINANCE_ADD_CREDIT">
                  <Button type="primary">充值</Button>
                </AuthorizedButton>
                <span className="remark">请联系客服进行充值</span>
              </div>
            </div>
          </div>
        </div>
        <div className="middle">
          <Tabs
            onChange={(e: any) => {
              mobx.setActiveTab(e)
            }}
            activeKey={mobx.activeTab}
          >
            {mobx.typeTabs.map((item: any) => (
              <TabPane tab={item.label} key={item.value} />
            ))}
          </Tabs>
          {mobx.activeTab === DictTabs[0].value ? (
            <>
              <MyComplexPage
                key="1"
                formItemGrid={false}
                ref={refComplexPage}
                renderSearch={({ onSearch, form }: any) => (
                  <RechargeRecordFilter form={form} onSearch={onSearch} />
                )}
                query={getList}
                params={{ tradeType: mobx.activeTab }}
                listDom={({ listProps, ajaxData }: any) => (
                  <RechargeRecordList {...listProps} ajaxData={ajaxData} />
                )}
              />
            </>
          ) : (
            <>
              <MyComplexPage
                key="2"
                ref={refComplexPage}
                formItemGrid={false}
                renderSearch={({ onSearch, form }: any) => (
                  <ConsumeRecordFilter form={form} onSearch={onSearch} />
                )}
                query={getList}
                params={{ tradeType: mobx.activeTab }}
                listDom={({ listProps, ajaxData }: any) => (
                  <ConsumeRecordList {...listProps} ajaxData={ajaxData} />
                )}
              />
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default observer(Wallet)
