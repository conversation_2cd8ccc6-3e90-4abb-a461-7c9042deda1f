// import IconSvg from '@/components/IconSvg'

// 字典转map 回显用
const distToMap = (arr: any) => {
  const obj: any = {}
  arr.forEach((e: any) => {
    obj[e.value] = e.label
  })
  return obj
}
// 给column参数加宽
export const columnsCalc = (columns: any) => {
  // 本页宽度数据
  const widthList: Record<string, string> = {
    orderId: '15%',
    customerId: '10%',
    customerName: '10%',
    tradeAmount: '10',
    currencyType: '10%',
    tradeChannel: '10%',
    tradeTime: '15%',
    remark: '20%',
  }
  const newConlumn = columns.map((e: any) => {
    e.width = widthList[e.dataIndex]
    return e
  })
  return newConlumn
}

// 交易类型
export const DictTabs = [
  {
    label: '充值记录',
    translateKey: 'finance-record:t1',
    value: 'RECHARGE',
  },
  {
    label: '消费记录',
    translateKey: 'finance-record:t2',
    value: 'CONSUME',
  },
]
// 交易渠道
export const DictChannel = [
  {
    label: 'CM后台',
    translateKey: 'finance-record:t49',
    value: 'CM',
  },
]
// 交易渠道  回显用
export const MapChannel = distToMap(DictChannel)
// 交易状态
export const status = [
  {
    label: '交易中',
    translateKey: 'finance-record:t5',
    value: 0,
  },
  {
    label: '交易成功',
    translateKey: 'finance-record:t6',
    value: 1,
  },
  {
    label: '交易失败',
    translateKey: 'finance-record:t7',
    value: 0,
  },
]
// 交易币种
export const DictMoneyType = [
  {
    label: '素材点数',
    translateKey: 'finance-record:t8',
    value: 'CBC',
  },
  // {
  //   label: '人民币',
  //   translateKey: 't9',
  //   value: 'CNY',
  // },
]
// 交易币种 回显用
export const MapMoneyType = distToMap(DictMoneyType)

// 消费类型
export const DictPayType = [
  {
    label: '下载',
    translateKey: 'finance-record:t10',
    value: 'DOWNLOAD',
  },
  {
    label: '分发',
    translateKey: 'finance-record:t11',
    value: 'DISTRIBUTE',
  },
  {
    label: 'AI模特原图解析',
    translateKey: 'finance-record:t16',
    value: 'AI_GENERATOR_PREPARE',
  },
  {
    label: 'AI模特图片生成',
    translateKey: 'finance-record:t17',
    value: 'AI_GENERATOR_CREATE',
  },
  {
    label: 'AI智能抠图',
    translateKey: 'finance-record:t18',
    value: 'AI_GENERATOR_CUTOUT',
  },
  {
    label: 'AI背景融合生成',
    translateKey: 'finance-record:t19',
    value: 'AI_GENERATOR_BACKGROUND',
  },
]
// 消费类型
export const MapPayType = distToMap(DictPayType)
