"use client";
import React from "react";
import { observer } from "mobx-react-lite";
import { PU_VERSION } from "@/constants";
import PURender from "@/components/pu-render";
import globalStore from "@/mobx/global-store";
import config from "@/utils/dynamic-config";

function IndividualDetail() {
  const token = globalStore.getChannelToken();

  return (
    <div style={{ height: "100%" }}>
      <div style={{ maxWidth: 1200, margin: "30px auto" }}>
        <PURender
          loadMui
          url={PU_VERSION.SINO_MEDIA_PU_MF.REMOTE}
          scope={PU_VERSION.SINO_MEDIA_PU_MF.SCOPE}
          module="./authentication/individual-detail"
          params={{
            env: config?.APP_ENV,
            source: "cb",
            noShowContainer: true,
            token
          }}
        />
      </div>
    </div>
  );
}
export default observer(IndividualDetail);