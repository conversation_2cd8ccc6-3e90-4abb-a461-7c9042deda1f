"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { observer } from "mobx-react-lite";
import { PU_VERSION, GLOBAL_REDIRECT_PATH } from "@/constants";
import PURender from "@/components/pu-render";
import globalStore from "@/mobx/global-store";
import config from "@/utils/dynamic-config";

function Individual() {
  const router = useRouter();
  const token = globalStore.getChannelToken();

  return (
    <div style={{ height: "100%" }}>
      <div style={{ maxWidth: 1200, margin: "30px auto" }}>
        <PURender
          loadMui
          url={PU_VERSION.SINO_MEDIA_PU_MF.REMOTE}
          scope={PU_VERSION.SINO_MEDIA_PU_MF.SCOPE}
          module="./authentication/individual"
          params={{
            env: config?.APP_ENV,
            source: "cb",
            topDomain: 'ads.meetsocial.cn',
            noShowContainer: true,
            token,
            // 注册成功后点击我知道了
            onSuccessModalOk: async (corporationId: any) => {
              // 点击我知道了后切换当前绑定主体的账户
              if (corporationId) {
                await globalStore.changeCorporation({ corporationId });
                router.push(GLOBAL_REDIRECT_PATH);
              } else {
                await globalStore.init();
                // 审核中，没有主体ID,跳转认证详情
                router.push("/certification/individual/detail");
              }
            },
          }}
        />
      </div>
    </div>
  );
}
export default observer(Individual);