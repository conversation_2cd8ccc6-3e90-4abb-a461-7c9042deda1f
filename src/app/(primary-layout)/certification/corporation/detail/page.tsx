"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { observer } from "mobx-react-lite";
import { PU_VERSION } from "@/constants";
import PURender from "@/components/pu-render";
import globalStore from "@/mobx/global-store";
import config from "@/utils/dynamic-config";

function CorporationDetail() {
  const router = useRouter();
  const token = globalStore.getChannelToken();

  return (
    <div style={{ height: "100%" }}>
      <div style={{ maxWidth: 1200, margin: "30px auto" }}>
        <PURender
          loadMui
          url={PU_VERSION.SINO_MEDIA_PU_MF.REMOTE}
          scope={PU_VERSION.SINO_MEDIA_PU_MF.SCOPE}
          module="./authentication/corporation-detail"
          params={{
            env: config?.APP_ENV,
            source: "cb",
            noShowContainer: true,
            token,
            // 注册成功后点击我知道了
            onRecertification: () => {
              // 重新认证->跳转企业主体认证
              router.push('/certification/corporation')
            },
          }}
        />
      </div>
    </div>
  );
}
export default observer(CorporationDetail);