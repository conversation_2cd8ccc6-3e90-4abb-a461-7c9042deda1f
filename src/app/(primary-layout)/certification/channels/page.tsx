'use client'
// 认证选择
import React from 'react'
import { useRouter } from "next/navigation";
import { observer } from "mobx-react-lite";
import { PU_VERSION } from "@/constants";
import PURender from "@/components/pu-render";
import globalStore from "@/mobx/global-store";
import config from "@/utils/dynamic-config";


const Channels = observer(() => {
  const router = useRouter()
  const token = globalStore.getChannelToken()

  return (
    <div style={{ height: '100%' }}>
      <PURender
        loadMui
        url={PU_VERSION.SINO_MEDIA_PU_MF.REMOTE}
        scope={PU_VERSION.SINO_MEDIA_PU_MF.SCOPE}
        module="./authentication/center"
        params={{
          env: config?.APP_ENV,
          source: 'cb',
          noShowContainer: true,
          token,
          onSelect: (type: string) => {
            if (type === 'individual') {
              /* 跳转显示个人认证组件 */
              router.push('/certification/individual')
            } else if (type === 'corporation') {
              /* 跳转显示企业认证组件 */
              router.push('/certification/corporation')
            }
          },
        }}
      />
    </div>
  )
})
export default Channels;