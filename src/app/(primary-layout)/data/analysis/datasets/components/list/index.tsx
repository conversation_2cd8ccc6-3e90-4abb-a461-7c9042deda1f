/**
 * 表格
 */
import React, { useMemo, useRef, useEffect, useState } from "react";
import { Table } from "antd";
import SourceMaterial from "@/app/(primary-layout)/data/analysis/components/SourceMaterial";
import styles from "./index.module.scss";

const List = ({ columns, pagination, summaryData, averageData,showSummaryRow, showAverageRow, ...props }: any) => {
  const [height, setHeight] = useState(500);
  const tableWrapperRef = useRef<any>(null);

  useEffect(() => {
    const tableWrapper: any = tableWrapperRef.current;
    if (tableWrapper) {
      const tableWrapperHeight = tableWrapper.offsetHeight;
      // 减去表头和分页器高度
      setHeight(tableWrapperHeight - 114);
    }
  }, []);

  /**
   * 生成列
   */
  const dynamicColumns = useMemo(() => {
    const result: any = [];
    for (const item of columns) {
      const isFilename = item.code === "self_file_name_modified";
      const col = {
        ...item,
        title: item.name,
        ellipsis: true,
        dataIndex: item.code,
        width: 200,
      };
      if (!isFilename) {
        result.push(col);
      } else {
        result.push({ ...col, fixed: 'left' }, {
          title: "素材",
          dataIndex: "material",
          key: "material",
          ellipsis: true,
          width: 200,
          fixed: 'left',
          render: (_: string, record: any) => {
            const materialUrlInfoList = record.materialUrlInfoList || [];
            materialUrlInfoList?.forEach?.((item: any) => {
              item.customerProductName = record["self_file_name_modified"];
              item.fileInfo = {
                previewInfo: {
                  resizeImageUrl: item.coverUrl,
                },
                url: item.url,
              };
            });
            return (
              <SourceMaterial
                materialList={materialUrlInfoList}
                showCard={2}
                modalTitle="素材"
              />
            );
          },
        });
      }
    }
    return result;
  }, [columns]);

  const calcScrollX = useMemo(() => {
    return dynamicColumns.reduce((acc: number, cur: any) => {
      return acc + cur.width;
    }, 0);
  }, [dynamicColumns]);

  const summaryRow = () => {
    return (
      <Table.Summary fixed="top">
        {showSummaryRow && <Table.Summary.Row className={styles["ant-table-summary-row"]}>
          {dynamicColumns.map((column: any, index: number) => {
            const value = summaryData[column.dataIndex];
            return (
              <Table.Summary.Cell key={column.dataIndex} index={index}>
                {column.dataIndex == "self_file_name_modified" ? '合计' : value || '-'}
              </Table.Summary.Cell>
            );
          })}
        </Table.Summary.Row>}
        {showAverageRow && <Table.Summary.Row className={styles["ant-table-summary-row"]}>
          {dynamicColumns.map((column: any, index: number) => {
            const value = averageData[column.dataIndex];
            return (
              <Table.Summary.Cell key={column.dataIndex} index={index}>
                {column.dataIndex == "self_file_name_modified" ? '平均' : value || '-'}
              </Table.Summary.Cell>
            );
          })}
        </Table.Summary.Row>}
      </Table.Summary>
    );
  };
  const summaryRowHeight = useMemo(()=>{
    return (showSummaryRow ? 40 : 0) + (showAverageRow ? 40 : 0);
  },[showSummaryRow, showAverageRow]);

  return (
    <div
      className="w-full h-full overflow-hidden relative pb-5"
      ref={tableWrapperRef}
    >
      <Table
        columns={dynamicColumns}
        {...props}
        pagination={{
          ...pagination,
          showTotal: (total) => `共${total}条数据`,
        }}
        scroll={{
          x: calcScrollX,
          y: height - summaryRowHeight,
        }}
        bordered
        rowKey="rowKey"
        summary={showSummaryRow||showAverageRow ? summaryRow : undefined}
      />
    </div>
  );
};

export default List;
