/**
 * 动态列设置
 */
import * as React from "react";
import { message } from "antd";
import { useModalWrap } from "@/components";
import ColumnSetting from "./column-setting";
import { validateCheckedValues } from "./utils";
import { dynmiacColumnSet } from "./services";

// 'self_file_name_modified'
export default function useDynamicColumnSetting({
  // 视图页面传参
  editId,
  editType,
  partVisible = true,
  fixedColumn,
  onSaveCallback,
}: any) {
  /**
   * 提交
   * @param params
   * @returns
   */
  const handleOk = async (params: any) => {
    try {
      const { data = {} } = await dynmiacColumnSet(params);
      if (data?.code === 0) {
        message.success("操作成功");
        onSaveCallback?.();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  };

  const { showModal, render } = useModalWrap({
    modalProps: {
      title: "自定义列",
      width: 774,
      styles: {
        header: {
          padding: "16px 24px",
          marginBottom: 0,
        },
        body: {
          padding: "0 24px",
        },
        footer: {
          padding: "16px 24px",
        },
      },
      onOk: (values: any) => {
        const customColumnEditDTOList = values?.customColumnEditDTOList;
        if (
          !Array.isArray(customColumnEditDTOList) ||
          customColumnEditDTOList.length === 0
        ) {
          message.warning("维度和指标至少选择一项");
          return false;
        }
        const validate: any = validateCheckedValues(
          values.customColumnEditDTOList
        );
        if (!validate?.isOk) {
          message.warning(`${validate?.name}至少选择一项`);
          return false;
        }
        return handleOk(values);
      },
    },
    children: ({ showParams, form }: any) => {
      const { id, type } = showParams || {};
      return (
        <ColumnSetting
          partVisible={partVisible}
          fixedColumn={fixedColumn}
          sourceId={id}
          sourceType={type}
          form={form}
          onSaveCallback={onSaveCallback}
          editId={editId}
          editType={editType}
        />
      );
    },
  });

  return {
    showColumnSetting: showModal,
    renderColumnSetting: render,
  };
}
