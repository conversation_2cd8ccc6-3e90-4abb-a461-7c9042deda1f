/**
 * 获取动态列hook
 */
import { useState, useEffect } from "react";
import { dynamicColumnInfo } from "../../services";

const useDynamicColumns = (params: any) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [result, setResult] = useState<any>({});

  async function fetchDynamicColumns(params: any) {
    setLoading(true);
    try {
      const { data = {} } = await dynamicColumnInfo(params);
      if (data?.code === 0) {
        const res = data?.result || {};
        setResult(res);
        return res;
      }
      return result;
    } finally {
      setLoading(false);
      return result;
    }
  }

  function format(result: any) {
    const {
      basisDimensionInfoList,
      basisIndicatorInfoList,
      indicatorInfoList,
    } = result || {};
    let options: any = [];
    if (Array.isArray(basisDimensionInfoList)) {
      const dateOption = basisDimensionInfoList.filter(
        (item: any) => item.dataType === "date"
      );
      options = options.concat(dateOption);
    }
    if (Array.isArray(basisIndicatorInfoList)) {
      options = options.concat(basisIndicatorInfoList);
    }
    if (Array.isArray(indicatorInfoList)) {
      options = options.concat(indicatorInfoList);
    }
    return options.map((item: any) => ({
      ...item,
      label: item.name,
      value: item.code,
      key: item.code,
    }));
  }

  useEffect(() => {
    async function init(params: any) {
      fetchDynamicColumns(params);
    }
    const { id, type } = params || {};
    if (id && type) {
      init({ id, type });
    }
  }, []);

  return {
    loading,
    result,
    format,
    fetchDynamicColumns,
  };
};

export default useDynamicColumns;
