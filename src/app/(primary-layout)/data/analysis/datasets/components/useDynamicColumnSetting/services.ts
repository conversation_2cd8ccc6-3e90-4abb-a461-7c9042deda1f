import requestHelper from '@/utils/requestHelper'

/**
 * 动态列设置
 * @param data 
 * @returns 
 */
export function dynmiacColumnSet(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/custom/column/info/edit', data)
}

/**
 * 删除计算字段
 * @param data 
 * @returns 
 */
export function indicatorRemove(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/calculate/indicator/delete', data)
}

/**
 * 校验赋值字段是否可以删除
 * @param data 
 * @returns 
 */
export function validateIndRemove(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/calculate/indicator/relate/group/flag', data)
}

/**
 * 赋值字段删除
 * @param data 
 * @returns 
 */
export function rangeRemove(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/group/indicator/delete', data)
}