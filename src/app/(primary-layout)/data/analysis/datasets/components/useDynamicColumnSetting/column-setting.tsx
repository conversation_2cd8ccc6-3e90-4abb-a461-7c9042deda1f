/**
 * 动态设置列
 */

import { useEffect, useMemo, useState } from "react";
import {
  Input,
  Checkbox,
  Tooltip,
  Form,
  Spin,
  Empty,
  Popconfirm,
  message,
  Modal,
} from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { ScrollArea } from "@/components/ui/scroll-area";
import { IconSvg } from "@/components";
import DragDropContext from "./moveable-area/drap-drop-context";
import List from "./moveable-area/list";
import { indicatorRemove, rangeRemove, validateIndRemove } from "./services";
import { dynamicColumnInfo } from "../../services";
import {
  mapDataSource,
  formatColumns,
  filterNewColumn,
  formatSubData,
} from "./utils";
import useCalcIndicatorModal from "../useCalcIndicatorModal";
import useAssignmentField from "../useAssignmentField";

const ColumnSetting = ({
  partVisible = true,
  fixedColumn,
  sourceId,
  sourceType,
  form,
  onSaveCallback,
  editId,
  editType,
}: any) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [originDataSource, setOriginDataSource] = useState<any>({});
  const [dataSource, setDataSource] = useState<any>([]);
  const [columns, setColumns] = useState<any>({
    partList: [],
    unPartList: [],
  });

  /**
   * 删除、拖拽排序更新列表
   * @param newColumns
   */
  const handleChange = (newColumns: any) => {
    setColumns(newColumns);
    form.setFieldsValue({
      customColumnEditDTOList: [
        ...newColumns.partList,
        ...newColumns.unPartList,
      ],
    });
  };

  async function fetchInfo(params: any) {
    try {
      const { data = {} } = await dynamicColumnInfo(params);
      if (data?.code === 0) {
        const result = data?.result || {};
        setDataSource(mapDataSource(result));
        setOriginDataSource(result);
        let selectedInfoList = result?.selectedInfoList;
        if (Array.isArray(selectedInfoList) && fixedColumn) {
          // 存在固定列
          selectedInfoList = selectedInfoList.map((item: any) => {
            return {
              ...item,
              fixed: fixedColumn === item.code,
            };
          });
        }
        if (Array.isArray(selectedInfoList) && selectedInfoList.length > 0) {
          form.setFieldsValue({
            customColumnEditDTOList: selectedInfoList,
            sourceId,
            sourceType,
          });
          setColumns(formatColumns(selectedInfoList));
        } else {
          form.setFieldsValue({
            sourceId,
            sourceType,
            customColumnEditDTOList: [],
          });
          setColumns({
            partList: [],
            unPartList: [],
          });
        }
      }
    } finally {
      setLoading(false);
    }
  }

  /**
   * 编辑、删除后刷
   */
  async function refreshInfo(params: any) {
    try {
      const { data = {} } = await dynamicColumnInfo(params);
      if (data?.code === 0) {
        const result = data?.result || {};
        setDataSource(mapDataSource(result));
        setOriginDataSource(result);
        const allList = [
          ...(result?.basisDimensionInfoList || []),
          ...(result?.basisIndicatorInfoList || []),
          ...(result?.partitionDimensionInfoList || []),
          ...(result?.indicatorInfoList || []),
        ];
        // 更新columns
        const { partList, unPartList } = columns;
        handleChange({
          partList: filterNewColumn(partList, allList),
          unPartList: filterNewColumn(unPartList, allList),
        });
      }
    } catch (error) {
      console.log(error);
    }
  }

  useEffect(() => {
    async function init(params: any) {
      await fetchInfo(params);
    }
    if (sourceId && sourceType) {
      init({ id: sourceId, type: sourceType });
    } else {
      setLoading(false);
    }
  }, []);

  const { showCalcIndicatior, renderCalcIndicatior } = useCalcIndicatorModal({
    onSaveCallback: async () => {
      await refreshInfo({ id: sourceId, type: sourceType });
      onSaveCallback?.();
    },
  });

  const { showAssignmentField, renderAssignmentField } = useAssignmentField({
    onSaveCallback: async () => {
      await refreshInfo({ id: sourceId, type: sourceType });
      onSaveCallback?.({ showUpdateEnumList: true });
    },
  });

  /**
   * 本地搜索
   * @param k
   * @returns
   */
  const handleSearch = (k: string) => {
    if (!k) {
      setDataSource(mapDataSource(originDataSource));
      return;
    }
    const newDataSource = Object.keys(originDataSource).reduce((acc, cur) => {
      const list = originDataSource[cur];
      if (Array.isArray(list) && list.length > 0) {
        const newList = list.filter((item: any) => {
          return item?.name?.includes?.(k);
        });
        if (newList.length > 0) {
          return {
            ...acc,
            [cur]: newList,
          };
        }
        return acc;
      }
      return acc;
    }, {});
    setDataSource(mapDataSource(newDataSource));
  };

  /**
   * 删除
   * @param listId
   * @param currentItem
   */
  const handleRemove = (listId: string, currentItem: Record<string, any>) => {
    const newList = [...(columns[listId] || [])];
    const idx = newList.findIndex((item) => item.code === currentItem.code);
    if (idx > -1) {
      newList.splice(idx, 1);
      handleChange({
        ...columns,
        [listId]: newList.map((i: any, index: number) => ({ ...i, index })),
      });
    }
  };

  /**
   * 勾选、取消勾选事件
   * @param e
   * @param item
   */
  const handleCheckboxChange = (e: any, item: any) => {
    if (e.target.checked) {
      // 选中
      const { partList, unPartList } = columns;
      if (partVisible && ["BASIS_DIMENSION", "RANGE"].includes(item.type)) {
        handleChange({
          ...columns,
          partList: partList
            .concat({ ...item, selected: true, partitionFlag: true })
            .map((i: any, index: number) => ({ ...i, index })),
        });
        return;
      }
      handleChange({
        ...columns,
        unPartList: unPartList
          .concat({ ...item, selected: true })
          .map((i: any, index: number) => ({ ...i, index })),
      });
      return;
    }
    // 取消勾选
    const { partList, unPartList } = columns;
    const allList = [...partList, ...unPartList];
    const idx = allList.findIndex((i: any) => i.code === item.code);
    if (idx > -1) {
      const { partitionFlag } = allList[idx] || {};
      if (partitionFlag) {
        // 已分区
        handleChange({
          partList: partList
            .filter((i: any) => i.code !== item.code)
            .map((i: any, index: number) => ({ ...i, index })),
          unPartList,
        });
      } else {
        // 未分区
        handleChange({
          unPartList: unPartList
            .filter((i: any) => i.code !== item.code)
            .map((i: any, index: number) => ({ ...i, index })),
          partList,
        });
      }
    }
  };

  /**
   * 全部清空
   */
  const handleClear = () => {
    const { partList, unPartList } = columns;
    handleChange({
      partList: partList.filter((item: any) => item.fixed),
      unPartList: unPartList.filter((item: any) => item.fixed),
    });
  };

  /**
   * 计算指标编辑
   * @param e
   * @param item
   */
  const handleIndicatorEdit = (e: any, item: any) => {
    // 编辑事件
    e.stopPropagation();
    e.preventDefault();
    showCalcIndicatior({
      id: item.id,
      sourceId: editId || sourceId,
      sourceType: editType || sourceType,
    });
  };

  const afterRemove = async (params: any, code: string) => {
    try {
      const { data = {} } = await dynamicColumnInfo(params);
      if (data?.code === 0) {
        const result = data?.result || {};
        setDataSource(mapDataSource(result));
        setOriginDataSource(result);
        // 更新columns
        const combineList = [...columns.partList, ...columns.unPartList];
        const idx = combineList.findIndex((e: any) => e.code === code);
        if (idx > -1) {
          // 删除
          combineList.splice(idx, 1);
          const newColuns = formatColumns(combineList);
          handleChange(newColuns);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleIndRemoveConfirm = async (item: any) => {
    const { data = {} } = await indicatorRemove({
      sourceId,
      sourceType,
      id: item.id,
    });
    if (data?.code === 0) {
      message.success("删除成功");
      await afterRemove({ id: sourceId, type: sourceType }, item.code);
      onSaveCallback?.();
    }
  };

  /**
   * 计算指标删除
   * @param e
   * @param item
   */
  const handleIndicatorRemove = async (e: any, item: any) => {
    // 删除事件
    e.stopPropagation();
    e.preventDefault();
    const params = {
      sourceId,
      sourceType,
      id: item.id,
    };
    const { data = {} } = await validateIndRemove(params);
    if (data?.code === 0) {
      if (data?.result) {
        // 被赋值字段依据，需弹框确认
        Modal.confirm({
          title: "温馨提示",
          icon: null,
          width: 480,
          content: (
            <div className="p-2">
              当前计算字段已被赋值，若删除该计算字段，也会同步删除相关的赋值字段。
            </div>
          ),
          centered: true,
          onOk: () => handleIndRemoveConfirm({ ...params, code: item.code }),
        });
        return;
      }
      await handleIndRemoveConfirm({ ...params, code: item.code });
    }
  };

  /**
   * 赋值字段编辑
   * @param e
   * @param item
   */
  const handleRangeEdit = (e: any, item: any) => {
    // 编辑事件
    e.stopPropagation();
    e.preventDefault();
    showAssignmentField({
      id: sourceId,
      type: sourceType,
      code: item.code,
    });
  };

  /**
   * 赋值字段删除
   * @param e
   * @param item
   */
  const handleRangeRemove = async (e: any, item: any) => {
    // 删除事件
    e.stopPropagation();
    e.preventDefault();
    const { data = {} } = await rangeRemove({
      id: sourceId,
      type: sourceType,
      code: item.code,
    });
    if (data?.code === 0) {
      message.success("删除成功");
      await afterRemove({ id: sourceId, type: sourceType }, item.code);
      onSaveCallback?.();
    }
  };

  const renderOpt = (item: any) => {
    const type = item.type;
    if (type === "CALCULATE_INDICATOR") {
      // 计算字段
      return (
        <div className="hidden absolute h-[32px] top-0 right-0 bg-[#fff] group-hover:block z-10">
          <div className="h-full flex items-center gap-2 px-2">
            <span
              className="w-6 h-6 flex items-center justify-center cursor-pointer hover:bg-[#EDEFF2] rounded-[4px]"
              onClick={(e) => handleIndicatorEdit(e, item)}
            >
              <IconSvg
                type="icon-bianji"
                // className="text-[14px] text-[#4E5969]"
              />
            </span>
            <Popconfirm
              placement="top"
              title="确定要删除吗？"
              okText="确定"
              cancelText="取消"
              onConfirm={(e) => {
                handleIndicatorRemove(e, item);
              }}
            >
              <span
                className="w-6 h-6 flex items-center justify-center cursor-pointer hover:bg-[#EDEFF2] rounded-[4px]"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
              >
                <IconSvg
                  type="icon-shanchu"
                  // className="text-[14px] text-[#4E5969]"
                />
              </span>
            </Popconfirm>
          </div>
        </div>
      );
    }
    if (type === "RANGE") {
      // 赋值字段
      return (
        <div className="hidden absolute h-[32px] top-0 right-0 bg-[#fff] group-hover:block z-10">
          <div className="h-full flex items-center gap-2 px-2">
            <span
              className="w-6 h-6 flex items-center justify-center cursor-pointer hover:bg-[#EDEFF2] rounded-[4px]"
              onClick={(e) => handleRangeEdit(e, item)}
            >
              <IconSvg
                type="icon-bianji"
                // className="text-[14px] text-[#4E5969]"
              />
            </span>
            <Popconfirm
              placement="top"
              title="确定要删除吗？"
              okText="确定"
              cancelText="取消"
              onConfirm={(e) => {
                handleRangeRemove(e, item);
              }}
            >
              <span
                className="w-6 h-6 flex items-center justify-center cursor-pointer hover:bg-[#EDEFF2] rounded-[4px]"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
              >
                <IconSvg
                  type="icon-shanchu"
                  // className="text-[14px] text-[#4E5969]"
                />
              </span>
            </Popconfirm>
          </div>
        </div>
      );
    }
    return null;
  };

  const renderChild = (item: any) => {
    const subChildren = item.children;
    return (
      <div key={item.key} className="mb-3 last:mb-0">
        <div className="text-[12px] text-[#4E5969] font-medium">
          {item.name}
        </div>
        <div className="flex items-center flex-wrap gap-1">
          {subChildren.map((subChild: any) => {
            const code = subChild.code;
            return (
              <div
                key={code}
                className="w-[calc((100%-4px)/2)] overflow-hidden"
              >
                <Checkbox
                  value={code}
                  checked={checkedValues.includes(code)}
                  disabled={fixedColumn === code}
                  onChange={(e: any) => handleCheckboxChange(e, subChild)}
                >
                  <Tooltip placement="topLeft" title={subChild.name}>
                    <div className="group w-[180px] relative">
                      <div className="w-full h-[32px] leading-[32px] single-line-ellipsis">
                        {subChild.name}
                      </div>
                      {renderOpt(subChild)}
                    </div>
                  </Tooltip>
                </Checkbox>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderContainer = () => {
    if (loading) {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <Spin spinning />
        </div>
      );
    }
    const { partList, unPartList } = columns;
    const selectedItemsLength = parseInt(
      partList.length + unPartList.length,
      10
    );
    return (
      <>
        <div className="w-[478px] h-full rounded-[4px] border border-solid border-[#F2F3F5]">
          <ScrollArea className="w-full h-full px-3 py-4 overflow-auto">
            <div className="w-full">
              {Array.isArray(dataSource) && dataSource.length > 0 ? (
                <>
                  {dataSource.map((item: any) => {
                    const children = item?.children;
                    return (
                      <div
                        key={item.key}
                        className="mb-4 last:mb-0 font-pingfang"
                      >
                        <div className="flex items-center w-full h-[36px] bg-[#F7F8FA] px-4">
                          <span className="font-medium text-[14px]">
                            {item.name}
                          </span>
                          {item.required && (
                            <span className="ml-[5px] text-[14px] text-[#4E5969]">
                              （至少选择一项）
                            </span>
                          )}
                        </div>
                        <div className="px-3 pt-3">
                          {children?.map((child: any) => {
                            if (child.key === "basisDimensionInfoList") {
                              const subChildren = formatSubData(
                                child.children || []
                              );
                              return (
                                <div key={child.key} className="mb-3 last:mb-0">
                                  {subChildren.map((subChild: any) =>
                                    renderChild(subChild)
                                  )}
                                </div>
                              );
                            }
                            return renderChild(child);
                          })}
                        </div>
                      </div>
                    );
                  })}
                </>
              ) : (
                <div className="w-full h-[440px] flex items-center justify-center">
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
        <div className="w-[232px] rounded-[4px] border border-solid border-[#F2F3F5] flex flex-col">
          <div className="flex-none flex items-center justify-between px-3 h-[46px]">
            <span className="text-[14px] font-pingfang font-medium">{`已选(${selectedItemsLength})`}</span>
            <span className="link-button" onClick={handleClear}>
              清空全部
            </span>
          </div>
          <div className="flex-1 overflow-hidden">
            <DragDropContext partVisible={partVisible} columns={columns} onChange={handleChange}>
              <ScrollArea className="w-full h-full overflow-auto">
                {partVisible && (
                  <>
                    <div className="bg-[#F7F8FA]">
                      <div className="flex items-center h-[36px] px-3">
                        <span className="text-[12px] font-medium font-pingfang text-[#4E5969] mr-1">
                          细分条件
                        </span>
                        <Tooltip
                          placement="top"
                          title={
                            <div className="w-[217px] text-[14px] leading-[22px] text-center">
                              拖动到该区域中的字段可作为分组依据，单元格合并展示
                            </div>
                          }
                        >
                          <div className="w-3 h-3 flex items-center justify-center">
                            <IconSvg
                              type="icon-xinxi"
                              className="text-[12px] text-[#4E5969]"
                            />
                          </div>
                        </Tooltip>
                      </div>
                      <List
                        className="px-2.5 pb-3 min-h-[133px]"
                        listItemClassName="bg-[rgba(22, 93, 255, 0.1)]"
                        listId="partList"
                        dataSource={partList}
                        onRemove={handleRemove}
                      />
                    </div>
                    <div className="border-b-[2px] border-b-solid border-b-[#EAEAEA]" />
                  </>
                )}
                <List
                  className="px-2.5 py-3 min-h-[260px] bg-[#fff]"
                  listId="unPartList"
                  dataSource={unPartList}
                  onRemove={handleRemove}
                />
              </ScrollArea>
            </DragDropContext>
          </div>
          {renderCalcIndicatior()}
          {renderAssignmentField()}
        </div>
      </>
    );
  };

  const checkedValues = useMemo(() => {
    return [...columns.partList, ...columns.unPartList].map(
      (item: any) => item.code
    );
  }, [columns]);

  return (
    <div className="w-full">
      <div className="mb-4">
        <Input
          style={{ width: 278 }}
          prefix={<SearchOutlined className="text-[#4E5969]" />}
          placeholder="搜索指标、维度"
          onPressEnter={(e: any) => handleSearch(e.target.value)}
        />
      </div>
      <div className="flex items-stretch oveflow-hidden h-[480px] gap-4">
        {renderContainer()}
      </div>
      <Form.Item name="customColumnEditDTOList" noStyle>
        <Input type="hidden" />
      </Form.Item>
      <Form.Item name="sourceId" noStyle>
        <Input type="hidden" />
      </Form.Item>
      <Form.Item name="sourceType" noStyle>
        <Input type="hidden" />
      </Form.Item>
    </div>
  );
};

export default ColumnSetting;
