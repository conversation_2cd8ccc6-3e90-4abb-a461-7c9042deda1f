/**
 * 拖拽列
 */
import { Droppable, Draggable } from "react-beautiful-dnd";
import ListItem from "./list-item";

export default function List(props: any) {
  const {
    style = {},
    className = "",
    listItemClassName = "",
    ignoreContainerClipping = false,
    isDropDisabled = false,
    isCombineEnabled = false,
    listId,
    listType = "MOVEABLE_AREA_LIST",
    dataSource,
    onRemove,
  } = props;

  return (
    <Droppable
      droppableId={listId}
      type={listType}
      ignoreContainerClipping={ignoreContainerClipping}
      isDropDisabled={isDropDisabled}
      isCombineEnabled={isCombineEnabled}
    >
      {(dropProvided, dropSnapshot) => (
        <div
          className={className}
          style={style}
          ref={dropProvided.innerRef}
          {...dropProvided.droppableProps}
        >
          {dataSource?.map((item: any, index: number) => (
            <Draggable key={item.code} draggableId={item.code} index={index}>
              {(dragProvided, dragSnapshot) => (
                <ListItem
                  key={item.code}
                  item={item}
                  className={listItemClassName}
                  isDragging={dragSnapshot.isDragging}
                  isGroupedOver={Boolean(dragSnapshot.combineTargetFor)}
                  provided={dragProvided}
                  onRemove={() => onRemove?.(listId, item)}
                />
              )}
            </Draggable>
          ))}
          {dropProvided.placeholder}
        </div>
      )}
    </Droppable>
  );
}
