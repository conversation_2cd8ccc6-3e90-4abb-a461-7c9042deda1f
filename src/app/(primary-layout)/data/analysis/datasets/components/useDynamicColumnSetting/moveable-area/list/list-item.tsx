/**
 * item
 */
import React from "react";
import { IconSvg } from "@/components";
import { cn } from "@/lib/utils";

export default function ListItem(props: any) {
  const { item, isDragging, provided, index, onRemove } = props;
  const name = item?.name || "";
  return (
    <div
      ref={provided.innerRef}
      {...provided.draggableProps}
      {...provided.dragHandleProps}
      data-is-dragging={isDragging}
      data-testid={item.code}
      data-index={index}
      className={cn(
        "w-full h-9 rounded-[4px] flex items-center bg-[#F2F3F5] mb-[6px] last:mb-0 user-select-none",
        {
          "bg-[rgba(22,93,255,0.1)]": !!item.partitionFlag,
        }
      )}
    >
      <div className="w-9 h-full flex items-center justify-center">
        <IconSvg type="icon-拖拽 (1)" className="text-[89939F]" />
      </div>
      <div className="flex-1 overflow-hidden">
        <div className="single-line-ellipsis user-select-none" title={name}>
          {name}
        </div>
      </div>
      <div className="w-9 h-full flex items-center justify-center">
        {item.fixed ? (
          <div className="w-6 h-6" />
        ) : (
          <div
            className="w-6 h-6 rounded-[4px] cursor-pointer flex items-center justify-center hover:bg-[#EAEAEA]"
            onClick={() => onRemove?.()}
          >
            <IconSvg type="iconcha1" className="text-[#858F9B]" />
          </div>
        )}
      </div>
    </div>
  );
}
