/**
 * 拖拽上下文
 */
import { DragDropContext } from "react-beautiful-dnd";
import { message } from "antd";
import { reorderColumnsMap } from "./utils";

export default function Context(props: any) {
  const { partVisible, columns, children, onChange } = props;

  const onDragEnd = (result: any) => {
    if (!result.destination) {
      return;
    }
    // 拖拽元素
    const source = result.source;
    const sourceIndex = source.index;
    const sourceDroppableId = source.droppableId;
    const sourceList = columns?.[sourceDroppableId] || [];
    const sourceItem = sourceList?.[sourceIndex];
    // 目标元素
    const destination = result.destination;
    const destinationIndex = destination.index;
    const destinationDroppableId = destination.droppableId;
    const destinationList = columns?.[destinationDroppableId] || [];
    const destinationItem = destinationList?.[destinationIndex];

    if (
      destinationDroppableId == "partList" &&
      !["BASIS_DIMENSION", "RANGE"].includes(sourceItem.type)
    ) {
      // 非维度指标不允许分区
      message.warning("指标字段不能作为细分条件");
      return;
    }

    if (
      ["BASIS_DIMENSION", "RANGE"].includes(sourceItem.type) &&
      !['self_material_put_time'].includes(sourceItem.code) && 
      destinationDroppableId == "unPartList" &&
      partVisible
    ) {
      // 基础维度指标、赋值字段不允许取消分区
      message.warning("维度字段仅作为细分条件");
      return;
    }

    if (sourceDroppableId === destinationDroppableId) {
      // 当前区域内进行拖拽
      if (sourceIndex === destinationIndex) {
        // 拖拽位置没有发生变化
        return;
      }
      if (sourceItem.fixed) {
        // 拖拽固定列拖拽 （0 - target index之间不允许有普通列）
        const betweenList = sourceList.slice(0, destinationIndex + 1);
        if (betweenList.some((item: any) => !item?.fixed)) {
          message.warning("不允许将固定的列移动到普通的列");
          return;
        }
      } else {
        // 拖拽普通列
        const betweenList =
          sourceIndex > destinationIndex
            ? sourceList.slice(destinationIndex, sourceIndex + 1)
            : sourceList.slice(sourceIndex, destinationIndex + 1);
        if (betweenList.some((item: any) => item.fixed)) {
          message.warning("不允许将普通的列移动到固定的列");
          return;
        }
      }
    }
    // 跨区域 (固定列存在多个分区时有问题，咱不支持固定列)

    const data = reorderColumnsMap({
      columnsMap: columns,
      source,
      destination,
    });
    onChange?.(data.columnsMap);
  };

  return <DragDropContext onDragEnd={onDragEnd}>{children}</DragDropContext>;
}
