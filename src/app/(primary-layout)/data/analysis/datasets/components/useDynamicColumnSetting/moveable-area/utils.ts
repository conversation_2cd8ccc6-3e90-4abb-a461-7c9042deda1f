export const reorderColumns = (list: any, startIndex: any, endIndex: any) => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};

/**
 * 拖拽排序
 * @param param0
 * @returns
 */
export const reorderColumnsMap = ({ columnsMap, source, destination }: any) => {
  const ddId = destination.droppableId;
  const sdId = source.droppableId;
  const current = [...columnsMap[sdId]];
  const next = [...columnsMap[ddId]];
  const target = current[source.index];

  // 当前列
  if (sdId === ddId) {
    const reordered = reorderColumns(current, source.index, destination.index);
    const result = {
      ...columnsMap,
      [sdId]: reordered?.map((i: any, index: number) => ({
        ...i,
        index,
      })),
    };
    return {
      columnsMap: result,
    };
  }

  // 其他列
  // 从原列中删除
  current.splice(source.index, 1);
  // 插入到新列
  next.splice(destination.index, 0, {
    ...target,
    partitionFlag: ddId === 'partList',
  });

  const result = {
    ...columnsMap,
    [sdId]: current?.map((i: any, index: number) => ({
      ...i,
      index,
    })),
    [ddId]: next?.map((i: any, index: number) => ({
      ...i,
      index,
    })),
  };

  return {
    columnsMap: result,
  };
};
