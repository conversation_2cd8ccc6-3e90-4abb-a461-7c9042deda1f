const baseData = [
  {
    key: "dimenssion",
    name: "维度",
    required: true,
    childrenTypes: ["BASIS_DIMENSION", "RANGE"], // 用于提交校验
    children: [
      {
        key: "basisDimensionInfoList",
        name: "基础维度（文本&日期）",
      },
      {
        key: "partitionDimensionInfoList",
        name: "赋值字段",
      },
    ],
  },
  {
    key: "indicator",
    name: "指标",
    required: true,
    childrenTypes: ["BASIS_INDICATOR", "CALCULATE_INDICATOR"],
    children: [
      {
        key: "basisIndicatorInfoList",
        name: "基础指标（数值）",
      },
      {
        key: "indicatorInfoList",
        name: "计算字段",
      },
    ],
  },
];

const subData = [
  {
    key: "adInfo",
    name: "广告信息",
    includes: [
      "campaign_id",
      "campaign_name",
      "adgroup_id",
      "adgroup_name",
      "ad_id",
      "ad_name",
      "self_material_put_time",
      "active_date_day",
    ],
  },
  {
    key: "customerInfo",
    name: "客户信息",
    includes: ["agency", "channel_name", "business_line"],
  },
  {
    key: "tags",
    name: "标签",
    includes: [
      "self_file_name_modified",
      "self_topic",
      "self_material_type",
      "self_material_format",
      "self_material_video_duration",
      "self_is_real_remark",
      "self_os_type",
      "self_vendor",
    ],
  },
];

export const formatSubData = (basisDimensionInfoList: any[]) => {
  if (
    !Array.isArray(basisDimensionInfoList) ||
    basisDimensionInfoList?.length === 0
  ) {
    return [];
  }
  const basisDimensionInfoData = basisDimensionInfoList.reduce(
    (prev, next) => ({
      ...prev,
      [next.code]: next,
    }),
    {}
  );

  return subData
    .map(({ includes, ...item }: any) => {
      return {
        ...item,
        children: includes
          .map((k: string) => basisDimensionInfoData[k])
          .filter((i: any) => !!i?.code),
      };
    })
    .filter(
      (item: any) => Array.isArray(item.children) && item.children.length > 0
    );
};

export const mapDataSource = (res?: any) => {
  return baseData
    .map((item: any) => {
      const children = item.children;
      const newChildren = children
        .map((subItem: any) => {
          const subChildren = res?.[subItem.key];
          if (Array.isArray(subChildren) && subChildren.length > 0) {
            return {
              ...subItem,
              children: subChildren,
            };
          }
          return null;
        })
        .filter((c: any) => !!c);
      return {
        ...item,
        children: newChildren,
      };
    })
    .filter(
      (item: any) => Array.isArray(item.children) && item.children.length > 0
    );
};

/**
 * 格式化
 * @param list
 * @returns
 */
export const formatColumns = (list: any) => {
  if (!Array.isArray(list) || list?.length === 0) {
    return {
      partList: [],
      unPartList: [],
    };
  }
  const partList = list
    .filter((item: any) => item.partitionFlag)
    .sort((a: any, b: any) => a.index - b.index);
  const unPartList = list
    .filter((item: any) => !item.partitionFlag)
    .sort((a: any, b: any) => a.index - b.index);
  // if (fixedColumn) {
  //   // 存在固定列，前端进行重新排序
  //   const fixedColumnIdx = unPartList.findIndex(
  //     (item: any) => item.code === fixedColumn
  //   );
  //   if (fixedColumnIdx > -1) {
  //     const fixedColumnItem = unPartList[fixedColumnIdx];
  //     unPartList.splice(fixedColumnIdx, 1);
  //     console.log(unPartList, '===>unPartList')
  //     return {
  //       partList,
  //       unPartList: [fixedColumnItem, ...unPartList].map(
  //         (item: any, index: number) => {
  //           return {
  //             ...item,
  //             index,
  //           };
  //         }
  //       ),
  //     };
  //   }
  // }
  return {
    partList,
    unPartList,
  };
};

function compare(types: string[], checkedValues: any) {
  return types.some((type: string) =>
    checkedValues.some((v: any) => v.type === type)
  );
}

/**
 * 校验勾选项
 * @param checkedValues
 * @returns
 */
export const validateCheckedValues = (checkedValues: any) => {
  if (baseData.some((b: any) => b.required)) {
    const validateResult = baseData.reduce((acc: any, cur: any) => {
      if (cur.required) {
        const childrenTypes = cur.childrenTypes || [];
        return {
          ...acc,
          [cur.key]: {
            name: cur.name,
            isOk: compare(childrenTypes, checkedValues),
          },
        };
      }
      return acc;
    }, {});
    const result = Object.values(validateResult).filter(
      (item: any) => !item.isOk
    );
    if (result.length > 0) {
      return result[0] || {};
    }
    return { isOk: true };
  }
  return { isOk: true };
};

export const filterNewColumn = (columnList: any, allList: any) => {
  if (!Array.isArray(allList) || allList?.length === 0) {
    return columnList;
  }
  if (!Array.isArray(columnList) || columnList?.length === 0) {
    return [];
  }
  return columnList
    .map((item: any) => {
      const current = allList.find(
        (subItem: any) => subItem.code === item.code
      );
      if (current) {
        // 存在，更新名称
        return {
          ...item,
          name: current.name,
        };
      }
      return item;
    })
    .map((item: any, index: number) => ({ ...item, index }));
};
