import * as React from "react";
import { Form, Input, Select, Button, Spin } from "antd";
import GroupNumList from "./group-num-list";
import GroupTextList from "./group-text-list";
import useDynamicColumns from "../useDynamicColumnSetting/useDynamicColumns";
import { getTextColumnEnumList, groupIndicatorInfo } from "./services";

const { useState, useEffect } = React;

const FormItem = Form.Item;

export default function AssignmentField({ initialValues, form }: any) {
  const { id, type, code } = initialValues || {};
  const isEdit = !!code;

  const [loading, setLoading] = useState<boolean>(isEdit);
  const [codeOptions, setCodeOptions] = useState<any>([]);
  const [textEnumOptions, setTextEnumOptions] = useState<any>([]);
  const [unit, setUnit] = useState<string>("");

  const { result } = useDynamicColumns({ id, type });

  useEffect(() => {
    // 赋值依据选项
    const {
      basisDimensionInfoList,
      basisIndicatorInfoList,
      indicatorInfoList,
    } = result || {};
    const options = [
      ...(basisDimensionInfoList || []),
      ...(basisIndicatorInfoList || []),
      ...(indicatorInfoList || []),
    ]
      .filter((item: any) => item.dataType !== "date")
      .map((item: any) => ({
        ...item,
        label: item.name,
        value: item.code,
      }));
    setCodeOptions(options);
  }, [result]);

  useEffect(() => {
    // 文本分组下拉选项
    async function fetchOptions(params: any) {
      const { data = {} } = await getTextColumnEnumList(params);
      const result = data?.result;
      if (Array.isArray(result)) {
        setTextEnumOptions(result);
      }
    }
    if (id && type) {
      fetchOptions({ id, type });
    }
  }, []);

  useEffect(() => {
    async function fetchInfo(params: any) {
      try {
        const { data = {} } = await groupIndicatorInfo(params);
        if (data?.code === 0) {
          const {
            name,
            baseFieldCode,
            symbol,
            type: groupType,
            itemList: indicatorList,
          } = data?.result || {};
          form.setFieldsValue({
            name,
            baseFieldCode,
            groupType,
            indicatorList,
            code,
            id,
            type,
          });
          setUnit(symbol || "");
        }
      } finally {
        setLoading(false);
      }
    }
    if (id && type) {
      if (code) {
        // 编辑
        fetchInfo({ id, type, code });
      } else {
        form.setFieldsValue({ ...initialValues });
      }
    }
  }, []);

  const renderList = ({ baseFieldCode, groupType, fields, remove }: any) => {
    if (!baseFieldCode) {
      return null;
    }
    if (groupType === "RANGE") {
      return (
        <GroupNumList
          fields={fields}
          inputProps={unit ? { addonAfter: unit } : {}}
          remove={remove}
        />
      );
    }
    const current = textEnumOptions.find(
      (item: any) => item.code === baseFieldCode
    );
    const options = Array.isArray(current?.valueList)
      ? current.valueList.map((s: string) => {
          if (s === "#null#") {
            return { label: "空值", value: s };
          }
          return { label: s, value: s };
        })
      : [];
    return <GroupTextList fields={fields} options={options} remove={remove} />;
  };

  if (loading) {
    return (
      <div className="pt-6 pb-2 min-h-[200px] flex items-center justify-center">
        <Spin spinning />
      </div>
    );
  }

  return (
    <div className="pt-6 pb-2">
      <div className="flex items-center gap-4 mb-1">
        <FormItem
          className="flex-1"
          labelCol={{ span: 7 }}
          wrapperCol={{ span: 16 }}
          label="赋值字段名称"
          name="name"
          rules={[{ required: true, message: "请输入名称", whitespace: true }]}
        >
          <Input placeholder="请输入名称" maxLength={50} showCount />
        </FormItem>
        <FormItem
          className="flex-1"
          labelCol={{ span: 5 }}
          wrapperCol={{ span: 18 }}
          label="赋值依据"
          name="baseFieldCode"
          rules={[{ required: true, message: "请选择赋值依据" }]}
        >
          <Select
            showSearch
            allowClear
            style={{ width: "100%" }}
            placeholder="请选择"
            options={codeOptions}
            disabled={!!code}
            filterOption={(input: any, option: any) =>
              (option?.label ?? "")
                ?.toLowerCase?.()
                ?.includes?.(input?.toLowerCase?.())
            }
            onChange={(v, option) => {
              if (!v) {
                // 清除
                form.setFieldsValue({
                  groupType: undefined,
                  indicatorList: undefined,
                });
                setUnit("");
                return;
              }
              const isNumeric = option?.dataType === "numeric";
              const newGroupType = isNumeric ? "RANGE" : "ENUMERATION";
              form.setFieldsValue({
                groupType: newGroupType,
                indicatorList: isNumeric
                  ? [
                      {
                        groupName: undefined,
                        maxValue: undefined,
                        minValue: undefined,
                      },
                    ]
                  : [{ groupName: undefined, valueList: undefined }],
              });
              setUnit(option?.symbol || "");
            }}
          />
        </FormItem>
      </div>
      <div>
        <FormItem
          shouldUpdate={(prev, next) => prev.baseFieldCode !== next.baseFieldCode}
          noStyle
        >
          {({ getFieldValue }: any) => {
            const groupType = getFieldValue("groupType");
            const baseFieldCode = getFieldValue("baseFieldCode");
            return (
              <Form.List name="indicatorList">
                {(fields, { add, remove }) => (
                  <div className="flex flex-col">
                    <div className="py-4 flex items-center justify-between mb-2 sticky top-0 left-0 bg-white z-10">
                      {fields.length > 0 ? (
                        <span className="font-medium text-gray-700">{`已分组：${fields.length}项`}</span>
                      ) : (
                        <span />
                      )}
                      <div className="flex items-center gap-4">
                        {fields.length > 0 ? (
                          <span className="ml-4 text-sm text-[#ff4d4f] font-normal">
                            注：其余未分组的值分到【其它】组
                          </span>
                        ) : (
                          <span />
                        )}
                        <Button
                          type="primary"
                          disabled={!groupType}
                          onClick={() => add()}
                        >
                          + 添加分组
                        </Button>
                      </div>
                    </div>
                    {renderList({ baseFieldCode, groupType, fields, remove })}
                  </div>
                )}
              </Form.List>
            );
          }}
        </FormItem>
      </div>
      <FormItem noStyle name="id">
        <Input type="hidden" />
      </FormItem>
      <FormItem noStyle name="type">
        <Input type="hidden" />
      </FormItem>
      {isEdit && (
        <FormItem noStyle name="code">
          <Input type="hidden" />
        </FormItem>
      )}
      <FormItem noStyle name="groupType">
        <Input type="hidden" />
      </FormItem>
    </div>
  );
}
