import requestHelper from "@/utils/requestHelper";

/**
 * 文本类型字段筛选项下拉选项信息获取
 * @param data
 * @returns
 */
export function getTextColumnEnumList(data = {}) {
  return requestHelper.post(
    "v4/demand/dam/creative/auto/analysis/text/column/enum/list",
    data
  );
}

/**
 * 赋值字段新增
 * @param data
 * @returns
 */
export function groupIndicatorCreate(data = {}) {
  return requestHelper.post(
    "v4/demand/dam/creative/auto/analysis/group/indicator/create",
    data
  );
}

/**
 * 赋值字段信息查询
 * @param data
 * @returns
 */
export function groupIndicatorInfo(data = {}) {
  return requestHelper.post(
    "v4/demand/dam/creative/auto/analysis/group/indicator/info",
    data
  );
}

/**
 * 赋值字段编辑
 * @param data
 * @returns
 */
export function groupIndicatorEdit(data = {}) {
  return requestHelper.post(
    "v4/demand/dam/creative/auto/analysis/group/indicator/edit",
    data
  );
}
