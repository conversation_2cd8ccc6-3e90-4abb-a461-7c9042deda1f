/**
 * 分组列表
 */

import * as React from "react";
import { Form, Input, InputNumber } from "antd";
import IconSvg from "@/components/Icon/SinoSymbolFont";

const FormItem = Form.Item;

export default function GroupNumList({
  fields,
  inputProps,
  remove,
  ...props
}: any) {
  return (
    <div>
      {fields.map((field: any) => (
        <div
          key={field.key}
          className="px-4 pt-6 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors mb-3 last:mb-0"
        >
          <div className="flex">
            <FormItem
              wrapperCol={{ span: 24 }}
              name={[field.name, "groupName"]}
              className="w-[240px]"
              rules={[
                {
                  required: true,
                  message: "请输入分组名称",
                  whitespace: true,
                },
              ]}
            >
              <Input
                placeholder="分组名称"
                style={{ width: "100%" }}
                maxLength={50}
                showCount
              />
            </FormItem>
            <div className="flex-1 overflow-hidden flex ml-8">
              <FormItem
                wrapperCol={{ span: 24 }}
                name={[field.name, "minValue"]}
                className="flex-1"
                rules={[
                  {
                    required: true,
                    message: "请输入最小值",
                  },
                ]}
              >
                <InputNumber
                  placeholder="最小值"
                  style={{ width: "100%" }}
                  precision={4}
                  max={9999999999}
                  {...inputProps}
                />
              </FormItem>
              <span className="w-[32px] text-center leading-[32px]">{"≤"}</span>
              <span className="w-[20px] text-center leading-[32px]">值</span>
              <span className="w-[32px] text-center leading-[32px]">{"<"}</span>
              <FormItem
                wrapperCol={{ span: 24 }}
                name={[field.name, "maxValue"]}
                className="flex-1"
                rules={[
                  {
                    required: true,
                    message: "请输入最大值",
                  },
                ]}
              >
                <InputNumber
                  placeholder="最大值"
                  style={{ width: "100%" }}
                  precision={4}
                  max={9999999999}
                  {...inputProps}
                />
              </FormItem>
            </div>
            {fields.length > 1 ? (
              <div
                className="w-8 h-8 rounded-[4px] cursor-pointer flex items-center justify-center hover:bg-gray-100 ml-4"
                onClick={() => remove(field.name)}
              >
                <IconSvg type="icon-shanchu2" className="text-[#858F9B]" />
              </div>
            ) : (
              <div className="w-8 ml-4" />
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
