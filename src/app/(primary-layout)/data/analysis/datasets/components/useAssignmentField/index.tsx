/**
 * 动态列设置
 */
import * as React from "react";
import { message } from "antd";
import { useModalWrap } from "@/components";
import AssignmentField from "./assignment-field";
import { groupIndicatorCreate, groupIndicatorEdit } from "./services";

const { useState } = React;
export default function useAssignmentField({ onSaveCallback }: any) {
  const [record, setRecord] = useState<any>({});

  /**
   * 提交
   * @param params
   * @returns
   */
  const handleOk = async (values: any) => {
    const saveFn = record.code ? groupIndicatorEdit : groupIndicatorCreate;
    try {
      const { data = {} } = await saveFn(values);
      if (data?.code === 0) {
        message.success("保存成功");
        onSaveCallback?.({showUpdateEnumList: true});
        return true;
      }
      return false;
    } catch (error: any) {
      message.error(error?.message || "保存失败");
      return false;
    }
  };

  const { showModal, render } = useModalWrap({
    modalProps: {
      title: (
        <h3 className="text-lg">
          {record.code ? "编辑赋值字段" : "添加赋值字段"}
        </h3>
      ),
      width: 920,
      styles: {
        header: {
          padding: "16px 24px",
          marginBottom: 0,
        },
        body: {
          padding: "0 24px",
          minHeight: "160px",
          maxHeight: "calc(100vh - 200px)",
          overflowY: "auto",
        },
        footer: {
          padding: "16px 24px",
        },
      },
      onOk: handleOk,
    },
    children: ({ showParams, form }: any) => {
      return <AssignmentField form={form} initialValues={showParams || {}} />;
    },
  });

  const handleShow = async (params: any) => {
    setRecord(params);
    showModal(params);
  };

  return {
    showAssignmentField: handleShow,
    renderAssignmentField: render,
  };
}
