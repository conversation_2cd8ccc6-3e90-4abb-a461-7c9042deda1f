/**
 * 分组列表
 */

import * as React from "react";
import { Form, Input, Select } from "antd";
import IconSvg from "@/components/Icon/SinoSymbolFont";

const FormItem = Form.Item;

export default function GroupTextList({ fields, options, remove }: any) {
  return (
    <div>
      {fields.map((field: any) => (
        <div
          key={field.key}
          className="px-4 pt-6 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors mb-3 last:mb-0"
        >
          <div className="flex">
            <FormItem
              wrapperCol={{ span: 24 }}
              name={[field.name, "groupName"]}
              className="w-[300px]"
              rules={[
                {
                  required: true,
                  message: "请输入分组名称",
                  whitespace: true,
                },
              ]}
            >
              <Input placeholder="分组名称" style={{ width: "100%" }} maxLength={50} showCount />
            </FormItem>
            <div className="flex-1 overflow-hidden flex ml-8">
              <FormItem
                wrapperCol={{ span: 24 }}
                name={[field.name, "valueList"]}
                className="flex-1"
                rules={[
                  {
                    required: true,
                    message: "请选择",
                  },
                ]}
              >
                <Select
                  mode="multiple"
                  showSearch
                  maxTagCount={3}
                  allowClear
                  style={{ width: "100%" }}
                  placeholder="请选择"
                  options={options}
                  filterOption={(input: any, option: any) =>
                    (option?.label ?? "")
                      ?.toLowerCase?.()
                      ?.includes?.(input?.toLowerCase?.())
                  }
                />
              </FormItem>
            </div>
            {fields.length > 1 ? (
              <div
                className="w-8 h-8 rounded-[4px] cursor-pointer flex items-center justify-center hover:bg-gray-100 ml-4"
                onClick={() => remove(field.name)}
              >
                <IconSvg type="icon-shanchu2" className="text-[#858F9B]" />
              </div>
            ) : (
              <div className="w-8 ml-4" />
            )}
          </div>
        </div>
      ))}
    </div>
  );
}
