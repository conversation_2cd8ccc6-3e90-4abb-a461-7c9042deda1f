/**
 * 动态列设置
 */
import * as React from "react";
import { message } from "antd";
import _ from "lodash";
import { parse } from "mathjs";
import { useModalWrap } from "@/components";
import CalcIndicator from "./calc-indicator";
import { getPureFormula, formatIndicatorName, getRandomInt } from "./utils";
import { dynamicColumnInfo } from "../../services";
import { indicatorAdd, indicatorEdit } from "./services";

const { useState } = React;
export default function useDynamicColumnSetting({ onSaveCallback }: any) {
  const [loading, setLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any>([]);
  const [record, setRecord] = useState<any>({});

  async function getOptions(params: any) {
    try {
      const { data = {} } = await dynamicColumnInfo(params);
      if (data?.code === 0) {
        const { basisIndicatorInfoList } = data?.result || {};
        if (
          Array.isArray(basisIndicatorInfoList) &&
          basisIndicatorInfoList.length > 0
        ) {
          const res = basisIndicatorInfoList.map((item: any) => {
            return {
              key: item.code,
              value: item.code,
              label: item.name,
              format: item.dataType,
            };
          });
          setDataSource(res);
        }
      }
    } finally {
      setLoading(false);
    }
  }

  /**
   * 校验公式合规
   * @param exprStr
   * @param scope
   * @returns
   */
  function evalWithZeroCheck(exprStr: string, scope = {}) {
    const node = parse(exprStr);
    node.traverse((n: any) => {
      if (n.isOperatorNode && n.op === "/") {
        // 校验分母不能为0
        const denomValue = n.args[1].evaluate(scope);
        if (denomValue === 0) {
          throw new Error("分母不能为0");
        }
      }
    });
    return node.evaluate(scope);
  }

  function validateFormula(value: any) {
    if (_.isEmpty(value)) {
      return Promise.resolve({
        success: false,
        message: "请输入计算公式",
      });
    }
    // 校验计算公式是否合法
    const { outer, formula } = getPureFormula(value, dataSource);
    if (outer?.length) {
      if (_.compact(outer).length) {
        return Promise.resolve({
          success: false,
          message: `公式中存在未定义的指标：${outer.join(",")}`,
        });
      }
      return Promise.resolve({
        success: false,
        message: "公式不合规",
      });
    }

    if (!formula) {
      return Promise.resolve({
        success: false,
        message: "公式不合规",
      });
    }
    try {
      evalWithZeroCheck(
        formula || "",
        dataSource.reduce((acc: any, cur: any) => {
          return {
            ...acc,
            [cur.key]: getRandomInt(10, 1000),
          };
        }, {})
      );
      return Promise.resolve({
        success: true,
      });
    } catch (error: any) {
      return Promise.resolve({
        success: false,
        message: "公式不合规",
      });
    }
  }
  /**
   * 提交
   * @param params
   * @returns
   */
  const handleOk = async (values: any) => {
    const { name, symbol } = values;
    const result: any = await validateFormula(values._formula);
    if (!result.success) {
      message.destroy();
      result?.message && message.warning(result.message);
      return false;
    }
    const _formula = formatIndicatorName(values._formula, true);
    const res = getPureFormula(_formula, dataSource);
    // id存在为编辑
    const { data = {} } = await (record?.id ? indicatorEdit : indicatorAdd)({
      expression: res?.formula,
      expressionName: _formula,
      symbol: symbol || "",
      name,
      ...record,
    });
    if (data?.code === 0) {
      message.success("保存成功");
      onSaveCallback?.();
      return true;
    }
    return false;
  };

  const { showModal, render } = useModalWrap({
    modalProps: {
      title: record?.id ? "编辑计算字段" : "添加计算字段",
      width: 740,
      styles: {
        header: {
          padding: "16px 24px",
          marginBottom: 0,
        },
        body: {
          padding: "0 24px",
        },
        footer: {
          padding: "16px 24px",
        },
      },
      okText: "保存",
      onOk: handleOk,
      okButtonProps: {
        loading,
      },
      afterClose: () => {
        setDataSource([]);
        setLoading(false);
      },
    },
    children: ({ showParams, form }: any) => {
      const { id } = showParams || {};
      return (
        <CalcIndicator
          form={form}
          loading={loading}
          id={id}
          dataSource={dataSource}
        />
      );
    },
  });

  const handleShow = async (params: any) => {
    const { sourceId, sourceType } = params || {};
    if (sourceId && sourceType) {
      getOptions({ id: sourceId, type: sourceType });
    }
    setRecord(params);
    showModal(params);
  };

  return {
    showCalcIndicatior: handleShow,
    renderCalcIndicatior: render,
  };
}
