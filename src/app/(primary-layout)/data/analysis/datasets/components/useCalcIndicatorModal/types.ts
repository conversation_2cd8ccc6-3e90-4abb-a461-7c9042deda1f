export interface IDataSources {
    label?: string;
    value?: string;
    disabled?: boolean;
    extra?: any;
    children?: IDataSources[];
}

export interface Indicator extends OP.Indicator {
  /** 字段类型 */
  type?: EFieldType;
  /** 字段格式化方式 */
  format?: EFieldDataType;
  /** 自定义指标-展示 */
  _formula?: string;
  /** 自定义指标-指标Keys */
  _formulaIndicatorKeys?: string[];
  /** 数据表-持久化列宽 */
  _width?: number;
  /** 数据表-持久化列表-环比 */
  _qoqWidth?: number;
}

/** 字段类型 */
export enum EFieldType {
  指标 = 1,
  维度 = 2,
}


/** 字段数据类型 */
export enum EFieldDataType {
  /** 默认展示2位 */
  小数 = 'decimal',
  小数1 = 'decimal_1',
  小数2 = 'decimal_2',
  小数3 = 'decimal_3',
  小数4 = 'decimal_4',
  小数5 = 'decimal_5',
  小数6 = 'decimal_6',
  整数 = 'integer',
  百分比 = 'percentage',
  文本 = 'text',
}