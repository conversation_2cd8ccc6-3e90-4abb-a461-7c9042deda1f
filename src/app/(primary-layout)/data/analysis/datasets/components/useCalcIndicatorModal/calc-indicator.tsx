/**
 * 计算
 */
import { useEffect, useState, useMemo, useRef } from "react";
import { Input, Form, Tooltip, Spin, Radio, Select } from "antd";
import _ from "lodash";
import { ScrollArea } from "@/components/ui/scroll-area";
import { operators, base_num, formatIndicatorName } from "./utils";
import { Button } from "@/components/ui/button";
import { indicatorInfo } from "./services";

const FormItem = Form.Item;

// const SPACE = " ";

export default function FormulaEditor(props: any) {
  const { loading, id, form, dataSource } = props;
  // const textAreaRef = useRef<any>(null);
  const [textValue, setTextValue] = useState<string>("");
  const [indicators, setIndicators] = useState<any[]>([]);

  useEffect(() => {
    setIndicators(dataSource);
  }, [dataSource]);

  useEffect(() => {
    async function fetchData(params: { id: number }) {
      try {
        const { data = {} } = await indicatorInfo(params);
        if (data?.code === 0) {
          const { name, expressionIndicatorName, symbol } =
            data?.result || {};
          const _formula = formatIndicatorName(expressionIndicatorName);
          form.setFieldsValue({ _formula, name, symbol: symbol || undefined });
          setTextValue(_formula?.replace(/\@/g, "") || "");
        }
      } catch (error) {
        console.log(error);
      }
    }
    if (id) {
      // 编辑态
      fetchData({ id });
    }
  }, []);

  // const insertText = async (text: string) => {
  //   const textarea = textAreaRef.current?.resizableTextArea?.textArea;
  //   if (textarea) {
  //     const { selectionStart, selectionEnd } = textarea;
  //     const value = textarea.value || "";
  //     const newText =
  //       value.slice(0, selectionStart) + text + value.slice(selectionEnd);
  //     form.setFieldsValue({ _formula: newText });
  //     // await form.validateFields(["_formula"]);
  //     // 手动更新光标位置
  //     setTimeout(() => {
  //       const pos = selectionStart + text.length;
  //       textarea.setSelectionRange(pos, pos);
  //       textarea.focus();
  //     }, 0);
  //   }
  // };

  const insertText = async (
    text: string,
    type: "indicator" | "number" | "operator"
  ) => {
    const _formula = form.getFieldValue("_formula") || "";
    const pureFormula = _formula.replace(/\@/g, "");
    const isOperators: boolean = type === "operator";
    const isNumber: boolean = type === "number";
    const newFormula = isOperators
      ? `${_formula}@ ${text} @`
      : isNumber
      ? `${_formula}${text}`
      : `${_formula}@${text}@`;
    form.setFieldsValue({ _formula: newFormula });
    const newTextValue = isOperators
      ? `${pureFormula} ${text} `
      : `${pureFormula}${text}`;
    setTextValue(newTextValue);
  };

  /**
   * 清空
   */
  const clear = () => {
    form.setFieldsValue({ _formula: undefined });
    setTextValue("");
  };

  const handleSearch = (v: string) => {
    if (!v) {
      setIndicators(dataSource);
      return;
    }
    const filterOptions = dataSource.filter((item: any) => {
      const label = item.label || "";
      return label.toLowerCase().includes(v?.toLowerCase() || "");
    });
    setIndicators(filterOptions);
  };

  return (
    <div id="calc-indicator-modal" className="flex flex-col pt-6 h-[506px]">
      {loading ? (
        <div className="h-full flex items-center justify-center">
          <Spin spinning />
        </div>
      ) : (
        <>
          <div className="flex-none flex items-center gap-10">
            <FormItem
              className="flex-1"
              name="name"
              label="字段名称"
              rules={[{ required: true, message: "请输入字段名称" }]}
            >
              <Input showCount maxLength={50} placeholder="请填写" />
            </FormItem>
            <FormItem
              className="w-[260px]"
              name="symbol"
              label="显示单位"
            >
              <Select
                placeholder="请选择显示单位"
                style={{ width: "100%" }}
                options={[{ label: "%", value: "%" }]}
                allowClear
              />
            </FormItem>
          </div>
          <div className="flex-1 overflow-hidden flex items-stretch border border-solid border-[#E5E6EB] rounded-[4px]">
            <div className="h-full flex flex-col p-[6px] w-[180px] flex-none border-r border-r-solid border-r-[#E5E6EB]">
              <div className="flex-none mb-2">
                <Input
                  placeholder="搜索"
                  allowClear
                  onChange={(e: any) => handleSearch(e.target.value)}
                />
              </div>
              <div className="flex-1 overflow-hidden">
                <ScrollArea className="w-full h-full">
                  {indicators.map((v: any) => (
                    <div
                      key={v.value}
                      className="w-full h-[32px] flex items-center px-4 cursor-pointer hover:bg-gray-100 mb-[2px] last:mb-0"
                      onClick={() => insertText(v.label, "indicator")}
                    >
                      <Tooltip placement="topLeft" title={v.label}>
                        <div className="single-line-ellipsis">{v.label}</div>
                      </Tooltip>
                    </div>
                  ))}
                </ScrollArea>
              </div>
            </div>
            <div className="h-full flex-1 bg-[#F7F8FA] overflow-hidden flex flex-col">
              <div className="h-[142px] bg-[#fff] flex-none px-2.5 py-2">
                <div className="border-gray-200 bg-white rounded-t-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-xs font-medium text-gray-600">
                      运算符:
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clear}
                      className="h-6 px-2 text-xs text-blue-600 border-blue-200 hover:bg-blue-50"
                    >
                      清空
                    </Button>
                  </div>

                  {/* Operators - 使用shadcn Button */}
                  <div className="flex flex-wrap gap-1 mb-3">
                    {operators.map((operator) => (
                      <Button
                        key={operator}
                        variant="outline"
                        size="sm"
                        onClick={() => insertText(operator, "operator")}
                        className="w-8 h-7 p-0 text-sm text-gray-700 border-gray-300 hover:bg-gray-100"
                      >
                        {operator}
                      </Button>
                    ))}
                  </div>

                  <div className="mb-1">
                    <span className="text-xs font-medium text-gray-600">
                      数字:
                    </span>
                  </div>

                  {/* Numbers - 使用shadcn Button */}
                  <div className="flex flex-wrap gap-1">
                    {base_num.map((number) => (
                      <Button
                        key={number}
                        variant="outline"
                        size="sm"
                        onClick={() => insertText(number, "number")}
                        className="w-8 h-7 p-0 text-sm text-gray-700 border-gray-300 hover:bg-gray-100"
                      >
                        {number}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* <div className="flex items-center mb-2">
                  <span className="w-[60px] flex-none">运算符：</span>
                  <div className="flex-1 flex items-center justify-between gap-2">
                    <div className="flex items-center gap-2">
                      {operators.map((op) => (
                        <div
                          key={op}
                          className="w-6 h-6 rounded-[4px] flex items-center justify-center cursor-pointer hover:bg-gray-100 transition-all duration-200"
                          onClick={() => insertText(op)}
                        >
                          {op}
                        </div>
                      ))}
                    </div>
                    <div className="link-button" onClick={clear}>
                      清空
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <span className="w-[60px] flex-none">数字：</span>
                  <div className="flex-1 flex items-center gap-2">
                    {base_num.map((num) => (
                      <div
                        key={num}
                        className="w-6 h-6 rounded-[4px] flex items-center justify-center cursor-pointer hover:bg-gray-100 transition-all duration-200"
                        onClick={() => insertText(num)}
                      >
                        {num}
                      </div>
                    ))}
                  </div>
                </div> */}
              </div>
              <div className="flex-1 overflow-hidden">
                <ScrollArea className="w-full h-full py-2">
                  <Input.TextArea
                    // ref={textAreaRef}
                    value={textValue}
                    // placeholder="请填写"
                    allowClear
                    autoSize={{ minRows: 10 }}
                    variant="borderless"
                    disabled
                    className="!text-[#121212] font-medium"
                  />
                </ScrollArea>
              </div>
            </div>
            <FormItem name="_formula">
              <Input type="hidden" />
            </FormItem>
          </div>
        </>
      )}
    </div>
  );
}
