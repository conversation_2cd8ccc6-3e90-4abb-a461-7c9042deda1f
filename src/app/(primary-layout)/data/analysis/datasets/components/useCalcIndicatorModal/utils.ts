import _ from "lodash";
import type { IDataSources } from "./types";

export const operators = ["+", "-", "×", "÷", "(", ")"];

export const base_num = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];

export function formatIndicatorName(name: string, reverser = false) {
  if (!name) return "";
  if (!reverser) {
    return name.replace(/\*/g, "×").replace(/\@ \/ \@/g, "@ ÷ @");
  }
  return name.replace(/\×/g, "*").replace(/\@ \÷ \@/g, "@ / @");
}

/**
 * 获取纯净的公式\
 * 根据实则运算符进行分割
 */
export function getPureFormula(value?: string, dataSource?: IDataSources[]) {
  // 替换换行符为空格
  value = _.trim(value).replace(/[\r\n]/g, "");
  if (!value) return {};

  let formula = undefined;
  // const indicatorNames = _.chain(value)
  //   .split(/[\+\-\*\/\(\)]/)
  //   .map(_.trim)
  //   .value().filter((it) => !!it);
  const indicatorNames = _.chain(value)
    .split(/[\@]/)
    .map(_.trim)
    .value()
    .filter((it) => !!it && !operators.includes(it));
  // 在数据源 | 不在数据源
  const [inner, outer] = _.partition(indicatorNames, (it) => {
    return _.some(dataSource, (data) => data.label === it);
  });
  const _formulaIndicatorMap = _.chain(inner)
    .map((label) => _.find(dataSource, { label }))
    .uniq()
    .compact()
    .value();
  const _formulaIndicatorKeys = _.chain(_formulaIndicatorMap)
    .map("value")
    .compact()
    .value();
  const pureOuter = outer?.filter((it) => !(it && typeof Number(it) === "number"));
  // 如果全部在数据源中，则将所有指标名替换为数据源的key
  if (_.isEmpty(pureOuter)) {
    // 按照指标名顺序进行替换 并 将value按_formulaIndicatorMap的labe和value进行替换
    formula = _.chain(_formulaIndicatorMap)
      .orderBy([(it) => it.label?.length], "desc")
      .reduce((acc, cur) => {
        if (cur?.label && cur?.value)
          acc = acc.replaceAll(cur.label, cur.value);
        return acc;
      }, value)
      .value()
      .replace(/\@/g, "").replace(/\×/g, "*").replace(/\÷/g, "/");
  }
  return {
    /** 在数据源内的name */
    inner,
    /** 不在数据源内的name */
    outer: pureOuter,
    /** 公式用到的指标keys */
    _formulaIndicatorKeys,
    /** 服务端用到的公式 */
    formula,
  };
}


/**
 * 生成随机数
 * @param min 
 * @param max 
 * @returns 
 */
export function getRandomInt(min: number, max: number) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}