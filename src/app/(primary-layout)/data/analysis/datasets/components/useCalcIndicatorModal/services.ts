import requestHelper from '@/utils/requestHelper'

/**
 * 新增字段
 * @param data 
 * @returns 
 */
export function indicatorAdd(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/calculate/indicator/add', data)
}

/**
 * 编辑字段
 * @param data 
 * @returns 
 */
export function indicatorEdit(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/calculate/indicator/edit', data)
}

/**
 * 计算字段信息查询
 * @param data 
 * @returns 
 */
export function indicatorInfo(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/calculate/indicator/info', data)
}