/**
 * 数据过滤
 */
import { forwardRef } from "react";
import CapsuleFilter from "@/app/(primary-layout)/data/analysis/components/CapsuleFilter";
import { analysisFilterEdit } from "../services";

const FilterButton = forwardRef(
  ({ id, type, onSaveCallback }: any, ref: any) => {
    const handleSave = async (param: any) => {
      try {
        const { data } = await analysisFilterEdit(param);
        if (data?.code === 0) {
        }
        return { code: data.code };
      } catch (error) {
        console.error("更新条件失败", error);
        return { code: -1 };
      }
    };

    const handleValueChange = (
      changedValues: any,
      allValues: any,
      valueList: any
    ) => {
      // 表单修改需要调用save
      handleSave({
        filterEditDTOList: valueList?.filter((item: any) => item?.id),
        sourceId: id,
        sourceType: type,
      }).then((res) => {
        if (res.code === 0) {
          // 请求列表
          onSaveCallback?.();
        }
      });
    };

    return (
      <CapsuleFilter
        filterText="数据过滤"
        bordered
        id={id}
        type={type}
        valuesChange={handleValueChange}
        showFilterTextSelect={true}
        ref={ref}
      />
    );
  }
);
FilterButton.displayName = "FilterButton";
export default FilterButton;
