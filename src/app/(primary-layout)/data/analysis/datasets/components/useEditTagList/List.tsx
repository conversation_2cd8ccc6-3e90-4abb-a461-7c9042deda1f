/*
 * @Date: 2025-05-20 18:59:35
 * @Author: miroku.yang
 * @Description: 编辑标签列表
 */
import React, { useEffect, useState, useRef } from "react";
import {
  Table,
  Spin,
  Input,
  message,
  InputNumber,
  Tooltip,
  Button,
} from "antd";
import {
  getMaterialSummarizeList,
  getMaterialSummarizeUpdate,
} from "./services";
import omit from "lodash/omit";
import isUndefined from "lodash/isUndefined";
import isNull from "lodash/isNull";
import styles from "./index.module.scss";
import clsx from "classnames";
import SourceMaterial from "../../../components/SourceMaterial";
import { getColumnSearchProps } from "./utils";
import { FILTER_OPTIONS, INITIAL_PAGINATION } from "./constant";

interface IProps {
  // 主题ID
  id: any;
}
const List = (props: IProps) => {
  const { id } = props;
  // 表格滚动容器高度需要减掉表头高度
  const tableHeaderHieght = 40,
    paddingLeft = 20,
    paddingBottom = 16,
    titleHeight = 48,
    scrollWidth = 10;
  const [dataSource, setDataSource] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [filters, setFilters] = useState<any>({});
  const [pagenation, setPagination] = useState<any>(INITIAL_PAGINATION);
  const [modalSize, setModalSize] = useState<any>({});
  const [fetching, setFetching] = useState(false);
  // 防止回车时触发两次保存
  const commitLockRef = useRef<{ [key: string]: boolean }>({});
  const originValueRef = useRef<{
    [cellKey: string]: { value: any; cellKey: string };
  }>({});

  const tableRef = useRef<any>(null);

  const fetchData = async (params?: any) => {
    try {
      const isLoading = params?.isLoading ?? true;
      const loadMore = params?.loadMore ?? false;
      setLoading(isLoading);
      const currentParams = omit(
        {
          ...pagenation,
          ...params,
          id,
        },
        ["total", "hasNext"]
      );
      const { data } = await getMaterialSummarizeList(currentParams);
      if (data?.code === 0) {
        const newItems = data?.result?.items || [];
        setPagination({
          ...pagenation,
          ...omit(params, [
            "selfIsRealRemark",
            "selfMaterialFormat",
            "selfMaterialPutTime",
            "selfMaterialType",
            "selfMaterialVideoDuration",
            "selfOsType",
            "selfTopic",
            "selfVendor",
          ]),
          total: data?.result?.total || 0,
          hasNext: data?.result?.hasNext || false,
        });
        setDataSource(loadMore ? [...dataSource, ...newItems] : newItems);
      }
      return { code: data.code };
    } catch (error) {
      console.error("获取数据失败", error);
      return { code: -1 };
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    // 在 Modal 打开后执行
    const modalBody = document.querySelector(
      ".edit-tag-modal .ant-modal-body"
    ) as HTMLElement;
    if (modalBody) {
      const width = modalBody.offsetWidth;
      const height = modalBody.offsetHeight;
      setModalSize({
        width: width,
        height: height,
      });
    }
    fetchData();
  }, []);

  // 单元格保存方法
  const handleCellSave = (
    record: any,
    idx: number,
    dataIndex: string,
    value: string
  ) => {
    try {
      getMaterialSummarizeUpdate({ [dataIndex]: value, ...record }).then(
        ({ data }) => {
          if (data?.code === 0) {
            const newData = [...dataSource];
            newData[idx][dataIndex] = value;
            setDataSource(newData);
            message.success("保存成功");
          }
        }
      );
    } catch (error) {
      console.error("保存失败", error);
    }
  };

  // 渲染可编辑单元格
  const renderDirectInput =
    (dataIndex: string, type: "text" | "number" = "text") =>
    (text: any, record: any, idx: number) => {
      const cellKey = `${record.selfFileName || idx}-${dataIndex}`;
      const handleCommit = (value: any) => {
        // 记录初始值
        const origin = originValueRef.current[cellKey];
        // 只处理当前单元格自己的提交
        if (!origin || origin.cellKey !== cellKey) return;
        const oldValue = origin.value;
        // 只有内容变更才提交,旧值为undefined或null时，value为""也不提交
        if (
          value === oldValue ||
          ((isUndefined(oldValue) || isNull(oldValue)) && value === "")
        )
          return;
        if (commitLockRef.current[cellKey]) return;
        commitLockRef.current[cellKey] = true;
        handleCellSave(record, idx, dataIndex, value);
        setTimeout(() => {
          commitLockRef.current[cellKey] = false;
        }, 300); // 300ms后允许再次提交
      };

      if (type === "number") {
        return (
          <InputNumber
            value={record[dataIndex]}
            min={0}
            step={1}
            className={clsx(styles["edit-input-number"], "h-full")}
            style={{ width: "100%" }}
            controls={false}
            variant="borderless"
            onFocus={() => {
              originValueRef.current[cellKey] = {
                value: record[dataIndex],
                cellKey,
              };
            }}
            onChange={(value) => {
              const newData = [...dataSource];
              newData[idx][dataIndex] = value;
              setDataSource(newData);
            }}
            onBlur={() => {
              handleCommit(record[dataIndex]);
            }}
            onPressEnter={(e) => {
              handleCommit(record[dataIndex]);
              (e.target as HTMLInputElement).blur();
            }}
            maxLength={11}
          />
        );
      }

      return (
        <Input
          value={record[dataIndex]}
          onFocus={() => {
            originValueRef.current[cellKey] = {
              value: record[dataIndex],
              cellKey,
            };
          }}
          onChange={(e) => {
            const newData = [...dataSource];
            newData[idx][dataIndex] = e.target.value;
            setDataSource(newData);
          }}
          onBlur={(e) => {
            handleCommit(e.target.value);
          }}
          onPressEnter={(e) => {
            handleCommit(e.currentTarget.value);
            e.currentTarget.blur();
          }}
          variant="borderless"
          className="h-full !pl-[0px] !pr-[0px]"
          maxLength={dataIndex == "selfTopic" ? 50 : 30}
          style={{ width: "100%" }}
        />
      );
    };

  // 筛选项事件
  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
    tableRef.current?.scrollTo?.({ top: 0 });
    fetchData({ ...INITIAL_PAGINATION, ...newFilters });
  };

  const columns: any = [
    {
      title: "Filename",
      dataIndex: "selfFileNameModified",
      key: "selfFileNameModified",
      width: "14%",
      fixed: "left",
      className: "!pl-[8px] !pr-[8px]",
      ellipsis: {
        showTitle: false,
      },
      render: (selfFileNameModified: string) => (
        <Tooltip placement="topLeft" title={selfFileNameModified}>
          {selfFileNameModified}
        </Tooltip>
      ),
    },
    {
      title: "素材URL",
      dataIndex: "materialUrlInfoList",
      key: "materialUrlInfoList",
      width: "12%",
      fixed: "left",
      className: "!pl-[8px] !pr-[8px]",
      render: (materialUrlInfoList: any[], record: any) => {
        materialUrlInfoList?.forEach?.((item: any) => {
          item.customerProductName = record.selfFileNameModified;
          item.fileInfo = {
            previewInfo: {
              resizeImageUrl: item.coverUrl,
            },
            url: item.url,
          };
        });
        return (
          <SourceMaterial materialList={materialUrlInfoList} showCard={2} />
        );
      },
    },
    {
      title: "Type（视频/平面）",
      dataIndex: "selfMaterialType",
      key: "selfMaterialType",
      width: "10%",
      className: "!pl-[8px] !pr-[8px] !pt-[0px] !pb-[0px]",
      ...getColumnSearchProps({
        dataIndex: "selfMaterialType",
        options: FILTER_OPTIONS,
        filters: filters,
        onChange: handleFilterChange,
      }),
      render: renderDirectInput("selfMaterialType"),
    },
    {
      title: "Remark（真人/非真人）",
      dataIndex: "selfIsRealRemark",
      key: "selfIsRealRemark",
      width: "11%",
      className: "!pl-[8px] !pr-[8px] !pt-[0px] !pb-[0px]",
      ...getColumnSearchProps({
        dataIndex: "selfIsRealRemark",
        options: FILTER_OPTIONS,
        filters: filters,
        onChange: handleFilterChange,
      }),
      render: renderDirectInput("selfIsRealRemark"),
    },
    {
      title: "Topic",
      dataIndex: "selfTopic",
      key: "selfTopic",
      width: "15%",
      className: "!pl-[8px] !pr-[8px] !pt-[0px] !pb-[0px]",
      ...getColumnSearchProps({
        dataIndex: "selfTopic",
        options: FILTER_OPTIONS,
        filters: filters,
        onChange: handleFilterChange,
      }),
      render: renderDirectInput("selfTopic"),
    },
    {
      title: "Format（尺寸）",
      dataIndex: "selfMaterialFormat",
      key: "selfMaterialFormat",
      width: "8%",
      className: "!pl-[8px] !pr-[8px] !pt-[0px] !pb-[0px]",
      ...getColumnSearchProps({
        dataIndex: "selfMaterialFormat",
        options: FILTER_OPTIONS,
        filters: filters,
        onChange: handleFilterChange,
      }),
      render: renderDirectInput("selfMaterialFormat"),
    },
    {
      title: "Duration（秒数）",
      dataIndex: "selfMaterialVideoDuration",
      key: "selfMaterialVideoDuration",
      width: "9%",
      className: "!pl-[8px] !pr-[8px] !pt-[0px] !pb-[0px]",
      ...getColumnSearchProps({
        dataIndex: "selfMaterialVideoDuration",
        options: FILTER_OPTIONS,
        filters: filters,
        onChange: handleFilterChange,
      }),
      render: renderDirectInput("selfMaterialVideoDuration", "number"),
    },
    {
      title: "Vendor（供应商）",
      dataIndex: "selfVendor",
      key: "selfVendor",
      width: "11%",
      ...getColumnSearchProps({
        dataIndex: "selfVendor",
        options: FILTER_OPTIONS,
        filters: filters,
        onChange: handleFilterChange,
      }),
      className: "!pl-[8px] !pr-[8px] !pt-[0px] !pb-[0px]",
      render: renderDirectInput("selfVendor"),
    },
    // {
    //   title: "客户端",
    //   dataIndex: "selfOsType",
    //   key: "selfOsType",
    //   width: "9%",
    //   className: "!pl-[8px] !pr-[8px] !pt-[0px] !pb-[0px]",
    //   ...getColumnSearchProps({
    //     dataIndex: "selfOsType",
    //     options: FILTER_OPTIONS,
    //     filters: filters,
    //     onChange: handleFilterChange,
    //   }),
    //   render: renderDirectInput("selfOsType"),
    // },
  ];

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    const scrollTop = target.scrollTop;
    const clientHeight = target.clientHeight;
    const scrollHeight = target.scrollHeight;
    // 距底 100px 时触发加载
    if (
      !fetching &&
      pagenation.hasNext &&
      scrollTop + clientHeight >= scrollHeight
    ) {
      setFetching(true);
      fetchData({
        pageNo: pagenation.pageNo + 1,
        isLoading: false,
        loadMore: true,
        ...filters,
      }).finally(() => {
        setFetching(false);
      });
    }
  };

  const handleReset = () => {
    setFilters({});
    tableRef.current?.scrollTo?.({ top: 0 });
    fetchData({ ...INITIAL_PAGINATION });
  };

  return (
    <div className="w-full h-full overflow-hidden relative">
      <div className=" flex items-center justify-between">
        <div className="mb-[16px] text-[14px] text-[#4E5969]">
          <span>素材识别依据：filename（素材名）</span>
          <span className="inline-block h-[32px] leading-[32px] bg-[#F2F3F5] rounded-[6px] pl-[16px] pr-[16px]">
            如：RELIABILITY_1X1_10S_ILLUS_STATIC_IDEA5_FBTT_V1_MAR25_SN.MP4
          </span>
        </div>
        <Button type="primary" onClick={handleReset}>
          重置筛选
        </Button>
      </div>
      <Table
        ref={tableRef}
        virtual
        columns={columns}
        dataSource={dataSource}
        loading={loading}
        pagination={false}
        rowKey={(record: any) => record.selfFileName}
        //  通过 virtual 开启虚拟滚动，此时 scroll.x 与 scroll.y 必须设置且为 number 类型
        scroll={{
          //   x: modalSize.width - 2 * paddingLeft - scrollWidth,
          x: 1760,
          y:
            modalSize.height -
            tableHeaderHieght -
            paddingBottom -
            titleHeight -
            2,
        }}
        className={styles["edit-tag-list-table"]}
        bordered
        onScroll={handleScroll}
      />
      {/* 底部加载中 */}
      {fetching && (
        <div
          className="col-span-full flex items-center justify-center py-4"
          style={{
            position: "absolute",
            left: 0,
            right: 0,
            bottom: 8,
            textAlign: "center",
            pointerEvents: "none",
          }}
        >
          <Spin size="small" />
          <span className="ml-2 text-gray-400">加载中...</span>
        </div>
      )}
    </div>
  );
};
export default List;
