/*
 * @Date: 2025-05-20 18:53:31
 * @Author: miroku.yang
 * @Description: 编辑标签列表
 */
import React from "react";
import { useModalWrap } from "@/components";
import { Table } from "antd";
import List from "./List";
import styles from "./index.module.scss";
import clsx from "classnames";

const useEditTagList = (props: any) => {
  const { onHideCallback } = props;
  const {
    showModal: showEditTagModal,
    hideModal,
    render: renderEditTagModal,
    form: any,
    visible,
  } = useModalWrap({
    modalProps: {
      title: "编辑标签字段",
      width: 1188,
      footer: null,
      className: clsx(styles["edit-tag-modal"], "edit-tag-modal"),
      afterClose: () => {
        onHideCallback?.();
      },
    },
    children: ({ showParams = {}, form }: any) => {
      return <List id={showParams.id} />;
    },
  });

  return {
    renderEditTagModal,
    showEditTagModal,
  };
};
export default useEditTagList;
