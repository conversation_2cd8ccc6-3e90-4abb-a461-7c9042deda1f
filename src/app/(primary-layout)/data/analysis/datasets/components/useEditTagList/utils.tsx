import { Button } from "antd";
import { cn } from "@/lib/utils";
import { FilterOutlined } from "@ant-design/icons";

function isNotEmpty(value: any) {
  if (typeof value === "boolean") {
    return true;
  }
  return value === 0 || !!value;
}

export const getColumnSearchProps = ({
  dataIndex,
  options,
  filters,
  onChange,
}: any) => {
  const value = filters?.[dataIndex];
  return {
    filterDropdown: ({ close }: any) => (
      <div onKeyDown={(e) => e.stopPropagation()}>
        <div className="flex flex-col p-1">
          {options?.map((item: any) => (
            <div
              key={item.value}
              className={cn(
                "h-[32px] rounded-[4px] hover:bg-[rgba(0,0,0,0.04)] transition-all duration-200 py-1 px-3 text-[14px] cursor-pointer user-select-none",
                {
                  "bg-[#e6f4ff] hover:bg-[#e6f4ff] text-[rgba(0,0,0,0.88)] font-semibold":
                    item.value === value,
                }
              )}
              onClick={() => {
                if (value !== item.value) {
                  onChange?.({ ...filters, [dataIndex]: item.value });
                  close?.();
                  return;
                }
                close?.();
              }}
            >
              {item.label}
            </div>
          ))}
        </div>
        <div className="px-2 py-[6px] border-t border-[rgb(240, 240, 240)] text-right">
          <Button
            onClick={() => {
              const newFilters = { ...filters };
              delete newFilters[dataIndex];
              onChange?.(newFilters);
              close?.();
            }}
            type="primary"
            size="small"
          >
            重置
          </Button>
        </div>
      </div>
    ),
    filterIcon: () => (
      <FilterOutlined style={{ color: isNotEmpty(value) ? "#1677ff" : undefined }} />
    ),
  };
};
