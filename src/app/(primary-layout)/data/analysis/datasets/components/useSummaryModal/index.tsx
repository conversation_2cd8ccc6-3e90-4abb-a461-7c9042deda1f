import React, { useState, useMemo, useEffect } from "react";
import { useModalWrap } from "@/components";
import { Input, Select, Button } from "antd";
import styles from "./index.module.scss";
import { getAnalysisAggConditionSave } from "../../services";

const AGG_OPTIONS = [
    { label: "求和", value: "SUM" },
    { label: "平均数", value: "AVG" },
];

const useSummaryModal = (props: any) => {
    const [currentParams, setCurrentParams] = useState<any>({});
    const [search, setSearch] = useState("");
    const [summaryConfig, setSummaryConfig] = useState<any>({});
    const { onSuccess, analysisId } = props;

    // 将summaryConfig转换为数组
    const summaryConfigArray = (obj: any) => {
        return Object.entries(obj).map(([key, value]) => ({
            code: key,
            aggType: value
        }));
    };

    const handleOk = async () => {
        try {
            const params = {
                userSubjectId: analysisId,
                aggConditionList: summaryConfigArray(summaryConfig)
            }
            const { data } = await getAnalysisAggConditionSave(params);
            if (data?.code === 0) {
                onSuccess(params, data);
                // 关闭弹窗
                return true;
            }
        } catch (error) {
            console.error('保存合计行报错', error);
            return false
        }
        return true;
    }

    const { columns = [], summaryAggConditionList = [] } = currentParams;

    // 搜索过滤
    const filteredColumns = useMemo(() => {
        if (!search) return columns;
        return columns.filter(
            (col: any) =>
                col.name?.toLowerCase().includes(search.toLowerCase()) ||
                col.code?.toLowerCase().includes(search.toLowerCase())
        );
    }, [search, columns]);

    const { showModal, render, visible } = useModalWrap({
        modalProps: {
            title: <div className="text-center">合计行</div>,
            width: 520,
            onOk: handleOk,
            className: styles.summaryModal,
            destroyOnClose: true,
        },
        children: () => {
            return (
                <div className={styles.summaryModalContent}>
                    <div className="p-[0px_30px_16px_30px] border-b border-[#e5e7eb]">
                        <Input
                            placeholder="搜索指标"
                            value={search}
                            onChange={(e) => setSearch(e.target.value)}
                            className='w-full'
                            allowClear
                        />
                    </div>
                    <div className="p-[0px_30px_10px_30px] mt-2 h-[469px] overflow-y-auto">
                        {filteredColumns.map((col: any) => (
                            <div className="flex items-center justify-between mb-2" key={col.code}>
                                <span className="flex-1 text-ellipsis overflow-hidden whitespace-nowrap" title={col.name}>
                                    {col.name}
                                </span>
                                <Select
                                    value={summaryConfig[col.code] || "SUM"}
                                    onChange={(val) =>
                                        setSummaryConfig((prev: any) => ({ ...prev, [col.code]: val }))
                                    }
                                    style={{ width: 90 }}
                                    options={AGG_OPTIONS}
                                />
                            </div>
                        ))}
                    </div>
                </div>
            );
        },
    });

    useEffect(() => {
        if (visible) {
            if (summaryAggConditionList?.length > 0) {
                // summaryAggConditionList 是数组，需要转换为对象
                const currentSummaryConfig = summaryAggConditionList.reduce((acc: any, item: any) => {
                    acc[item.code] = item.aggType;
                    return acc;
                }, {});
                setSummaryConfig(currentSummaryConfig);
            }else{
                setSummaryConfig({});
            }
        }
    }, [visible, summaryAggConditionList]);

    const handleShowModal = (showParams: any) => {
        setCurrentParams(showParams);
        showModal?.(showParams);
    };

    return {
        showSummaryModal: handleShowModal,
        renderSummaryModal: render,
    };
};

export default useSummaryModal;