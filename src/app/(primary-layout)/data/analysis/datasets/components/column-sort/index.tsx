/**
 * 选择列排序组件
 */

import React, { useState, useEffect } from "react";
import { Button, Dropdown, Select, Segmented } from "antd";
import clsx from "classnames";
import styles from "./index.module.scss";
import { getAnalysisSortEdit } from "./services";

export default function ColumnSort({
  options,
  onChange,
  className,
  value,
  viewId,
  type,
  onSortChange,
}: any) {
  // 默认按照spend_usd倒叙
  const [selectValue, setSelectValue] = useState({ code: "spend_usd" });
  const [segmentedValue, setSegmentedValue] = useState("desc");

  useEffect(() => {
    if (value?.code) {
      setSelectValue({ ...value });
      setSegmentedValue(value.orderByAsc ? "asc" : "desc");
    } else {
      setSelectValue({ code: "spend_usd" });
      setSegmentedValue("desc");
    }
  }, [value]);

  const onSave = async (currentValue: any) => {
    try {
      const params = {
        customSortEditDTO: currentValue,
        sourceId: viewId,
        sourceType: type,
      };
      const { data } = await getAnalysisSortEdit(params);
      return data;
    } catch (error) {
      console.error("排序保存失败", error);
      return { code: -1 };
    }
  };

  return (
    <Dropdown
      // destroyPopupOnHide
      trigger={["click"]}
      dropdownRender={() => {
        return (
          <div className="w-[482px] bg-white rounded-[8px] border border-solid border-[#E5E6EB] [box-shadow:0_4px_10px_rgba(0,0,0,0.1)] p-6">
            <div className="font-pingfang font-medium text-[#4E5969] leading-[22px] mb-4">
              设置排序条件<span className=" text-[12px]">（组内排序）</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex-1 overflow-hidden">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择"
                  options={options}
                  value={selectValue?.code}
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input: any, option: any) =>
                    (option?.label ?? "")
                      ?.toLowerCase()
                      ?.includes?.(input?.toLowerCase())
                  }
                  onChange={(a, b) => {
                    setSelectValue(b);
                    // 切换时默认正序
                    setSegmentedValue("asc");
                    onSave({
                      ...b,
                      orderByAsc: segmentedValue === "asc",
                    }).then((data) => {
                      if (data?.code == 0) {
                        onSortChange(viewId);
                      }
                    });
                  }}
                />
              </div>
              <div className="flex-1 overflow-hidden">
                {/* 只有当 selectValue.code 有值时才显示 Segmented */}
                {selectValue?.code && (
                  <Segmented
                    options={[
                      { label: "选择正序", value: "asc" },
                      { label: "选择倒序", value: "desc" },
                    ]}
                    value={segmentedValue}
                    onChange={(v: any) => {
                      setSegmentedValue(v);
                      onSave({
                        ...selectValue,
                        orderByAsc: v === "asc",
                      }).then((data) => {
                        if (data?.code == 0) {
                          onSortChange(viewId);
                        }
                      });
                    }}
                    className={styles["segmented-component"]}
                  />
                )}
              </div>
            </div>
            <div className="mt-[12px] ">
              <span
                className="rounded-[4px] hover:bg-[#f2f3f5] cursor-pointer py-[6px] px-[12px] transition-colors text-[#165DFF]"
                onClick={() => {
                  // 重置到spend_usd
                  setSelectValue({ code: "spend_usd" });
                  setSegmentedValue("desc");
                  onSave({
                    code: "spend_usd",
                    orderByAsc: false,
                  }).then((data) => {
                    if (data?.code == 0) {
                      onSortChange(viewId);
                    }
                  });
                }}
              >
                重置排序条件
              </span>
            </div>
          </div>
        );
      }}
    >
      <Button className={clsx(className)}>数据排序</Button>
    </Dropdown>
  );
}
