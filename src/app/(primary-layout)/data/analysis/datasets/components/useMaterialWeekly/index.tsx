/**
 * 动态列设置
 */
import * as React from "react";
import { message, Form, Input, Tooltip, Select } from "antd";
import { useModalWrap } from "@/components";
import SinoModalSymbolFont from "@/components/Icon/SinoModalSymbolFont";
import { genMasterialWeekly } from "../../services";
import find from "lodash/find";
import uniqBy from "lodash/uniqBy";

const { useEffect, useState } = React;

const FormItem = Form.Item;

function ModalBody({ subjectInfoId, form, aggregateList }: any) {
  useEffect(() => {
    if (subjectInfoId) {
      form.setFieldsValue({ subjectInfoId });
    }
  }, []);

  return (
    <div className="pt-6 pb-2">
      <FormItem
        label="聚合依据"
        name="code"
        rules={[{ required: true, message: "请选择" }]}
      >
        {/* <Input disabled value="filename（素材名）" /> */}
        <Select
          mode="multiple"
          showSearch
          allowClear
          style={{ width: "100%" }}
          placeholder="请选择"
          options={aggregateList}
          filterOption={(input: any, option: any) =>
            (option?.label ?? "")
              ?.toLowerCase?.()
              ?.includes?.(input?.toLowerCase?.())
          }
        />
      </FormItem>
      <FormItem
        label={
          <div className="flex items-center">
            <span className="mr-[2px]">聚合计算</span>
            <Tooltip title="基础指标根据当前设置的计算公式聚合，其余通过基础指标二次计算得到的字段，会基于公式和基础字段值的变化完成动态计算">
              <span>
                <SinoModalSymbolFont type="icon-xinxi" />
              </span>
            </Tooltip>
          </div>
        }
      >
        <Input disabled value="求和" />
      </FormItem>
      <FormItem
        label="视图名称"
        name="name"
        rules={[{ required: true, message: "请输入名称" }]}
      >
        <Input maxLength={50} showCount placeholder="请输入" />
      </FormItem>
      <FormItem noStyle name="subjectInfoId">
        <Input type="hidden" />
      </FormItem>
    </div>
  );
}

export default function useMaterialWeekly({
  aggregateList,
  onSaveCallback,
}: any) {
  const [currentParams, setCurrentParams] = useState<any>({});

  /**
   * 提交
   * @param params
   * @returns
   */
  const handleOk = async (values: any) => {
    // values.code 为["agency","self_material_put_time"], 根据code 和aggregateList 找options
    const options = values.code
      ?.map((code: string, codeIdx: number) => {
        const item = aggregateList.find((item: any) => item.code === code);
        // 聚合依据的index 将作为视图页面列的排序
        return item ? { ...item, index: codeIdx } : null;
      })
      ?.filter((item: any) => !!item?.code);
    values.groupInfoList = options?.length ? options : undefined;
    let newColumns = currentParams.columns || [];
    // 如果是视图页面进行创建，那么需要将columns中的partitionFlag 设置为false
    if (currentParams?.originType == "VIEW") {
      newColumns = newColumns?.map((item: any) => ({
        ...item,
        partitionFlag: false,
      }));
    }
    // 前端设置自定义列：将聚合依据作为细分条件
    values.customColumnEditDTOList = [
      ...values?.groupInfoList?.map((item: any, idx: number) => ({
        ...item,
        partitionFlag: true,
        selected: true,
        index: item?.index || idx,
      })),
      ...newColumns,
    ];

    if (currentParams?.originType == "VIEW") {
      // 如果是视图页面进行创建，那么选择的聚合依据有可能在columns 已经存在，需要过滤，以columns为准，仅将columns下包含的聚合依据的partitionFlag 和index进行替换
      // values.customColumnEditDTOList = values.customColumnEditDTOList?.map(
      //   (item: any) => {
      //     const columnItem = find(currentParams.columns, {
      //       code: item.code,
      //     });
      //     if (columnItem) {
      //       return {
      //         ...item,
      //         partitionFlag: columnItem.partitionFlag,
      //         index: columnItem.index,
      //       };
      //     }
      //     return item;
      //   }
      // );
      values.customColumnEditDTOList = uniqBy(
        values.customColumnEditDTOList,
        "code"
      );
    }
    delete values.code;
    try {
      const { data = {} } = await genMasterialWeekly(values);
      if (data?.code === 0) {
        message.success("操作成功");
        onSaveCallback?.(data);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  };

  const { showModal, render } = useModalWrap({
    modalProps: {
      title: currentParams.title || "生成数据报表",
      width: 520,
      styles: {
        header: {
          padding: "16px 24px",
          marginBottom: 0,
        },
        body: {
          padding: "0 24px",
        },
        footer: {
          padding: "16px 24px",
        },
      },
      onOk: handleOk,
    },
    formProps: {
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
    },
    children: ({ showParams, form }: any) => {
      return (
        <ModalBody
          subjectInfoId={showParams ? showParams?.subjectInfoId : null}
          form={form}
          aggregateList={aggregateList}
        />
      );
    },
  });

  const handleShowModal = (showParams: any) => {
    setCurrentParams(showParams);
    showModal?.(showParams);
  };

  return {
    showMaterialWeekly: handleShowModal,
    renderMaterialWeekly: render,
  };
}
