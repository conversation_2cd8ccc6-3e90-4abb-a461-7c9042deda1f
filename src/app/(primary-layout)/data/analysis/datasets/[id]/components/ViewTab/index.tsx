/*
 * @Date: 2025-05-19 11:54:24
 * @Author: miroku.yang
 * @Description: 视图tab 组件
 */
import React, { useRef, useState, useEffect, useMemo } from "react";
import styles from "./index.module.scss";
import {
  Tabs,
  Select,
  Input,
  Dropdown,
  Menu,
  Popover,
  message,
  Popconfirm,
  Form,
  InputNumber,
} from "antd";
import SinoSymbolFont from "@/components/Icon/SinoSymbolFont";
import clsx from "classnames";
import { MoreOutlined } from "@ant-design/icons";
import find from "lodash/find";
import findIndex from "lodash/findIndex";
import { useParams, usePathname } from "next/navigation";
import { useModalWrap } from "@/components";
import useMaterialWeekly from "../../../components/useMaterialWeekly";

type TargetKey = React.MouseEvent | React.KeyboardEvent | string;
interface IProps {
  renderChildren: (p: any) => React.ReactNode;
  fetchViewCreate: (params: any) => Promise<any>;
  fetchViewList: (params: any) => Promise<any>;
  fetchViewDelete: (params: any) => Promise<any>;
  tabs: any;
  updateValue: (key: string, value: any) => void;
  views: any;
  onTabChange: (key: string) => void;
  tableLoading: boolean;
  paginationObj: any;
  fetching: boolean;
  fetchDetailList: (params?: any) => Promise<any>;
  tableContainerSize: any;
  analysisInfo: any;
  fetchCacheAndDetailList: (tabKey: string) => Promise<any>;
  handleGroup: () => void;
  grouped: boolean;
  handleCopyView: () => void;
}

const ViewTab = (props: IProps) => {
  const {
    renderChildren,
    fetchViewCreate,
    fetchViewList,
    fetchViewDelete,
    tabs = [],
    updateValue,
    views,
    onTabChange,
    tableLoading,
    paginationObj,
    fetching,
    fetchDetailList,
    tableContainerSize,
    analysisInfo,
    fetchCacheAndDetailList,
    handleGroup,
    grouped,
    handleCopyView,
  } = props;
  const routerParams = useParams();
  const pathname = usePathname();
  const [activeKey, setActiveKey] = useState("");
  const [record, setRecord] = useState<any>({});
  const inputRef = useRef<any>(null);
  const [eidtor, setEidtor] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [isCustom, setIsCustom] = useState(false); // 控制是否显示输入框
  const deleteShowParams = useRef<any>(null);

  const onChange = (newActiveKey: string) => {
    setActiveKey(newActiveKey);
    // 重新请求
    onTabChange?.(newActiveKey);
  };

  const addSuccess = (res: any) => {
    // res.result 返回的是新建视图的id
    // message.success("新建视图成功");
    // 新建或者编辑成功后刷新tabs列表
    fetchViewList({ id: Number(routerParams.id) }).then((viewRes: any) => {
      if (viewRes?.code === 0) {
        // 选中新建的视图tab
        const currentTab = find(viewRes.result, { id: res.result }) || {};
        if (currentTab.id) {
          setActiveKey(currentTab.id);
          // 这里无需筛选列表，在page 页面有监听tabkey 的变化请求，所以会自动请求
        } else {
          setActiveKey(viewRes.result[0]?.id);
        }
      }
    });
  };
  // 新建视图
  const add = (name: string) => {
    try {
      fetchViewCreate({
        name: name,
        subjectInfoId: Number(routerParams.id),
      }).then((res) => {
        if (res?.code === 0) {
          addSuccess(res);
        }
      });
    } catch (error) {
      console.error("新建视图失败", error);
    }
  };

  const aggregateList = useMemo(() => {
    return [
      ...(analysisInfo?.basisDimensionInfoList || []),
      ...(analysisInfo?.partitionDimensionInfoList || []),
    ]?.map((item: any) => ({ ...item, label: item.name, value: item.code }));
  }, [analysisInfo]);

  const { showModal: showDeleteViewModal, render: renderDeleteViewModal } =
    useModalWrap({
      modalProps: {
        width: 400,
        title: "删除视图",
        className: styles["delete-modal-wrap"],
        onOk: () => {
          remove(deleteShowParams.current?.key, deleteShowParams.current?.tabs); // 调用删除逻辑
        },
      },
      children: ({ showParams = {} }: any) => (
        <div>{`确认要删除视图“${showParams.name}”吗？`}</div>
      ),
    });

  // 生成数据报表
  const { showMaterialWeekly, renderMaterialWeekly } = useMaterialWeekly({
    aggregateList,
    onSaveCallback: (data: any) => {
      addSuccess(data);
    },
  });

  // 删除视图
  const remove = (targetKey: TargetKey, beforeDeleteTabs: any) => {
    try {
      fetchViewDelete({
        id: targetKey,
      }).then((res) => {
        if (res?.code === 0) {
          message.success("删除视图成功");
          // 删除成功后刷新tabs列表
          fetchViewList({ id: Number(routerParams.id) }).then(
            (viewRes: any) => {
              if (viewRes?.code === 0) {
                const currentTabIndex = findIndex(beforeDeleteTabs, {
                  key: targetKey,
                });
                // 判断选中逻辑
                let preTab;
                if (currentTabIndex === 0) {
                  // 如果是第一个，选中下一个
                  preTab = beforeDeleteTabs[currentTabIndex + 1];
                } else {
                  // 如果是后面的，选中前一个
                  preTab = beforeDeleteTabs[currentTabIndex - 1];
                }
                if (preTab) {
                  setActiveKey(preTab.id);
                }
              }
            }
          );
        }
      });
    } catch (error) {
      console.error("删除视图失败", error);
    }
  };

  // 复制表格
  // const handleCopy = (item: any) => {
  //   handleCopyView?.();
  // };

  // 重命名
  const handleRenameConfirm = (item: any) => {
    setEidtor(false);
    try {
      fetchViewCreate({
        name: item.name,
        id: item.id,
        isEdit: true,
      }).then((res) => {
        if (res?.code === 0) {
          // res.result 返回的是新建视图的id
          message.success("编辑视图成功");
          // 新建或者编辑成功后刷新tabs列表
          fetchViewList({ id: Number(routerParams.id) }).then(
            (viewRes: any) => {
              if (viewRes?.code === 0) {
                // 选中编辑的视图tab
                const currentTab = find(viewRes.result, { id: item.id }) || {};
                if (currentTab) {
                  setActiveKey(currentTab.id);
                } else {
                  setActiveKey(viewRes.result[0]?.id);
                }
              }
            }
          );
        }
      });
    } catch (error) {
      console.error("编辑视图失败", error);
    }
  };

  useEffect(() => {
    // 仅数据报表才请求视图列表
    if (!routerParams.id) return;
    fetchViewList({
      id: Number(routerParams.id),
    });
  }, [routerParams.id]);

  useEffect(() => {
    setActiveKey(tabs[0]?.key);
  }, [pathname, tabs]);

  useEffect(() => {
    if (eidtor) {
      setTimeout(() => {
        inputRef.current?.focus();
        inputRef.current?.select();
      }, 0);
    }
  }, [eidtor]);

  useEffect(() => {
    updateValue("tabKey", activeKey);
  }, [activeKey, updateValue]);

  const menuItems = (node: any) => {
    const item = tabs.find((i:any) => i.key == node.key);
    return [
      {
        key: "rename",
        icon: <SinoSymbolFont
          type="icon-bianji"
          className={clsx("mr-[4px]")}
          style={{ fontSize: 14 }}
        />,
        label: "重命名",
        onClick: (e: any) => {
          setEidtor(true);
          setRecord(item);
          e.domEvent.stopPropagation();
        },
      },
      {
        key: "delete",
        icon: <SinoSymbolFont
          type="icon-delete"
          className={clsx("mr-[4px]")}
          style={{ fontSize: 14 }}
        />,
        label: "删除",
        onClick: (e: any) => {
          e.domEvent.stopPropagation();
          // 保存数据
          deleteShowParams.current = { ...item, tabs: tabs };
          showDeleteViewModal(item);
        },
      },
    ]
  }

  // 自定义 tab 渲染
  const renderTabBar = (props: any, DefaultTabBar: any) => (
    <DefaultTabBar {...props}>
      {(node: any) => {
        const item = tabs.find((i) => i.key == node.key);
        if (!item) return node;
        if (eidtor && record.key == item.key) {
          return React.cloneElement(
            node,
            {},
            <span style={{ display: "flex", alignItems: "center" }}>
              <Input
                value={record.label}
                style={{ width: 150, marginRight: 4 }}
                onChange={(e) =>
                  setRecord({ ...record, label: e.target.value })
                }
                onClick={(e) => e.stopPropagation()}
                onBlur={(e: any) =>
                  handleRenameConfirm({
                    ...item,
                    name: e.target.value,
                    label: e.target.value,
                  })
                }
                onKeyDown={(e: any) => {
                  if (e.key === "Enter")
                    handleRenameConfirm({
                      ...item,
                      name: e.target.value,
                      label: e.target.value,
                    });
                  if (e.key === "Escape") setEidtor(false);
                }}
                className={styles["rename-input"]}
                ref={inputRef}
                maxLength={50}
              />
            </span>
          );
        }
        return React.cloneElement(
          node,
          {},
          <span style={{ display: "flex", alignItems: "center" }}>
            <span>{item.label}</span>
            {node.key == activeKey ? (
              <Dropdown
                trigger={["click"]}
                menu={{
                  items: menuItems(node)
                }}
                placement="bottomLeft"
              >
                <span className={clsx("ml-[4px]", styles["more-icon"])}>
                  <MoreOutlined
                    style={{
                      fontSize: 16,
                      color: "#999",
                      cursor: "pointer",
                    }}
                  />
                </span>
              </Dropdown>
            ) : null}
          </span>
        );
      }}
    </DefaultTabBar>
  );

  return (
    <>
      <Tabs
        type="editable-card"
        onChange={onChange}
        activeKey={activeKey}
        items={tabs}
        className={styles["view-tab"]}
        renderTabBar={renderTabBar}
        addIcon={
          // <Popconfirm
          //   icon={null}
          //   title="视图名称："
          //   description={
          //     <Form form={form}>
          //       <Form.Item
          //         label={null}
          //         name={"name"}
          //         rules={[{ message: "请输入视图名称", required: true }]}
          //       >
          //         <Input placeholder="请输入" maxLength={50} />
          //       </Form.Item>
          //     </Form>
          //   }
          //   onConfirm={() => {
          //     return form.validateFields().then(async (values) => {
          //       add(values.name);
          //       return false;
          //     });
          //   }}
          //   onOpenChange={() => {
          //     form.resetFields();
          //   }}
          //   okText="确定"
          //   cancelText="取消"
          //   placement="bottomLeft"
          //   arrow={false}
          // >
          <span
            className="whitespace-nowrap"
            onClick={() =>
              showMaterialWeekly({
                title: "新建视图",
                subjectInfoId: Number(routerParams.id),
                columns:analysisInfo?.customViewSelectedInfoList || [],
                originType: "VIEW"
              })
            }
          >
            <SinoSymbolFont
              type="icon-quzhizuo"
              className={clsx("mr-[12px]", styles["back-icon"])}
            />
            <span>新建视图</span>
          </span>
          // </Popconfirm>
        }
        tabBarGutter={0}
      // tabBarExtraContent={
      //   <div>
      //     展示数据：
      //     <Select
      //       className="w-[100px]"
      //       defaultValue="1"
      //       onChange={(value) => {
      //         // 如果选择“自定义”，显示输入框
      //         setIsCustom(value === "2");
      //         // updateValue("paramType", value);
      //         // // 切换视图类型时，重置分页
      //         // updateValue("paginationObj", {
      //         //   pageNo: 1,
      //         //   pageSize: paginationObj.pageSize,
      //         //   total: 0,
      //         //   hasMore: false,
      //         // });
      //       }}
      //     >
      //       <Select.Option value="1">全部</Select.Option>
      //       <Select.Option value="2">自定义</Select.Option>
      //     </Select>
      //     {isCustom && (
      //       <InputNumber
      //         className="!ml-[8px] w-[200px]"
      //         placeholder="请输入自定义内容"
      //         min={1} // 设置最小值为 1
      //         max={200} // 设置最大值为 200
      //         onChange={(value) => {
      //           console.log("自定义输入内容：", value);
      //           // 在这里可以处理输入框的值
      //         }}
      //       />
      //     )}
      //   </div>
      // }
      // tabBarExtraContent={
      //   <div style={{ fontSize: 12, color: "#666", marginRight: 16 }}>
      //     最大可展示数量500条， 共{paginationObj?.total ?? 0}条， 已加载
      //     {views?.length ?? 0}条
      //   </div>
      // }
      />
      {tabs.map((item: any) => (
        <div key={item.id}>
          {item.key == activeKey
            ? renderChildren({
              activeKey,
              tab: item,
              views,
              tableLoading,
              paginationObj,
              fetching,
              fetchDetailList,
              tableContainerSize,
              updateValue,
              analysisInfo,
              fetchCacheAndDetailList,
              handleGroup,
              grouped,
              handleCopyView,
            })
            : null}
        </div>
      ))}
      <div style={{ height: 0, overflow: "hidden" }}>
        {renderDeleteViewModal()}
        {/* 新建视图 */}
        {renderMaterialWeekly()}
      </div>
    </>
  );
};
export default ViewTab;
