import { Table, Button } from "antd";
import React, { useState } from "react";

// 示例数据
const rawData = [
  {
    campaign: "Liz Linen Drape Pleated",
    age: "18-24",
    gender: "女性",
    ad: "广告1",
    status: "未投放",
  },
  {
    campaign: "Liz Linen Drape Pleated",
    age: "18-24",
    gender: "女性",
    ad: "广告2",
    status: "未投放",
  },
  {
    campaign: "Liz Linen Drape Pleated",
    age: "18-24",
    gender: "男性",
    ad: "广告3",
    status: "已暂停",
  },
  {
    campaign: "Liz Linen Drape Pleated",
    age: "25-34",
    gender: "女性",
    ad: "广告4",
    status: "已暂停",
  },
];

// 计算 rowSpan 辅助函数
function getMergedData(data, keys) {
  const mergedData = data.map((item) => ({ ...item }));
  keys.forEach((key, keyIdx) => {
    let last = null;
    let count = 0;
    for (let i = 0; i < mergedData.length; i++) {
      if (!last || mergedData[i][key] !== last) {
        // 新分组
        let rowSpan = 1;
        for (let j = i + 1; j < mergedData.length; j++) {
          let same = true;
          for (let k = 0; k <= keyIdx; k++) {
            if (mergedData[j][keys[k]] !== mergedData[i][keys[k]]) {
              same = false;
              break;
            }
          }
          if (same) rowSpan++;
          else break;
        }
        mergedData[i][`${key}RowSpan`] = rowSpan;
        last = mergedData[i][key];
      } else {
        mergedData[i][`${key}RowSpan`] = 0;
      }
    }
  });
  return mergedData;
}

const columns = [
  {
    title: "广告系列名称",
    dataIndex: "campaign",
    render: (text, row, index) => ({
      children: text,
      props: { rowSpan:row.campaignRowSpan },
      // props: { rowSpan: index==0?4:0 },
    }),
  },
  {
    title: "年龄",
    dataIndex: "age",
    // render: (text, row, index) => ({
    //   children: text,
    //   props: { rowSpan: row.ageRowSpan },
    // }),
  },
  {
    title: "性别",
    dataIndex: "gender",
    // render: (text, row, index) => ({
    //   children: text,
    //   props: { rowSpan: row.genderRowSpan },
    // }),
  },
  {
    title: "广告创意",
    dataIndex: "ad",
    render(text:any) {
      return (
        <span style={{ color: "#1677ff", cursor: "pointer" }}>
          查看广告创意{text}
        </span>
      );
    }
  },
  {
    title: "投放状态",
    dataIndex: "status",
  },
];

export default function DemoTable() {
    const [data, setData] = useState(rawData);
    const [grouped, setGrouped] = useState(false);
  
    const handleGroup = () => {
      if (!grouped) {
        const newData = getMergedData(rawData, ["campaign", "age", "gender"]);
        console.log("分组后的数据：", newData);
        setData(newData);
      } else {
        setData(rawData);
      }
      setGrouped(!grouped);
    };
  
    // 动态columns，未分组时不合并单元格
    const mergedColumns = columns.map(col => {
      if (!grouped && col.render) {
        return { ...col, render: undefined };
      }
      return col;
    });
  
    return (
      <div>
        <Button onClick={handleGroup} type="primary" style={{ marginBottom: 16 }}>
          {grouped ? "取消组合细分条件" : "组合细分条件"}
        </Button>
        <Table
          // virtual
          columns={mergedColumns}
          dataSource={data}
          rowKey={(row, idx) => idx}
          pagination={false}
          bordered
          scroll={{
            x: columns?.length > 10 ? 120 * columns?.length : 1200, // 120 是每列的宽度
            y: 600,
          }}
        />
      </div>
    );
  }