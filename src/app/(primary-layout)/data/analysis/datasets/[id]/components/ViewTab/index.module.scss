.view-tab {
  :global {
    .ant-tabs-nav {
      margin-bottom: 0 !important;
    }

    .ant-tabs-nav-wrap {
      &::before {
        box-shadow: none !important;
      }
    }

    .ant-tabs-tab {
      border-color: transparent !important;
      position: relative;
      background: transparent !important;

      &::after {
        content: "";
        position: absolute;
        bottom: 10px;
        left: 0;
        height: 20px;
        width: 1px;
        background-color: #e5e6eb;
      }

      &:first-child {
        &::after {
          content: none;
        }
      }
    }

    .ant-tabs-tab-active {
      border-top: 1px solid #e5e6eb !important;
      border-right: 1px solid #e5e6eb !important;
      border-left: 1px solid #e5e6eb !important;
      background: #fff !important;

      &::after {
        content: none;
      }

      & + .ant-tabs-tab {
        &::after {
          content: none;
        }
      }
    }

    .ant-tabs-tab-focus {
      outline: none !important;
    }

    .ant-tabs-nav-add {
      border: none !important;
    }
  }

  .rename-input {
    height: 22px;
    &::selection {
      background: #0055fe;
    }
  }
}
.more-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;

  &:hover {
    background: #edeff2;
    border-radius: 4px;
  }
}

.delete-modal-wrap{
  :global{
    .ant-modal-header{
      padding:30px 30px 12px 30px!important;
    }
  }
}
