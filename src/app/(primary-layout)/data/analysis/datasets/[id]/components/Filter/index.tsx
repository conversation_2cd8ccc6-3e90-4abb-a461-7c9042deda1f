/*
 * @Date: 2025-05-19 14:28:59
 * @Author: miroku.yang
 * @Description: 筛选条件汇总
 */
import React, { useMemo, useState } from "react";
import { <PERSON><PERSON>, Tooltip } from "antd";
import CapsuleFilter from "@/app/(primary-layout)/data/analysis/components/CapsuleFilter";
import { getCreativeAutoAnalysisFilterEdit } from "../../../../../services";
import useDynamicColumnSetting from "../../../../datasets/components/useDynamicColumnSetting";
import ColumnSort from "@/app/(primary-layout)/data/analysis/datasets/components/column-sort";
import { CheckOutlined, CopyOutlined } from "@ant-design/icons"; // 复制icon
import { useParams } from "next/navigation";

interface IProps {
  viewId: number; // 主题id 或者 视图id
  type: string;
  fetchCacheAndDetailList: (params?: any) => Promise<any>; // 用于更新数据
  updateValue: (key: string, value: any) => void; // 用于更新数据
  paginationObj: any; // 分页对象
  handleGroup: (params?: any) => void; // 用于更新分组
  grouped: boolean; // 是否分组
  views: any[]; // 视图列表数据
  analysisInfo: any; // 分析主题或视图的缓存信息
  tableLoading: boolean;
  handleCopyView: () => void;
}

// 需要展示文本类型下拉的code
const showFilterTextSelectCodes = [
  "self_file_name_modified",
  "self_topic",
  "self_material_type",
  "self_material_format",
  "self_material_video_duration",
  "self_is_real_remark",
  "self_os_type",
  "self_vendor",
];

const Filter = (props: IProps) => {
  const {
    viewId,
    type,
    fetchCacheAndDetailList,
    updateValue,
    paginationObj,
    handleGroup,
    grouped,
    views,
    analysisInfo,
    tableLoading,
    handleCopyView,
  } = props;
  const routerParams = useParams();
  const [copied, setCopied] = useState(false);

  const sortOptions = useMemo(() => {
    // 获取缓存的过滤条件
    const currentBasisDimensionInfoList = (
      analysisInfo.basisDimensionInfoList || []
    ).filter((item: any) => item.dataType == "date");

    const showFilterTextSelectCodesToOptions =
      analysisInfo?.basisDimensionInfoList?.filter?.((item: any) =>
        showFilterTextSelectCodes.includes(item.code)
      ) || [];
    return [
      ...currentBasisDimensionInfoList,
      ...(analysisInfo.basisIndicatorInfoList || []),
      ...(analysisInfo.indicatorInfoList || []),
      ...(showFilterTextSelectCodesToOptions || []),
      ...(analysisInfo.partitionDimensionInfoList || []),
    ]?.map((m: any) => ({
      ...m,
      label: m.name,
      value: m.code,
      key: m.code,
      dateGranularity: analysisInfo.dateGranularity,
    }));
  }, [analysisInfo]);

  const mergeKeys = useMemo(() => {
    // 获取缓存的过滤条件，其实只有basisDimensionInfoList 维度里面才可以进入分区
    const allList = analysisInfo?.selectedInfoList?.length
      ? analysisInfo?.selectedInfoList
      : [
          ...(analysisInfo.basisDimensionInfoList || []),
          ...(analysisInfo.basisIndicatorInfoList || []),
          ...(analysisInfo.indicatorInfoList || []),
          ...(analysisInfo.partitionDimensionInfoList || []),
        ];
    return (
      allList
        .filter((item: any) => item.partitionFlag)
        ?.map((item: any) => item.code) || []
    );
  }, [analysisInfo]);

  const handleSave = async (param: any) => {
    try {
      const { data } = await getCreativeAutoAnalysisFilterEdit(param);
      if (data?.code === 0) {
      }
      return { code: data.code };
    } catch (error) {
      console.error("更新条件失败", error);
      return { code: -1 };
    }
  };

  const handleValueChange = (
    changedValues: any,
    allValues: any,
    valueList: any
  ) => {
    // 表单修改需要调用save
    handleSave({
      filterEditDTOList: valueList?.filter((item: any) => item?.id),
      sourceId: viewId,
      sourceType: type,
    }).then((res) => {
      if (res.code === 0) {
        if (grouped) {
          updateValue("grouped", false);
        }
        // 请求列表
        fetchCacheAndDetailList(viewId);
      }
    });
  };

  // 获取缓存数据//请求缓存的信息
  const currentId = routerParams.id ? Number(routerParams.id) : undefined;
  const { showColumnSetting, renderColumnSetting } = useDynamicColumnSetting({
    editId: currentId,
    editType: "USER_SUBJECT",
    onSaveCallback: () => {
      if (grouped) {
        updateValue("grouped", false);
      }
      // 请求列表
      fetchCacheAndDetailList(viewId);
    },
  });

  const disabledGroupButton = useMemo(() => {
    // 禁用组合细分条件按钮的逻辑
    return tableLoading || !views?.length || !mergeKeys.length;
  }, [tableLoading, views, mergeKeys]);

  const disabledGroupButtonTitle = useMemo(() => {
    if (disabledGroupButton) {
      if (!views?.length) {
        return "暂无数据无法设置细分条件";
      }
      if (!mergeKeys?.length) {
        return "请先在自定义列中设置细分条件";
      }
    } else {
      return null;
    }
  }, [disabledGroupButton, views, mergeKeys]);

  const onSortChange = (viewParamId: number) => {
    if (grouped) {
      updateValue("grouped", false);
    }
    // 请求列表
    fetchCacheAndDetailList(viewParamId);
  };

  const handleCopyClick = () => {
    handleCopyView();
    setCopied(true);
    setTimeout(() => setCopied(false), 3000);
  };

  return (
    <div className="flex items-center justify-between">
      <div className="mb-[12px] flex items-center">
        <CapsuleFilter
          filterText="数据结果过滤"
          className="mr-[10px]"
          bordered
          id={viewId}
          type={type}
          valuesChange={handleValueChange}
        />
        <ColumnSort
          options={sortOptions}
          className="mr-[10px]"
          value={
            analysisInfo?.customSortEditDTO
              ? analysisInfo.customSortEditDTO
              : undefined
          }
          viewId={viewId}
          type={type}
          onSortChange={onSortChange}
        />
        <Button
          className="mr-[10px]"
          onClick={(e) => {
            e.stopPropagation();
            // type: 数据类型 USER_SUBJECT：用户分析主题、VIEW：视图,可用值:DATA_SOURCE,USER_SUBJECT,VIEW
            showColumnSetting({
              id: viewId,
              type: type,
            });
          }}
        >
          自定义列
        </Button>
        {/* <Button onClick={handleGroup}>组合细分条件</Button> */}
        {/* <Tooltip title={disabledGroupButtonTitle}>
          <Button
            type="primary"
            onClick={handleGroup}
            disabled={disabledGroupButton}
          >
            {grouped ? "取消组合细分条件" : "组合细分条件"}
          </Button>
        </Tooltip> */}

        {/* 渲染自定义列 */}
        <div style={{ height: 0, overflow: "hidden" }}>
          {renderColumnSetting()}
        </div>
      </div>
      <div className="mb-[12px]">
        <Button
          type="primary"
          onClick={handleCopyClick}
          icon={
            copied ? (
              <CheckOutlined style={{ color: "#52c41a" }} />
            ) : (
              <CopyOutlined />
            )
          }
        >
          复制视图数据
        </Button>
      </div>
    </div>
  );
};
export default Filter;
