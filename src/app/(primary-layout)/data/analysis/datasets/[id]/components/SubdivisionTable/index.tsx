import { Table, But<PERSON>, Spin, message, Tooltip } from "antd";
import React, {
  useEffect,
  useMemo,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import styles from "./index.module.scss";
import SourceMaterial from "@/app/(primary-layout)/data/analysis/components/SourceMaterial";
import clsx from "classnames";
import { columnsSort } from "@/app/(primary-layout)/data/analysis/datasets/utils";
import { changeAnalysisInfoToParams } from "../../util";
import { v4 as uuidv4 } from "uuid";
interface IProps {
  views: any;
  tableLoading: boolean;
  fetching: boolean;
  paginationObj: any;
  updateValue: (key: string, value: any) => void;
  fetchDetailList: (params?: any) => Promise<any>;
  tableContainerSize: any;
  analysisInfo: any;
  viewId: number | undefined; // 主题id 或者 视图id
  grouped: boolean; // 是否分组
}

function getSortList(analysisInfo: any) {
  // 获取缓存的过滤条件
  const {
    selectedInfoList,
    basisDimensionInfoList,
    basisIndicatorInfoList,
    indicatorInfoList,
    partitionDimensionInfoList,
  } = analysisInfo || {};

  if (Array.isArray(selectedInfoList) && selectedInfoList.length > 0) {
    return columnsSort(selectedInfoList);
  }
  return columnsSort([
    ...(basisDimensionInfoList || []),
    ...(basisIndicatorInfoList || []),
    ...(indicatorInfoList || []),
    ...(partitionDimensionInfoList || []),
  ]);
}

// 简单的行合并，所有相同的值都合并
function getMergedData1(data: any, keys: any) {
  if (!data || !data.length || !keys.length) return data;
  const mergedData = data.map((item: any) => ({ ...item }));
  keys.forEach((key: string) => {
    let last = null;
    for (let i = 0; i < mergedData.length; i++) {
      if (mergedData[i][key] !== last) {
        // 新分组，统计有多少行内容相同
        let rowSpan = 1;
        for (let j = i + 1; j < mergedData.length; j++) {
          if (mergedData[j][key] === mergedData[i][key]) {
            rowSpan++;
          } else {
            break;
          }
        }
        mergedData[i][`${key}RowSpan`] = rowSpan;
        last = mergedData[i][key];
        // 后续相同的都设为0
        for (let k = 1; k < rowSpan; k++) {
          mergedData[i + k][`${key}RowSpan`] = 0;
        }
      }
    }
  });
  return mergedData;
}

// 联合合并，后面的列要依据前面的列进行合并
// 例如：如果第一列的值相同，那么第二列的值也要相同，才会合并
function getMergedData(data: any, keys: any) {
  if (!data || !data.length || !keys.length) return data;
  const mergedData = data.map((item:any) => ({ ...item }));
  // 对每一列都处理 rowSpan
  keys.forEach((key:any, keyIdx:any) => {
    let prevValues = null;
    let start = 0;
    for (let i = 0; i < mergedData.length; i++) {
      // 当前行前 keyIdx+1 列的值
      const curValues = keys
        .slice(0, keyIdx + 1)
        .map((col:any) => mergedData[i][col])
        .join("||");
      if (curValues !== prevValues) {
        // 新分组，统计有多少行内容相同
        let rowSpan = 1;
        for (let j = i + 1; j < mergedData.length; j++) {
          const nextValues = keys
            .slice(0, keyIdx + 1)
            .map((col:any) => mergedData[j][col])
            .join("||");
          if (nextValues === curValues) {
            rowSpan++;
          } else {
            break;
          }
        }
        mergedData[i][`${key}RowSpan`] = rowSpan;
        prevValues = curValues;
      } else {
        mergedData[i][`${key}RowSpan`] = 0;
      }
    }
  });
  return mergedData;
}

const SubdivisionTable = (props: IProps, ref: any) => {
  const {
    views,
    tableLoading,
    fetching,
    paginationObj,
    updateValue,
    fetchDetailList,
    tableContainerSize,
    analysisInfo,
    viewId,
    grouped,
  } = props;

  const [data, setData] = useState(views || []);
  // const [grouped, setGrouped] = useState(false);
  const [mergeKeys, setMergeKeys] = useState<any>([]); // 用于行合并的key
  const [allList, setAllList] = useState<any>([]);

  useEffect(() => {
    if (analysisInfo) {
      const allList: any = getSortList(analysisInfo);
      const currentMergeKeys =
        allList
          .filter((item: any) => item.partitionFlag)
          ?.map((item: any) => item.code) || [];
      setMergeKeys(currentMergeKeys);
      setAllList(allList);
    }
  }, [analysisInfo]);

  const columns = useMemo(() => {
    const result: any[] = [];
    allList.forEach((item: any) => {
      const col = {
        ...item,
        title: item.name,
        ellipsis: true,
        dataIndex: item.code,
        className: "!pl-[8px] !pr-[8px] !pt-[16px] !pb-[16px]",
      };
      // 只对第一列做行合并
      if (mergeKeys.includes(item.code)) {
        col.onCell = (record: any) => ({ rowSpan: record[`${item.code}RowSpan`] })
      }
      result.push(col);
      if (item.code === "self_file_name_modified") {
        // 只插入“素材”列，不要再 push col
        const title = "素材";
        result.push({
          title: title,
          dataIndex: "material",
          key: "material",
          ellipsis: true,
          className: clsx(
            "!pl-[8px] !pr-[8px] !pt-[0px] !pb-[0px]",
            styles["material-column"]
          ),
          render: (text: string, record: any) => {
            const materialUrlInfoList = record.materialUrlInfoList || [];
            materialUrlInfoList?.forEach?.((item: any) => {
              item.customerProductName = record["self_file_name_modified"];
              item.fileInfo = {
                previewInfo: {
                  resizeImageUrl: item.coverUrl,
                },
                url: item.url,
              };
            });
            return <SourceMaterial
              materialList={materialUrlInfoList}
              showCard={2}
              modalTitle={title}
            />;
          },
        });
        // 新增“素材URL”列
        // result.push({
        //   title: "素材URL",
        //   dataIndex: "material_url",
        //   key: "material_url",
        //   ellipsis: {
        //     showTitle: false,
        //   },
        //   className: clsx(
        //     "!pl-[8px] !pr-[8px] !pt-[0px] !pb-[0px]",
        //     styles["material-column"]
        //   ),
        //   render: (text: string, record: any) => {
        //     const materialUrlInfoList = record.materialUrlInfoList || [];
        //     // 拼接所有URL用于tooltip展示
        //     const allUrls = materialUrlInfoList
        //       .map((item: any) => item.url)
        //       .join("\n");
        //     return {
        //       children: (
        //         <Tooltip
        //           title={
        //             <div
        //               style={{
        //                 maxWidth: 400,
        //                 whiteSpace: "pre-line",
        //                 wordBreak: "break-all",
        //               }}
        //             >
        //               {allUrls}
        //             </div>
        //           }
        //           rootClassName={styles["material-url-tooltip"]}
        //         >
        //           <div className="line-clamp-3 break-all">
        //             {materialUrlInfoList.map((item: any, idx: number) => (
        //               <div key={idx}>
        //                 <a
        //                   href={item.url}
        //                   target="_blank"
        //                   rel="noopener noreferrer"
        //                 >
        //                   {item.url}
        //                 </a>
        //               </div>
        //             ))}
        //           </div>
        //         </Tooltip>
        //       ),
        //     };
        //   },
        // });
      }
    });
    return result;
  }, [allList, mergeKeys]);

  const renderRowKey = (list:any = []) => {
    if(Array.isArray(list)){
      return list.map((item: any,index:number) => ({ ...item, rowKey: `${uuidv4().replace(/-/g, "")}${index}` }));
    }
    return list;
  }

  useEffect(() => {
    setData(renderRowKey(getMergedData(views || [], mergeKeys)));
  }, [views, mergeKeys]);

  const handleGroup = () => {
    if (!grouped) {
      const newData = getMergedData(data, mergeKeys);
      setData(renderRowKey(newData));
    } else {
      setData(renderRowKey(data));
    }
    // setGrouped(!grouped);
  };

  // 动态columns，未分组时不合并单元格
  const mergedColumns = columns.map((col) => {
    // 确保素材列的 render 不被移除
    if (!grouped && col.render && !["material"].includes(col.dataIndex)) {
      return { ...col, render: undefined };
    }
    return col;
  });

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    const scrollTop = target.scrollTop;
    const clientHeight = target.clientHeight;
    const scrollHeight = target.scrollHeight;
    if (
      !fetching &&
      paginationObj.hasMore &&
      scrollTop + clientHeight >= scrollHeight
    ) {
      updateValue("fetching", true);
      fetchDetailList({
        pageNo: paginationObj.pageNo + 1,
        isLoading: false,
        loadMore: true,
        id: viewId,
      })
        .then((newData) => {
          if (newData?.code === 0) {
            const updatedData = newData?.list || [];
            const mergedData = grouped
              ? getMergedData(updatedData, mergeKeys)
              : updatedData;
            setData(renderRowKey(mergedData));
          }
        })
        .finally(() => {
          updateValue("fetching", false);
        });
    }
  };

  useImperativeHandle(ref, () => ({
    groupChange: handleGroup,
    onCopyView: handleCopy,
  }));

  const isDateTime = (val: string) => {
    // 简单判断 yyyy-mm-dd HH:mm:ss 格式
    return /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(val);
  };

  // 隐藏素材url 列，但是复制时需要展示，用下面方法
  const handleCopy = () => {
    // 生成 HTML 表格内容
    const tableHtml = `
    <table border="1" style="border-collapse: collapse; width: 100%;">
      <thead>
        <tr>
          ${mergedColumns
        .map((col: any) => {
          if (col.dataIndex === "material") {
            return `<th style="padding: 8px; text-align: left;">${col.title || ""
              }</th>
                        <th style="padding: 8px; text-align: left;">素材URL</th>`;
          }
          return `<th style="padding: 8px; text-align: left;">${col.title || ""
            }</th>`;
        })
        .join("")}
        </tr>
      </thead>
      <tbody>
        ${data
        .map((row: any) => {
          let tds = "";
          mergedColumns.forEach((col: any) => {
            if (col.dataIndex === "material") {
              // 只展示第一个素材
              const materialUrlInfoList = row.materialUrlInfoList || [];
              const first = materialUrlInfoList[0];
              if (!first) {
                tds += `<td></td>`;
              } else if (first.fileType === "IMAGE") {
                tds += `<td>=IMAGE("${first.urlNotExpire}")</td>`;
              } else if (first.fileType === "VIDEO") {
                tds += `<td>=IMAGE("${first.coverUrlNotExpire}")</td>`;
              } else {
                tds += `<td><span>未知素材类型</span></td>`;
              }
              // material 后面插入 material_url 列
              const urls = materialUrlInfoList
                .map(
                  (item: any) =>
                    `<a href="${item.urlNotExpire}" target="_blank" rel="noopener noreferrer">${item.urlNotExpire}</a>`
                )
                .join("<br/>");
              tds += `<td style="padding: 8px; text-align: left; white-space: pre-line;">${urls}</td>`;
            } else {
              // 非素材列的默认处理
              const rowSpan = row[`${col.dataIndex}RowSpan`];
              if (rowSpan === 0) {
                // 跳过 rowSpan 为 0 的单元格
                return;
              }
              let cellValue = row[col.dataIndex] || "";
              // 判断是否为日期时间格式
              if (typeof cellValue === "string" && isDateTime(cellValue)) {
                cellValue = `=TEXT("${cellValue}", "yyyy-mm-dd HH:mm:ss")`;
              }
              tds += `<td style="padding: 8px; text-align: left;" ${rowSpan > 0 ? `rowspan="${rowSpan}"` : ""
                }>${cellValue}</td>`;
            }
          });
          return `<tr>${tds}</tr>`;
        })
        .join("")}
      </tbody>
    </table>
  `;

    // 创建临时元素复制 HTML
    const tempElement = document.createElement("div");
    tempElement.innerHTML = tableHtml;
    document.body.appendChild(tempElement);

    const range = document.createRange();
    range.selectNodeContents(tempElement);
    const selection = window.getSelection();
    selection?.removeAllRanges();
    selection?.addRange(range);

    try {
      document.execCommand("copy");
      message.success("表格内容已复制到剪贴板");
    } catch (err) {
      message.error("复制失败");
      console.error("复制失败", err);
    }

    // 移除临时元素
    document.body.removeChild(tempElement);
  };

  // 点击分页
  const paginationChange = async (pagination: any) => {
    // ajax
    const otherParams: any = {};
    const filterParams: any = changeAnalysisInfoToParams(analysisInfo);
    for (const key in filterParams) {
      otherParams[key] = filterParams[key];
    }
    fetchDetailList({
      pageNo:
        pagination.pageSize !== paginationObj.pageSize ? 1 : pagination.current,
      pageSize: pagination.pageSize,
      isLoading: true,
      id: viewId,
      ...otherParams,
    }).then((newData) => {
      if (newData?.code === 0) {
        const updatedData = newData?.list || [];
        const mergedData = grouped
          ? getMergedData(updatedData, mergeKeys)
          : updatedData;
        setData(renderRowKey(mergedData));
      }
    });
  };

  return (
    <div className="w-full h-full">
      {/* <Button type="primary" style={{ marginBottom: 16 }} onClick={handleGroup}>
        {grouped ? "取消组合细分条件" : "组合细分条件"}
      </Button> */}
      {/* <button onClick={handleCopy}>复制</button> */}
      <Table
        // virtual
        columns={mergedColumns}
        dataSource={data}
        rowKey={"rowKey"}
        pagination={{
          ...paginationObj,
          current: paginationObj.pageNo,
          showSizeChanger: true,
          showTotal: (total: number) => `共 ${total || 0} 条数据`,
        }}
        bordered
        sticky={{ offsetHeader: 0 }}
        loading={tableLoading}
        // onScroll={handleScroll}
        onChange={paginationChange}
        scroll={{
          x: columns?.length > 10 ? 220 * columns?.length : 2200, // 220 是每列的宽度
          y: tableContainerSize.height,
        }}
        className={styles["subdivision-table"]}
      />
      {/* 底部加载中 */}
      {fetching && (
        <div
          className="col-span-full flex items-center justify-center py-4"
          style={{
            position: "absolute",
            left: 0,
            right: 0,
            bottom: 36,
            textAlign: "center",
            pointerEvents: "none",
          }}
        >
          <Spin size="small" />
          <span className="ml-2 text-gray-400">加载中...</span>
        </div>
      )}
    </div>
  );
};

export default forwardRef(SubdivisionTable);
