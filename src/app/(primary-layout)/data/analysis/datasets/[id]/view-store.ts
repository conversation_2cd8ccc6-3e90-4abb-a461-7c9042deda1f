import { makeAutoObservable } from "mobx";
import {
  getCreativeAutoAnalysisDetailList,
  getCreativeAutoAnalysisViewDelete,
  getCreativeAutoAnalysisViewCreate,
  getCreativeAutoAnalysisDatasourceList,
  getCreativeAutoAnalysisViewEdit,
  getCreativeAutoAnalysisViewList,
  getCreativeAutoAnalysisCustomInfo,
} from "./services";
import omit from "lodash/omit";

class ViewStore {
  constructor() {
    makeAutoObservable(this);
  }

  // 表格loading, 例如切换tab,第一次加载，需要在上方展示loading,
  loading: boolean = false;
  // 请求中的，如滚动加载
  fetching: boolean = false;
  // 列表数据
  views: any = [];
  paginationObj = {
    pageNo: 1,
    pageSize: 200,
    total: 0,
    hasMore: false,
  };
  // 视图tab
  tabs: any = [];

  paramType: string = "VIEW"; // 视图类型

  // 缓存的信息
  analysisInfo: any = {};

  tabKey: string = ""; // 当前选中的tab key

  grouped: boolean = true; // 是否分组

  requestId: number = 0; //当前请求标识符

  updateValue = (key: any, value: any) => {
    (this as any)[key] = value;
  };

  // 获取请求入参
  fetchRequestParams = async (params: any) => {
    try {
      const { data } = await getCreativeAutoAnalysisCustomInfo(params);
      if (data?.code == 0) {
        // this.analysisInfo = data?.result || {};
        return { code: data.code, result: data.result };
      }
    } catch (error) {
      console.error("获取请求入参失败", error);
      return { code: -1, result: {} };
    }
  };

  fetchCreativeAutoAnalysisDetailList = async (params?: {
    isLoading?: boolean;
    loadMore?: boolean;
    searchText?: string;
    id?: number;
    pageNo?: number;
    pageSize?: number;
  }) => {
    const currentRequestId = ++this.requestId;
    const isLoading = params?.isLoading ?? true;
    const loadMore = params?.loadMore ?? false;
    const hasSearch =
      params?.searchText !== undefined && params?.searchText !== "";
    try {
      this.loading = isLoading;
      // 计算请求页码
      const pageNo = params?.pageNo || this.paginationObj.pageNo;
      const pageSize = params?.pageSize || this.paginationObj.pageSize;
      // 自定义列、筛选、排序
      const otherParams: any = omit(params, [
        "isLoading",
        "loadMore",
        "searchText",
        "id",
        "pageNo",
        "pageSize",
      ]);
      const { data = {} } = await getCreativeAutoAnalysisDetailList({
        pageNo,
        pageSize,
        searchText: hasSearch ? params?.searchText : undefined,
        id: params?.id ?? undefined,
        ...otherParams,
      });
      if (data.code === 0) {
        if (this.requestId === currentRequestId) {
          const newItems = data?.result?.items || [];
          this.views = loadMore ? [...this.views, ...newItems] : newItems;
          this.paginationObj = {
            ...this.paginationObj,
            pageNo, // 记得同步更新当前页码
            pageSize,
            total: data?.result?.total || 0,
            hasMore: data?.result?.hasNext || false,
          };
        }
      }
      return { code: data.code, list: this.views };
    } catch (e) {
      return { code: -1 };
    } finally {
      this.loading = false;
    }
  };

  // 删除视图
  fetchCreativeAutoAnalysisViewDelete = async (params?: {}) => {
    try {
      const { data = {} } = await getCreativeAutoAnalysisViewDelete(params);
      return { code: data.code };
    } catch (e) {
      return { code: -1 };
    }
  };

  // 获取视图tab 数据
  fetchCreativeAutoAnalysisViewList = async (params?: {}) => {
    try {
      const { data = {} } = await getCreativeAutoAnalysisViewList(params);
      if (data.code === 0) {
        const newItems = data?.result?.viewVOList || [];
        this.tabs =
          newItems?.map((item: any) => ({
            ...item,
            key: item.id,
            label: item.name,
            value: item.id,
            closeIcon: false,
          })) || [];
      }
      return { code: data.code, result: this.tabs };
    } catch (e) {
      console.error("获取视图数据失败", e);
      return { code: -1 };
    }
  };

  // 新建｜编辑视图
  fetchCreativeAutoAnalysisViewCreate = async (params?: {
    isEdit?: boolean;
    subjectInfoId: number;
  }) => {
    try {
      const requestApi = params?.isEdit
        ? getCreativeAutoAnalysisViewEdit
        : getCreativeAutoAnalysisViewCreate;
      const { data = {} } = await requestApi(
        omit(
          params,
          params?.isEdit ? ["isEdit", "dataSourceConfigId"] : ["isEdit"]
        )
      );
      return { code: data.code, result: data.result };
    } catch (e) {
      console.error(`${params?.isEdit ? "编辑" : "新建"}视图失败`, e);
      return { code: -1 };
    }
  };

  // 数据源列表信息查询
  fetchCreativeAutoAnalysisDatasourceList = async (params?: {}) => {
    try {
      const { data = {} } = await getCreativeAutoAnalysisDatasourceList(params);
      if (data.code === 0) {
        this.datasourceOptionList =
          data?.result?.map((item: any) => ({
            ...item,
            value: item.id,
            label: item.name,
          })) || [];
      }
    } catch (e) {
      console.error("数据源列表获取失败", e);
    }
  };

  reset = () => {
    this.loading = false;
    this.fetching = false;
    this.views = [];
    this.paginationObj = {
      pageNo: 1,
      pageSize: 200,
      total: 0,
      hasMore: false,
    };
    this.tabs = [];
    this.paramType = "VIEW";
    this.analysisInfo = {};
    this.tabKey = "";
    this.grouped = true;
    this.requestId = 0;
  };
}

export default new ViewStore();
