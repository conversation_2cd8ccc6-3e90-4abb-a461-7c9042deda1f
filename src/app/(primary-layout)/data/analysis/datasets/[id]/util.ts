/*
 * @Date: 2025-05-28 13:29:43
 * @Author: miroku.yang
 * @Description:
 */
/**
 *
 * @param result
 * @returns 通过缓存的info 信息，整理成查询参数，未设置时取基础维度和基础指标
 */
export const changeAnalysisInfoToParams = (result: any) => {
  const allList = result?.selectedInfoList?.length
    ? result?.selectedInfoList
    : [
        ...(result.basisDimensionInfoList || []),
        ...(result.basisIndicatorInfoList || []),
        // ...(result.indicatorInfoList || []),
        // ...(result.partitionDimensionInfoList || []),
      ];
  return {
    // 筛选type 为BASIS_DIMENSION:基础维度、RANGE:赋值字段
    dimensionList: allList.filter((item: any) =>
      ["BASIS_DIMENSION", "RANGE"].includes(item.type)
    ),
    // 筛选type 为BASIS_INDICATOR:基础指标、CALCULATE_INDICATOR:计算字段
    indicatorList: allList.filter((item: any) =>
      ["BASIS_INDICATOR", "CALCULATE_INDICATOR"].includes(item.type)
    ),
    //数据过滤列表信息-视图
    viewFilterInfoRequestList:
      result.filterEditDTOList?.map((item: any) => {
        const currentItem = {
          ...item,
          filterValueList: item.valueList,
        };
        // 处理多余传值
        delete currentItem.valueList;
        return currentItem;
      }) || undefined,
    // 主题过滤信息
    subFilterInfoRequestList: result.subFilterInfoRequestList,
    // 排序参数
    orderInfo: result?.customSortEditDTO || undefined,
  };
};
