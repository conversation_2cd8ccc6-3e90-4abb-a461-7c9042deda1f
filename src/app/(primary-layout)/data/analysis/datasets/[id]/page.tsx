/*
 * @Date: 2025-05-23 19:33:44
 * @Author: miroku.yang
 * @Description: 报表页面
 */
"use client";
import React, { useEffect, useRef, useState } from "react";
import SubdivisionTable from "./components/SubdivisionTable";
import clsx from "classnames";
import styles from "./index.module.scss";
import ViewTab from "./components/ViewTab";
import Filter from "./components/Filter";
import { observer, useLocalObservable } from "mobx-react-lite";
import viewStore from "./view-store";
import { useParams } from "next/navigation";
import { changeAnalysisInfoToParams } from "./util";
// import { ScrollArea } from "@/components/ui/scroll-area";
// import Demo from "./components/MergedDemo";

const ReportForms = () => {
  const routerParams = useParams();
  const viewMobx = useLocalObservable(() => viewStore);
  const [tableContainerSize, setTableContainerSize] = useState<any>({});
  const tableContainerRef = useRef<any>(null);
  // 表格
  const tableRef = useRef<any>(null);

  useEffect(() => {
    const tabHeight = 40,
      filterPaddingTop = 16,
      filterHeight = 44,
      tableHeaderHieght = 40,
      tablePaginationHeight = 48,
      filterPaddingBottom = 16;
    if (tableContainerRef.current) {
      const width = tableContainerRef.current.offsetWidth;
      const height =
        tableContainerRef.current.offsetHeight -
        tabHeight -
        filterPaddingTop -
        filterHeight -
        tableHeaderHieght -
        tablePaginationHeight -
        filterPaddingBottom;
      setTableContainerSize({
        width: width,
        height: height,
      });
    }
    return () => {
      // 切换一级tab(主题与数据报表)时，初始化store, 防止当前卡片视图的数据汇总请求了上一次参数
      viewMobx?.reset();
    };
  }, []);

  // 切换tab
  const handleTabChange = (tabKey: string) => {
    // 切换tab
  };

  // 获取缓存数据//请求缓存的信息
  const currentId = routerParams.id ? Number(routerParams.id) : undefined;
  const fetchCacheAndDetailList = async (tabKey: string) => {
    // 获取主题的筛选信息和获取视图的筛选信息
    const [subjectRes, viewRes] = await Promise.all([
      viewMobx.fetchRequestParams({
        type: "USER_SUBJECT",
        // 这个地方查的是主题对应的详情
        id: currentId,
      }),
      viewMobx.fetchRequestParams({
        type: viewMobx.paramType,
        id: Number(tabKey),
      }),
    ]);
    if (subjectRes?.code === 0 && viewRes?.code === 0) {
      // 获取分析主题列表
      // 获取分析主题列表
      const currentParams: any = changeAnalysisInfoToParams({
        subFilterInfoRequestList:
          subjectRes?.result?.filterEditDTOList || undefined,
        ...(viewRes.result || {}),
      });
      viewMobx.updateValue("analysisInfo", {
        // 将分析主题的列表表头存在analysisInfo中
        customViewSelectedInfoList:subjectRes?.result?.selectedInfoList || [],
        subFilterInfoRequestList:
          subjectRes?.result?.filterEditDTOList || undefined,
        ...(viewRes.result || {}),
      });
      viewMobx.fetchCreativeAutoAnalysisDetailList({
        isLoading: true,
        id: Number(tabKey),
        ...currentParams,
      });
    }
    // console.log(subjectRes, "subjectRes", viewRes);
  };
  
  // 初始化时获取缓存数据和详情列表或者切换tab
  useEffect(() => {
    // 根据视图id 请求对应的缓存数据和详情列表
    if (viewMobx.tabKey) {
      // tab 的变化需要重置组合细分
      // if(viewMobx.grouped){
      //   viewMobx.updateValue("grouped", false);
      // }
      fetchCacheAndDetailList(viewMobx.tabKey);
    }
  }, [viewMobx.tabKey]);
  
  // 组合细分条件事件
  const handleGroup = () => {
    if (tableRef.current?.groupChange) {
      // 组合细分条件
      tableRef.current.groupChange();
      viewMobx.updateValue("grouped", !viewMobx.grouped);
    }
  };

  // 视图复制
  const handleCopyView = () => {
    if (tableRef.current?.onCopyView) {
      tableRef.current.onCopyView();
    }
  };

  return (
    <div
      className={clsx(styles["report-forms"], "w-full h-full flex flex-col")}
      ref={tableContainerRef}
    >
      <ViewTab
        fetchViewCreate={viewMobx.fetchCreativeAutoAnalysisViewCreate}
        fetchViewList={viewMobx.fetchCreativeAutoAnalysisViewList}
        fetchViewDelete={viewMobx.fetchCreativeAutoAnalysisViewDelete}
        tabs={viewMobx.tabs}
        updateValue={viewMobx.updateValue}
        views={viewMobx.views}
        onTabChange={handleTabChange}
        tableLoading={viewMobx.loading}
        paginationObj={viewMobx.paginationObj}
        fetching={viewMobx.fetching}
        fetchDetailList={viewMobx.fetchCreativeAutoAnalysisDetailList}
        fetchCacheAndDetailList={fetchCacheAndDetailList}
        tableContainerSize={tableContainerSize}
        analysisInfo={viewMobx.analysisInfo}
        handleGroup={handleGroup}
        grouped={viewMobx.grouped}
        handleCopyView={handleCopyView}
        renderChildren={(renderParams: any) => {
          return (
            <div
              className={clsx(
                styles["report-forms-content"],
                "w-full h-full p-[16px] overflow-hidden"
              )}
            >
              <Filter
                viewId={Number(renderParams.tab.key)}
                type={viewMobx.paramType}
                fetchCacheAndDetailList={renderParams.fetchCacheAndDetailList}
                updateValue={renderParams.updateValue}
                paginationObj={renderParams.paginationObj}
                handleGroup={renderParams.handleGroup}
                grouped={renderParams.grouped}
                views={renderParams.views}
                analysisInfo={renderParams.analysisInfo}
                tableLoading={renderParams.tableLoading}
                handleCopyView={renderParams.handleCopyView}
              />
              <SubdivisionTable
                views={renderParams.views}
                tableLoading={renderParams.tableLoading}
                paginationObj={renderParams.paginationObj}
                fetching={renderParams.fetching}
                updateValue={renderParams.updateValue}
                fetchDetailList={renderParams.fetchDetailList}
                tableContainerSize={renderParams.tableContainerSize}
                analysisInfo={renderParams.analysisInfo}
                ref={tableRef}
                viewId={Number(renderParams.tab.key)}
                grouped={renderParams.grouped}
              />
              {/* <Demo/> */}
            </div>
          );
        }}
      />
    </div>
  );
};

export default observer(ReportForms);
