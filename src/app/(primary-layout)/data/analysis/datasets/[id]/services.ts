/*
 * @Date: 2025-05-16 15:32:04
 * @Author: miroku.yang
 * @Description: 
 */
import requestHelper from '@/utils/requestHelper'

// 明细数据查询
export function getCreativeAutoAnalysisDetailList(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/data/aggregation/list', data)
}

// 删除视图
export function getCreativeAutoAnalysisViewDelete(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/view/delete', data)
}

// 新建视图
export function getCreativeAutoAnalysisViewCreate(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/view/create', data)
}

// 数据源列表信息查询
export function getCreativeAutoAnalysisDatasourceList(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/datasource/list', data)
}

// 编辑视图
export function getCreativeAutoAnalysisViewEdit(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/view/edit', data)
}

// 视图列表查询
export function getCreativeAutoAnalysisViewList(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/view/list', data)
}

// 获取数据过滤条件信息
export function getCreativeAutoAnalysisCustomInfo(data = {}) {
  return requestHelper.post('v4/demand/dam/creative/auto/analysis/custom/column/info', data)
}

