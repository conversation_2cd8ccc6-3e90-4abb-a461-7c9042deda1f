.media-box {
  position: relative;
  height: 120px!important;
  text-align: center;
  background-color: #010101;
  overflow: hidden;
  .cb-lazy-load-wrapper {
    display: flex;
    // height: 100%;
    align-items: center;
    justify-content: center;
    font-size: 0;
  }
  .cb-video-wrapper .video-js .vjs-big-play-button {
    display: none;
  }
  .cb-video-wrapper .video-js {
    border: none;
  }
  .custom-poster {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 3;
    display: inline-block;
    height: 100%;
    margin: 0;
    padding: 0;
    vertical-align: middle;
    background-color: #000000;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: contain;
    cursor: pointer;
    .total-time {
      position: absolute;
      right: 15px;
      bottom: 5px;
      line-height: 100%;
      color: #fff;
    }
  }
  .fuzzy-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    filter: blur(10px);
    transform: scale(1.2);
    transform-origin: center;
  }
  img.media-box-img {
    max-width: 100%;
    max-height: 100%;
    margin: 0 auto;
    display: inline-block;
    position: relative;
    z-index: 1;
  }
  .icon svg{
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -16px;
    margin-left: -16px;
    font-size: 32px;
  }
}