import styles from './index.module.scss';
import clsx from 'classnames'
import ceil from 'lodash/ceil';
import noop from 'lodash/noop';
import isNull from 'lodash/isNull';
import isUndefined from 'lodash/isUndefined';
import debounce from 'lodash/debounce';
import moment from 'moment';
import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import CBPreview from '@/components/CBVideo';
import SinoSymbolFont from '@/components/Icon/SinoSymbolFont';
import ReactLazyLoad from '../ImageLazyLoad';

interface Item {
  status?: Number;
  type?: Number | String;
  fileStatus?: Number;
  previewUrl?: String;
  url?: String;
  times?: Number | String;
}

export interface MappingItem {
  status?: string;
  type?: string;
  fileStatus?: string;
  previewUrl?: string;
  url?: string;
  times?: string;
}

interface mediaBoxProps {
  classNameWrap?: string;
  // 是否显示为正方形
  square?: boolean;
  scrollContainer?: string;
  sourceId?: any;
  item: Item;
  // 全屏预览回调
  handlePreview?: (current: any) => void;
  // 鼠标悬浮是否自动播放
  hoverAutoDisplay?: boolean;
  // 字段映射
  mappingItem?: MappingItem;
}

const setHeight = (dom: any) => {
  //获取dom宽度
  if (dom) {
    dom.style.height = `${dom.clientWidth}px`;
    dom.style.lineHeight = `${dom.clientWidth}px`;
  }
};

const DefultMappingItem = {
  status: 'status',
  type: 'type',
  fileStatus: 'fileStatus',
  previewUrl: 'previewUrl',
  url: 'url',
  times: 'times',
};

const MediaBoxWithTranslate = React.memo(
  ({
    classNameWrap = '',
    item,
    sourceId,
    vttUrl,
    handlePreview = (current: any) => {},
    scrollContainer,
    square = true,
    hoverAutoDisplay = false,
    mappingItem,
    mouseenterCallback,
    mouseleaveCallback,
  }: any) => {
    const [showCustomPoster, setShowCustomPoster] = useState(true);
    const [showVideo, setShowVideo] = useState(false);

    // 播放器实例
    const playerRef = useRef<any>(null);
    const divRef = useRef<any>(null);
    const clearMouseleaveTimeout = useRef<any>(null);
    const clearShowVideoHandle = useRef<any>(null);

    const formatItem: any = useMemo(() => {
      const formatMappingItem = Object.assign({}, DefultMappingItem, mappingItem);
      let temp: Item = Object.keys(formatMappingItem).reduce((pre, key) => {
        return {
          ...pre,
          // @ts-ignore
          [key]: item[formatMappingItem[key]],
        };
      }, {});
      /* 文件类型type 1图片 2视频 */
      // temp.type = (temp.type === 'image' || temp.type === 1) ? 1 : (temp.type === 'video' || temp.type === 2) ? 2 : 2;
      switch (temp.type) {
        case 'image':
        case 'IMAGE':
        case 1:
          temp.type = 1;
          break;
        case 'video':
        case 'VIDEO':
        case 2:
          temp.type = 2;
          break;
        default:
          temp.type = 2;
      }
      // 处理时间 HH:mm:ss
      if (!isNull(temp.times) && !isUndefined(temp.times)) {
        if (!isNaN(Number(temp.times))) {
          temp.times = moment.utc(ceil(Number(temp.times)) * 1000).format('HH:mm:ss');
        }
      }
      return temp;
    }, [item]);

    const isVideo: boolean = useMemo(() => {
      return formatItem.type === 2;
    }, [formatItem]);

    const cardUrl = useMemo(() => {
      // eslint-disable-next-line no-nested-ternary
      return formatItem.fileStatus === 1
        ? 'https://static.creativebooster.com/cm-web/CM_1620901307736231.svg'
        : formatItem.previewUrl;
    }, [formatItem]);

    const setPlayerIns = useCallback(
      (player: any) => (playerRef.current = player),
      [playerRef.current],
    );

    const mouseenter = (e: any) => {
      if (clearMouseleaveTimeout.current) {
        clearTimeout(clearMouseleaveTimeout.current);
      }
      clearShowVideoHandle.current = setTimeout(() => {
        isVideo && mouseenterCallback?.({
          isTrans: true,
          targetTrans: 'CN',
          source: 'CREATIVE_INTELLIGENCE',
          sourcePath: item.downloadUrl,
          sourceId,
        })
        let player = playerRef.current;
        // 移除自定义海报
        setShowCustomPoster(false);
        if (player) {
          // player.currentTime(0);
          player.play().catch(() => {});
        } else {
          // 初始化video
          setShowVideo(true);
        }
        clearShowVideoHandle.current = null;
      }, 800);
    };

    const mouseleave = (e: any) => {
      // stopPlay?.(item);
      // 显示自定义海报
      setShowCustomPoster(true);
      if (clearShowVideoHandle.current) {
        clearTimeout(clearShowVideoHandle.current);
      }
      if (clearMouseleaveTimeout.current) {
        clearTimeout(clearMouseleaveTimeout.current);
      }
      clearMouseleaveTimeout.current = setTimeout(() => {
        isVideo && mouseleaveCallback?.({ sourceId })
        let player = playerRef.current;
        // 判断video是否全屏，如果处于全屏状态下，则无需销毁
        if (player && player.isFullscreen()) {
          return;
        }
        setShowVideo(false);
      }, 300);
    };

    useEffect(() => {
      setHeight(divRef.current);

      const fn = debounce(() => {
        setHeight(divRef.current);
      }, 200);
      // 缩放尺寸，保持比例
      if (square) {
        window.addEventListener('resize', fn);
      }
      return () => {
        if (square) {
          window.removeEventListener('resize', fn);
        }
      };
    }, []);

    return (
      <div
        className={clsx([styles['media-box'], classNameWrap])}
        ref={divRef}
        onClick={() => {
          formatItem.status !== 1 &&
            (formatItem.type === 1 || !hoverAutoDisplay) &&
            handlePreview(formatItem);
        }}
        style={{
          cursor: formatItem.status === 1 ? 'default' : 'pointer',
          height: '100%',
        }}
      >
        {/* 文件类型type 1图片 2视频 */}
        {formatItem.type === 2 && formatItem.fileStatus !== 1 ? (
          <div
            style={{ width: '100%', height: '100%' }}
            onMouseEnter={hoverAutoDisplay && formatItem.url ? mouseenter : noop}
            onMouseLeave={hoverAutoDisplay && formatItem.url ? mouseleave : noop}
          >
            {showCustomPoster && (
              <div className={clsx("flex h-full",styles['cb-lazy-load-wrapper'])}>
                <div
                  className={styles["fuzzy-bg"]}
                  style={{
                    backgroundImage: `url(${cardUrl})`,
                  }}
                />
                <img
                  className={styles["fuzzy-bg"]}
                  // @ts-ignore
                  src={cardUrl}
                />
                <img
                  className={styles["media-box-img"]}
                  // @ts-ignore
                  src={cardUrl}
                />
                {formatItem.url ? (
                  <SinoSymbolFont type="iconbofang2" style={{ zIndex: 2 }} className={styles['icon']}/>
                ) : (
                  <span
                    style={{
                      color: '#fff',
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                    }}
                  >
                    资源地址为空
                  </span>
                )}
                <span className={styles["total-time"]}>{formatItem.times}</span>
              </div>
            )}
            {/* {showVideo && (
              <video src={formatItem.url}></video>
            )} */}
            {showVideo && (
              <CBPreview
                rotatePlayer={false}
                // cm 创意情报修复字幕显示问题加了这个，但是这里加了后视频加载失败
                // crossOrigin="anonymous"
                // @ts-ignore
                setPlayerIns={setPlayerIns}
                wrappedStyle={{ width: '100%', height: '100%', position: 'relative', zIndex: 2 }}
                // @ts-ignore
                src={formatItem.url}
                vttUrl={vttUrl}
                config={{
                  controlBar: {
                    liveDisplay: true,
                    pictureInPictureToggle: false,
                  },
                  autoplay: true,
                }}
              />
            )}
          </div>
        ) : scrollContainer ? (
          <ReactLazyLoad
            scrollContainer={scrollContainer}
            placeholder={
              <div
                style={{
                  color: '#808080d9',
                  height: 200,
                  textAlign: 'center',
                  lineHeight: '200px',
                }}
              >
                加载中...
              </div>
            }
          >
            <div
              className={styles["fuzzy-bg"]}
              style={{
                backgroundImage: `url(${cardUrl})`,
              }}
            />
            <img
              className={styles["media-box-img"]}
              // @ts-ignore
              src={cardUrl}
            />
          </ReactLazyLoad>
        ) : (
          <div className={clsx("flex h-full",styles['cb-lazy-load-wrapper'])}>
            <div
              className={styles["fuzzy-bg"]}
              style={{
                backgroundImage: `url(${cardUrl})`,
              }}
            />
            <img
              className={styles["media-box-img"]}
              // @ts-ignore
              src={cardUrl}
            />
          </div>
        )}
      </div>
    );
  },
);

export default MediaBoxWithTranslate;
