import MediaBoxWithTranslate, { MappingItem } from '../MediaBoxWithTranslate';
import styles from './index.module.scss';
import clsx from 'classnames'
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Carousel, Button } from 'antd';

interface Props {
  style?: React.CSSProperties;
  classNameWrap?: string;
  clusterIds?: string[];
  needTranslate?: boolean;
  mediaInfoList: any[];
  scrollContainer?: string;
  mappingItem?: MappingItem;
  handlePreview?: (p?:any) => void;
  getTranslateData?: (current: any, index: number) => void;
  playableUrl?: string;
}

const MaterialCarousel = React.memo((props: Props) => {
  const {
    style = {},
    classNameWrap = '',
    clusterIds = [],
    needTranslate = false,
    mappingItem = {},
    handlePreview = () => {},
    mediaInfoList,
    scrollContainer = '',
    getTranslateData,
    playableUrl,
  } = props;
  const [currentIndex, setCurrentIndex] = useState(0);
  // 翻译结果
  const [tranlateData, setTranslateData] = useState<any>({
    transSubtitleUrl: '',
    sourceSubtitleUrl: '',
    transSubtitleContent: {},
    sourceSubtitleContent: {},
  });

  const selfRef = useRef(null);

  // 翻译轮询的id
  const translateRef = useRef<any>(null);

  /**
   * 清除轮询
   */
  const clearTranslate = () => {
    if (translateRef.current) {
      clearTimeout(translateRef.current);
      translateRef.current = null;
    }
  };


  const prev = useCallback(
    e => {
      if (currentIndex === 0) {
        return;
      }
      selfRef?.current.prev();
    },
    [currentIndex],
  );

  const next = useCallback(
    e => {
      if (currentIndex === mediaInfoList.length - 1) {
        return;
      }
      selfRef?.current.next();
    },
    [currentIndex, mediaInfoList.length],
  );

  const afterChange = useCallback((index: number) => {
    setCurrentIndex(index);
  }, []);


  useEffect(() => {
    return () => {
      clearTranslate();
    };
  }, []);

  return (
    <div className={styles["material-carousel"]} style={{ position: 'relative', ...style }}>
      <Carousel ref={selfRef} dots={false} afterChange={afterChange}>
        {/* 防止轮播过多时卡顿，只保留当前展示的，还有前面一个以及后面一个, 其余的销毁 */}
        {mediaInfoList.map((v, i) => {
          if (currentIndex === i - 1 || currentIndex === i || currentIndex === i + 1) {
            const sourceId = clusterIds[i];
            // if (v.type === 'playable') {
            //   return (
            //     <div style={{ width: '100%', aspectRatio: '1 / 1', position: 'relative' }}>
            //       <img
            //         style={{ width: '100%', height: 'auto' }}
            //         src="https://static.creativebooster.com/cm-web/playable.png"
            //         alt="playable"
            //       />
            //       <div
            //         style={{
            //           display: 'flex',
            //           alignItems: 'center',
            //           position: 'absolute',
            //           bottom: '60px',
            //           left: '50%',
            //           transform: 'translateX(-50%)',
            //         }}
            //       >
            //         <Button
            //           type="primary"
            //           onClick={() => {
            //             if (!v?.previewUrl) return;
            //             window.dispatchEvent(
            //               new CustomEvent('playableStart', {
            //                 detail: { url: v.previewUrl },
            //               }),
            //             );
            //           }}
            //         >
            //           开始试玩
            //         </Button>
            //         {playableUrl && (
            //           <Button
            //             type="primary"
            //             style={{ marginLeft: '20px' }}
            //             onClick={() => {
            //               window.dispatchEvent(
            //                 new CustomEvent('playableStart', {
            //                   detail: { url: playableUrl },
            //                 }),
            //               );
            //             }}
            //           >
            //             查看原始素材
            //           </Button>
            //         )}
            //       </div>
            //     </div>
            //   );
            // }
            return (
              <MediaBoxWithTranslate
                key={i}
                classNameWrap={classNameWrap || ''}
                scrollContainer={scrollContainer}
                square
                hoverAutoDisplay
                mappingItem={mappingItem}
                sourceId={sourceId}
                vttUrl={tranlateData?.transSubtitleUrl}
                item={v}
                handlePreview={handlePreview}
              />
            );
          }
          return <div key={i} />;
        })}
      </Carousel>
      {mediaInfoList.length > 1 && (
        <>
          <span
            className={clsx(styles['material-carousel-pre-btn'], { disabled: currentIndex === 0 })}
            onClick={prev}
          >
            <LeftOutlined />
          </span>
          <span
            className={clsx(styles['material-carousel-next-btn'], {
              disabled: currentIndex === mediaInfoList.length - 1,
            })}
            onClick={next}
          >
            <RightOutlined />
          </span>
        </>
      )}
    </div>
  );
});
export default MaterialCarousel;
