/**
 * Author:hong.rong
 * Desc:查询滚动元素的父元素
 * Date:2021-06-24
 */

export default node => {
  if (!(node instanceof HTMLElement)) {
    return document.documentElement;
  }

  const excludeStaticParent = node.style.position === 'absolute';
  const overflowRegex = /(scroll|auto)/;
  let parent = node;

  while (parent) {
    if (!parent.parentNode) {
      return node.ownerDocument || document.documentElement;
    }

    const style = window.getComputedStyle(parent);
    const { position } = style;
    const { overflow } = style;
    const overflowX = style['overflow-x'];
    const overflowY = style['overflow-y'];

    if (position === 'static' && excludeStaticParent) {
      parent = parent.parentNode;
      // eslint-disable-next-line no-continue
      continue;
    }

    if (
      overflowRegex.test(overflow) &&
      overflowRegex.test(overflowX) &&
      overflowRegex.test(overflowY)
    ) {
      return parent;
    }

    parent = parent.parentNode;
  }

  return node.ownerDocument || node.documentElement || document.documentElement;
};
