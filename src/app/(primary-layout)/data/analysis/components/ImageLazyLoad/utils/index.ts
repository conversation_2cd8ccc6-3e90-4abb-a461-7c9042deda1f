/**
 * Author:hong.rong
 * Desc:工具类
 * Date:2021-06-24
 */

export const LISTEN_FLAG = 'data-lazyload-listened';

/**
 * 是否字符串
 * @param string
 */
export const isString = (string: any) => typeof string === 'string';

/**
 * 是否元素
 * @param obj
 */
export const isElement = (obj: any) => {
  return typeof HTMLElement === 'object'
    ? obj instanceof HTMLElement
    : !!(
        obj &&
        typeof obj === 'object' &&
        (obj.nodeType === 1 || obj.nodeType === 9) &&
        typeof obj.nodeName === 'string'
      );
};
