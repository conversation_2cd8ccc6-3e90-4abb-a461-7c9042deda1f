/**
 * Author:hong.rong
 * Desc:节流
 * Date:2021-06-24
 */

export default function throttle(fn, threshold = 250, scope?: any) {
  let last;
  let deferTimer;
  return function() {
    // eslint-disable-next-line @typescript-eslint/no-invalid-this
    const context = scope || this;

    const now = +new Date();
    // eslint-disable-next-line prefer-rest-params
    const args = arguments;
    if (last && now < last + threshold) {
      // hold on to it
      clearTimeout(deferTimer);
      deferTimer = setTimeout(function() {
        last = now;
        fn.apply(context, args);
      }, threshold);
    } else {
      last = now;
      fn.apply(context, args);
    }
  };
}
