/**
 * Author:hong.rong
 * Desc:重写react-lazyload ts版
 * Date:2021-03-18
 */

// eslint-disable-next-line max-classes-per-file
import * as React from 'react';
import { LISTEN_FLAG, isElement, isString } from './utils';
import { on, off } from './utils/event';
import scrollParent from './utils/scrollParent';
import debounce from './utils/debounce';
import throttle from './utils/throttle';

const defaultBoundingClientRect: any = {
  top: 0,
  right: 0,
  bottom: 0,
  left: 0,
  width: 0,
  height: 0,
};

// 被监听的组件集合
const listeners = [];
// 等待的组件集合
let pending = [];

// 使用try检测是否支持passive事件
let passiveEventSupported = false;
try {
  const opts = Object.defineProperty({}, 'passive', {
    get() {
      passiveEventSupported = true;
    },
  });
  window.addEventListener('test', null, opts);
} catch (e) {
  console.log(e);
}

// 如果支持，设置事件params
// 重点: FALSE兼作CAPTURE的默认值！
const passiveEvent = passiveEventSupported
  ? { capture: false, passive: true }
  : false;

/**
 * 检测component在溢出容器parent之后是否可见
 * @param  {node} component React component
 * @param  {node} parent    component's scroll parent
 * @return {bool}
 */
const checkOverflowVisible = function checkOverflowVisible(component, parent) {
  const node = component.ref;

  let parentTop;
  let parentLeft;
  let parentHeight;
  let parentWidth;

  try {
    ({
      top: parentTop,
      left: parentLeft,
      height: parentHeight,
      width: parentWidth,
    } = parent.getBoundingClientRect());
  } catch (e) {
    ({
      top: parentTop,
      left: parentLeft,
      height: parentHeight,
      width: parentWidth,
    } = defaultBoundingClientRect);
  }

  const windowInnerHeight =
    window.innerHeight || document.documentElement.clientHeight;
  const windowInnerWidth =
    window.innerWidth || document.documentElement.clientWidth;

  // 计算处于滚动父元素和视口交叉点元素的top和height
  const intersectionTop = Math.max(parentTop, 0); // 交叉点相对于视口的top
  const intersectionLeft = Math.max(parentLeft, 0); // 交叉点相对于视口的left
  const intersectionHeight =
    Math.min(windowInnerHeight, parentTop + parentHeight) - intersectionTop; // height
  const intersectionWidth =
    Math.min(windowInnerWidth, parentLeft + parentWidth) - intersectionLeft; // width

  // 检测元素在交叉点是否可见
  let top;
  let left;
  let height;
  let width;

  try {
    ({ top, left, height, width } = node.getBoundingClientRect());
  } catch (e) {
    ({ top, left, height, width } = defaultBoundingClientRect);
  }

  const offsetTop = top - intersectionTop; // 元素相对于交叉点的top
  const offsetLeft = left - intersectionLeft; // 元素相对于交叉点的left

  const offsets = Array.isArray(component.props.offset)
    ? component.props.offset
    : [component.props.offset, component.props.offset]; // 兼容先前的API

  return (
    offsetTop - offsets[0] <= intersectionHeight &&
    offsetTop + height + offsets[1] >= 0 &&
    offsetLeft - offsets[0] <= intersectionWidth &&
    offsetLeft + width + offsets[1] >= 0
  );
};

/**
 * 检测component在document内是否可见
 * @param  {node} component React component
 * @return {bool}
 */
const checkNormalVisible = function checkNormalVisible(component) {
  const node = component.ref;
  // 如果这个元素被css所隐藏，它肯定是看不见的
  if (
    !(node.offsetWidth || node.offsetHeight || node.getClientRects().length)
  ) {
    return false;
  }

  let top;
  let elementHeight;

  try {
    ({ top, height: elementHeight } = node.getBoundingClientRect());
  } catch (e) {
    ({ top, height: elementHeight } = defaultBoundingClientRect);
  }

  const windowInnerHeight =
    window.innerHeight || document.documentElement.clientHeight;

  const offsets = Array.isArray(component.props.offset)
    ? component.props.offset
    : [component.props.offset, component.props.offset]; // 兼容先前的API

  return (
    top - offsets[0] <= windowInnerHeight &&
    top + elementHeight + offsets[1] >= 0
  );
};

/**
 * 探测元素在视口是否可见, 如果可见, 设置`visible`状态为true.
 * 假如`once` prop为true, 在checkVisible之后把对component的监听移除
 * @param  {React} component   响应scroll和resize事件的React组件
 */
const checkVisible = function checkVisible(component) {
  const node = component.ref;
  if (!(node instanceof HTMLElement)) {
    return;
  }

  const parent = scrollParent(node);
  const isOverflow =
    component.props.overflow &&
    parent !== node.ownerDocument &&
    parent !== document &&
    parent !== document.documentElement;
  const visible = isOverflow
    ? checkOverflowVisible(component, parent)
    : checkNormalVisible(component);
  if (visible) {
    // 如果之前已经设置visible可见，避免重复渲染
    if (!component.visible) {
      if (component.props.once) {
        pending.push(component);
      }
      // eslint-disable-next-line no-param-reassign
      component.visible = true;
      component.forceUpdate();
    }
  } else if (!(component.props.once && component.visible)) {
    // eslint-disable-next-line no-param-reassign
    component.visible = false;
    if (component.props.unmountIfInvisible) {
      component.forceUpdate();
    }
  }
};

/**
 * 移除监听中绑定`once`事件的组件
 */
const purgePending = function purgePending() {
  pending.forEach(component => {
    const index = listeners.indexOf(component);
    if (index !== -1) {
      listeners.splice(index, 1);
    }
  });
  pending = [];
};

const lazyLoadHandler = () => {
  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < listeners.length; ++i) {
    const listener = listeners[i];
    checkVisible(listener);
  }
  // 移除监听中绑定`once`事件的组件
  purgePending();
};

/**
 * 更新组件，不管元素在视口是否可见
 * Forces the component to display regardless of whether the element is visible in the viewport.
 */
const forceVisible = () => {
  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < listeners.length; ++i) {
    const listener = listeners[i];
    listener.visible = true;
    listener.forceUpdate();
  }
  // 移除监听中绑定`once`事件的组件
  purgePending();
};

// 取决于组件的props
let delayType: string;
let finalLazyLoadHandler = null;

export interface LazyLoadProps {
  className?: string;
  classNamePrefix?: string;
  once?: boolean;
  offset?: number | number[];
  overflow?: boolean;
  resize?: boolean;
  scroll?: boolean;
  throttle?: number | boolean;
  debounce?: number | boolean;
  unmountIfInvisible?: boolean;
  style?: React.CSSProperties;
  height?: number | string; // 占位符高度，用于计算滚动区域高度，与placeholder属性选填其一
  placeholder?: React.ReactNode; // 自定义占位符组件，用于计算滚动区域高度，与height属性选填其一
  scrollContainer?: string | React.ReactNode;
}

class LazyLoad extends React.Component<LazyLoadProps> {
  static defaultProps = {
    className: '',
    classNamePrefix: 'cb-lazy-load',
    once: false,
    offset: 0,
    overflow: false,
    resize: false,
    scroll: true,
    unmountIfInvisible: false,
  };

  private readonly visible: boolean;

  private ref: any;

  constructor(props) {
    super(props);
    this.visible = false;
    this.setRef = this.setRef.bind(this);
  }

  componentDidMount() {
    // 不太可能即时更改延迟类型，这主要是为测试而设计的
    let scrollPort: any = window;
    const { scrollContainer } = this.props;
    if (scrollContainer) {
      if (isString(scrollContainer)) {
        scrollPort = scrollPort.document.querySelector(scrollContainer);
      }
      if (isElement(scrollContainer)) {
        scrollPort = scrollContainer;
      }
    }
    const needResetFinalLazyLoadHandler =
      (this.props.debounce !== undefined && delayType === 'throttle') ||
      (delayType === 'debounce' && this.props.debounce === undefined);

    if (needResetFinalLazyLoadHandler) {
      off(scrollPort, 'scroll', finalLazyLoadHandler, passiveEvent);
      off(window, 'resize', finalLazyLoadHandler, passiveEvent);
      finalLazyLoadHandler = null;
    }

    if (!finalLazyLoadHandler) {
      if (this.props.debounce !== undefined) {
        finalLazyLoadHandler = debounce(
          lazyLoadHandler,
          typeof this.props.debounce === 'number' ? this.props.debounce : 300,
        );
        delayType = 'debounce';
      } else if (this.props.throttle !== undefined) {
        finalLazyLoadHandler = throttle(
          lazyLoadHandler,
          typeof this.props.throttle === 'number' ? this.props.throttle : 300,
        );
        delayType = 'throttle';
      } else {
        finalLazyLoadHandler = lazyLoadHandler;
      }
    }

    if (this.props.overflow) {
      const parent = scrollParent(this.ref);
      if (parent && typeof parent.getAttribute === 'function') {
        const listenerCount = 1 + +parent.getAttribute(LISTEN_FLAG);
        if (listenerCount === 1) {
          parent.addEventListener('scroll', finalLazyLoadHandler, passiveEvent);
        }
        parent.setAttribute(LISTEN_FLAG, listenerCount);
      }
    } else if (listeners.length === 0 || needResetFinalLazyLoadHandler) {
      const { scroll, resize } = this.props;

      if (scroll) {
        on(scrollPort, 'scroll', finalLazyLoadHandler, passiveEvent);
      }

      if (resize) {
        on(window, 'resize', finalLazyLoadHandler, passiveEvent);
      }
    }

    listeners.push(this);
    checkVisible(this);
  }

  shouldComponentUpdate() {
    return this.visible;
  }

  componentWillUnmount() {
    if (this.props.overflow) {
      const parent = scrollParent(this.ref);
      if (parent && typeof parent.getAttribute === 'function') {
        const listenerCount = +parent.getAttribute(LISTEN_FLAG) - 1;
        if (listenerCount === 0) {
          parent.removeEventListener(
            'scroll',
            finalLazyLoadHandler,
            passiveEvent,
          );
          parent.removeAttribute(LISTEN_FLAG);
        } else {
          parent.setAttribute(LISTEN_FLAG, listenerCount);
        }
      }
    }

    const index = listeners.indexOf(this);
    if (index !== -1) {
      listeners.splice(index, 1);
    }

    if (listeners.length === 0 && typeof window !== 'undefined') {
      off(window, 'resize', finalLazyLoadHandler, passiveEvent);
      off(window, 'scroll', finalLazyLoadHandler, passiveEvent);
    }
  }

  setRef(element) {
    if (element) {
      this.ref = element;
    }
  }

  render() {
    const {
      height,
      children,
      placeholder,
      className,
      classNamePrefix,
      style,
    } = this.props;
    return (
      <div
        className={`${classNamePrefix}-wrapper ${className}`}
        ref={this.setRef}
        style={style}
      >
        {this.visible
          ? children
          : placeholder || (
              <div
                style={{ height }}
                className={`${classNamePrefix}-placeholder`}
              />
            )}
      </div>
    );
  }
}

const decorator = (options: any) => (WrappedComponent: any) =>
  class LazyLoadDecorated extends React.Component {
    render() {
      return (
        <LazyLoad {...options}>
          <WrappedComponent {...this.props} />
        </LazyLoad>
      );
    }
  };

export { decorator as lazyload };
export { lazyLoadHandler as forceCheck };
export { forceVisible };
export default LazyLoad;
