/*
 * @Date: 2025-03-13 15:49:19
 * @Author: miroku.yang
 * @Description:
 */
import styles from './index.module.scss';
import React from 'react';
import CBPreviewHover from '@/components/CBPreviewHover';
import { zipList } from '@/utils';
import { FileHelper } from "@/lib/common";
// import { ConfigContext } from '@cb/bs-components-v2';

interface IProps {
  record: any;
  showPreview: (dataSource: any, index: number) => void;
  fileIndex: number;
  list: any;
}
const Card = (props: IProps) => {
  const { record, showPreview, fileIndex, list } = props;
  // const { showZipModal } = React.useContext(ConfigContext);

  const fileInfo: any =
    record?.originData || record?.imageFileInfo || record?.fileInfo || record || {};
  const poster: string =
    fileInfo?.previewInfo?.resizeImageUrl || record?.poster || fileInfo?.url || '';
  const isVideo = FileHelper.getType(fileInfo?.url)?.includes('video');
  // 视频才有playUrl，先统一按照视频取值，没有去url
  const url: string = fileInfo?.streamInfo?.playUrl || fileInfo?.url || '';
  const transcodeVideoUrlList: any[] = fileInfo?.streamInfo?.transcodeVideoUrlList || [];
  const currentFileType = FileHelper.getType(url);

  return (
    <CBPreviewHover
      key={record?.uuid}
      percent={record?.percent}
      url={isVideo ? url : poster}
      poster={poster}
      width="60px"
      height="60px"
      style={{ padding: 0 }}
      showPopconfirm
      className={styles['work-look-img']}
      onPreview={() => {
        // if (zipList.includes(currentFileType)) {
        //   showZipModal?.({
        //     path: fileInfo.path,
        //     uuid: fileInfo.uuid,
        //     fileName: fileInfo?.name,
        //   });
        //   return;
        // }
        if (url) {
          showPreview(list, fileIndex);
        }
      }}
      useXgPlayer={false}
      transcodeVideoUrlList={transcodeVideoUrlList}
    />
  );
};

export default Card;
