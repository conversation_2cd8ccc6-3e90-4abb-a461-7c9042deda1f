/*
 * @Date: 2025-05-23 13:40:08
 * @Author: miroku.yang
 * @Description: 
 */
import Card from './Card';
import styles from './index.module.scss';
import React from 'react';

interface IProps {
  list: any;
  showPreview: (dataSource: any, index: number) => void;
}
const WorksColumn = (props: IProps) => {
  const { list, showPreview } = props;
  const dataSource = list?.map((item: any, index: number) => ({ ...item, uwiUuid: item.uwiUuid || index + 1 }));
  return (
    <div className={styles['works-component']}>
      {dataSource?.map((item: any, index: number) => (
        <Card
          record={item}
          showPreview={showPreview}
          key={item.uwiUuid}
          fileIndex={index}
          list={list}
        />
      ))}
    </div>
  );
};

export default WorksColumn;
