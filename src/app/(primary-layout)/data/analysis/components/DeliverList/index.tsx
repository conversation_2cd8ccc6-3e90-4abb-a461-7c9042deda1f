/*
 * @Date:
 * @Author: miroku.yang
 * @Description: 素材弹窗
 */
import Card from './Card';
import styles from './index.module.scss';
import clsx from "classnames";
import React, { useEffect, useMemo, useState } from 'react';
import { InfoOutlined } from '@ant-design/icons';
import { Spin, Pagination } from 'antd';

interface IProps {
  searchParams?: any;
  showPreview: (dataSource: any, index: number) => void;
  list: any; // 列表数据
}
const DeliverList = (props: IProps) => {
  const { searchParams, showPreview } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any>(null);
  const [pagination, setPagination] = useState<any>({ pageNo: 1, total: 0, pageSize: 10 });
  const defaultText = '暂不支持展示前序环节的方向/脚本/拍摄等内容，统一展示该任务的成片。';

  const fetchSettleWorkList = (p?: any) => {
    const newParams = p ? { ...searchParams, ...p } : { ...searchParams, ...pagination };
    // uid 为空无需请求接口
    if (!newParams.uid) {
      setDataSource([]);
      return false;
    }
    try {
      setLoading(true);
      getSettleWorkList(newParams)
        .then(({ data }: any) => {
          if (data?.code == 0) {
            setDataSource(data?.result?.items || []);
            setPagination({
              ...pagination,
              total: data?.result?.total || 0, // 交付量总数
            });
          }
        })
        .catch(error => {
          setDataSource([]);
          // eslint-disable-next-line no-console
          console.error('交付量点击获取更多作品失败', error);
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (error) {
      setLoading(false);
      setDataSource([]);
      // eslint-disable-next-line no-console
      console.error('try catch 打印：交付量点击获取更多作品失败', error);
    }
  };
  useEffect(() => {
    // fetchSettleWorkList();
  }, []);

  useEffect(()=>{
    setDataSource(props.list)
  },[props.list])

  const hasDateSource = useMemo(() => {
    return Array.isArray(dataSource) && dataSource?.length;
  }, [dataSource]);

  // 点击分页
  const paginationChange = async (e: number, s: number) => {
    const newPagination = pagination;
    newPagination.pageNo = e;
    newPagination.pageSize = s;
    setPagination(newPagination);
    // ajax
    fetchSettleWorkList(newPagination);
  };

  return (
    <div
      className={clsx(styles['deliver-list-component'], {
        [styles['has-data-source']]: hasDateSource,
      })}
    >
      <Spin spinning={loading}>
        {Array.isArray(dataSource) && dataSource?.length == 0 ? (
          <div className="flex items-center justify-center flex-col">
            <img
              className={styles['empty-img']}
              src="https://static.creativebooster.com/cb-npm/CM_1630315285802473.svg"
              alt="空内容图片"
            />
            <div className={styles.title}>暂无数据</div>
            {/* <div className={styles.subtitle}>{defaultText}</div> */}
          </div>
        ) : null}
        {hasDateSource ? (
          <>
            {/* <div className={styles['info-out-lined-wrap']}>
              <InfoOutlined className={styles['info-out-lined']} style={{ fontSize: 12 }} />
              {defaultText}
            </div> */}
            <div className={styles['card-list']}>
              {dataSource.map((item: any, index: number) => (
                <Card
                  item={item}
                  key={item.id}
                  showPreview={showPreview}
                  fileIndex={index}
                  list={dataSource}
                />
              ))}
              {/* <div className={styles['pagination-area']}>
                <Pagination
                  current={pagination.pageNo}
                  total={pagination.total}
                  pageSize={pagination.pageSize}
                  onChange={paginationChange}
                  showSizeChanger
                  showTotal={(total: number) => `共 ${total || 0} 条数据`}
                />
              </div> */}
            </div>
          </>
        ) : null}
      </Spin>
    </div>
  );
};
export default DeliverList;
