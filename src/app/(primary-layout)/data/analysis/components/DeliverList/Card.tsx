/*
 * @Date: 2025-03-13 10:43:19
 * @Author: miroku.yang
 * @Description: 弹框卡片
 */
import styles from './index.module.scss';
import clsx from "classnames";
import moment from 'moment';
import React from 'react';
import { FileDownloadTask } from '@/components/FileDownload';
import IconSvg from '@/components/IconSvg';
import LookImg from '@/components/LookImg';
import { zipList } from '@/utils';
// import { ConfigContext } from '@cb/bs-components-v2';
import { FileHelper } from "@/lib/common";
import { Tooltip, message } from '@m-design/mui';

interface IProps {
  // 文件信息
  item: any;
  showPreview: (dataSource: any, index: number) => void;
  fileIndex: number;
  list: any;
}
const Card = (props: IProps) => {
  const { item = {}, showPreview, fileIndex, list } = props;
  // const { showZipModal } = React.useContext(ConfigContext);

  const fileInfo: any = item?.originData || item?.imageFileInfo || item?.fileInfo || item || {};
  const poster: string =
    fileInfo?.previewInfo?.resizeImageUrl || item.poster || fileInfo?.url || '';
  const url: string = fileInfo?.streamInfo?.playUrl || fileInfo?.url || '';
  const isVideo = FileHelper.getType(url)?.includes('video');
  const transcodeVideoUrlList: any[] = fileInfo?.streamInfo?.transcodeVideoUrlList || [];

  const handleDownload = async (downloadFiles: any) => {
    if (!downloadFiles?.length) {
      return message.warning('暂无可下载的附件');
    }
    await FileDownloadTask.add({ files: downloadFiles });
  };

  return (
    <div className={styles['card-component']}>
      <LookImg
        className={clsx(styles['deliver-look-img'], {
          [styles['other-file']]:
            !FileHelper.getType(url)?.includes('image') &&
            !FileHelper.getType(url)?.includes('video'),
        })}
        key={item.uuid}
        percent={item.percent}
        url={isVideo ? url : poster}
        poster={poster}
        width={200}
        height={200}
        showPopconfirm
        file={fileInfo}
        showPreviewIcon
        onPreview={() => {
          if (url) {
            const currentFileType = FileHelper.getType(url);
            // if (zipList.includes(currentFileType)) {
            //   showZipModal?.({
            //     path: fileInfo.path,
            //     uuid: fileInfo.uuid,
            //     fileName: fileInfo?.name,
            //   });
            //   return;
            // }
            showPreview(list, fileIndex);
          }
        }}
        showDelete={false}
        renderAction
        isReadChunk={true ? item.isReadChunk : false}
        // renderDownload={() => (
        //   <Tooltip title="下载源文件" rootClassName={styles['download-tooltip']}>
        //     <span
        //       className={styles['download-icon-wrap']}
        //       onClick={(e: Event) => {
        //         handleDownload([{ name: fileInfo.name, url: fileInfo.url }]);
        //         e.stopPropagation();
        //       }}
        //     >
        //       <IconSvg type="iconiconxiazai1" style={{ color: '#fff', fontSize: 20 }} />
        //     </span>
        //   </Tooltip>
        // )}
        openThrottle
        useXgPlayer={false}
        transcodeVideoUrlList={transcodeVideoUrlList}
      />
      <div className={clsx('flex justify-between', styles['bottom-wrap'])}>
        <span className={styles['customer-product-name']} title={item.customerProductName}>
          {item.customerProductName}
        </span>
        {/* <span className={styles['upload-time']}>
          {item.uploadTime ? moment(item.uploadTime).format('YYYY-MM-DD') : '-'}
        </span> */}
      </div>
    </div>
  );
};

export default Card;
