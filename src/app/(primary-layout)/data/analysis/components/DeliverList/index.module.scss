.deliver-list-component {
  height: 625px;
  overflow-y: auto;
  :global {
    .m-design-spin-nested-loading {
      height: 100%;
    }
    .m-design-spin-spinning{
      position: inherit!important;
    }
    .m-design-spin-container {
      display: flex;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
  }
  &.has-data-source {
    :global {
      .m-design-spin-container {
        flex-direction: column;
        justify-content: flex-start;
      }
    }
  }
  .info-out-lined-wrap {
    width: 100%;
    margin-top: 20px;
    margin-bottom: 16px;
    margin-left: 9px;
  }
  .info-out-lined {
    margin-right: 9px;
    padding: 2px;
    color: #fff;
    background-color: #006cf6;
    border-radius: 50%;
  }

  .title {
    margin-bottom: 4px;
    color: #1d2129;
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
  }

  .subtitle {
    color: #86909c;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
  }

  .empty-img {
    width: 133px;
    height: 120px;
    margin-bottom: 39px;
  }
  .card-list {
    display: flex;
    flex-wrap: wrap;
  }
  .card-component {
    margin-right: 7px;
    margin-bottom: 24px;
    margin-left: 8px;
    :global {
      .look-img--preview {
        right: 14px;
      }
      .look-img--download {
        right: 12px;
      }
    }
  }
  .deliver-look-img {
    padding: 0;
    background: #f2f3f5;
    border: none;
    border-radius: 6px;
    overflow: hidden;
    :global {
      .icon.file-content {
        width: 58px;
        height: 100%;
      }
      img{
        object-fit: cover!important;
      }
      video{
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  .pagination-area {
    width: 100%;
    text-align: right;
    :global {
      .m-design-pagination {
        padding-right: 14px;
      }
    }
  }

  .download-icon-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(18, 18, 22, 0.65);
    border-radius: 4px;
    > span {
      color: #fff;
      font-size: 20px;
    }
  }
}

.customer-product-name {
  color: #1d2129;
  font-weight: 500;
  font-size: 12px;
  line-height: 24px;
  max-width: 200px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.upload-time {
  color: #86909c;
  font-weight: 500;
  font-size: 12px;
  line-height: 24px;
}

.bottom-wrap {
  margin-top: 8px;
}
