/*
 * @Date: 2025-05-15 15:32:51
 * @Author: miroku.yang
 * @Description: 新建、编辑主题
 */
import { Form, Input, Select, Button, message } from "antd";
import { useModalWrap } from "@/components";
import { useRef, useState } from "react";

interface IProps {
  // 新建主题
  createAnalysisFetch: (params: any) => Promise<any>;
  // 操作成功后的回到
  onSuccessCallback?: (p?: any,isEdit?:boolean) => void;
}
const useCreateAnalysisTopic = (props: IProps) => {
  const { onSuccessCallback, createAnalysisFetch } = props;
  const showParamsRef = useRef<any>({});
  const [showParams, setShowParams] = useState<any>({});
  const formRef = useRef<any>(null);
  const [submitLoading, setSubmitLoading] = useState(false);

  const {
    showModal: showCreateAnalysisModal,
    hideModal,
    render: renderCreateAnalysisModal,
    form: any,
  } = useModalWrap({
    modalProps: {
      title: (
        <div className="text-center">
          {showParams.id ? "编辑分析主题" : "新建分析主题"}
        </div>
      ),
      width: 520,
      footer: null,
    },
    children: ({ form }: any) => {
      formRef.current = form;

      return (
        <Form
          form={form}
          layout="horizontal"
          labelCol={{ span: 4 }}
          labelAlign={"left"}
        >
          <Form.Item
            label="主题名称"
            name="name"
            rules={[{ required: true, message: "请输入主题名称" }]}
          >
            <Input placeholder="请输入" maxLength={50} />
          </Form.Item>
          <Form.Item
            label="数据源"
            name="dataSourceConfigId"
            rules={showParamsRef.current.id?[]:[{ required: true, message: "请选择数据源" }]}
          >
            <Select
              placeholder="请选择"
              disabled={showParamsRef.current.id}
              showSearch
              allowClear
              optionFilterProp="children"
              filterOption={(input: any, option: any) =>
                (option?.label ?? "")
                  ?.toLowerCase()
                  ?.includes?.(input?.toLowerCase())
              }
              options={showParamsRef.current?.options || []}
            ></Select>
          </Form.Item>
          <Form.Item className="text-center">
            <Button
              type="primary"
              loading={submitLoading}
              onClick={() => handleCreate(form)}
              className="w-[200px]"
            >
              确认
            </Button>
          </Form.Item>
        </Form>
      );
    },
  });

  const handleCreate = (form: any) => {
    if (submitLoading) return;
    setSubmitLoading(true);
    form
      .validateFields()
      .then((values: any) => {
        const params = {
          ...values,
          id: showParamsRef.current.id || undefined,
          isEdit: !!showParamsRef.current.id,
        };
        createAnalysisFetch(params).then((res: any) => {
          if (res?.code == 0) {
            message.success(
              showParamsRef.current.id
                ? "编辑分析主题成功"
                : "新建分析主题成功"
            );
            hideModal();
            form.resetFields();
            // 成功后的操作
            onSuccessCallback?.({id:res?.result || undefined,name:values.name},!!showParamsRef.current.id);
          }
        });
      })
      .finally(() => {
        setSubmitLoading(false);
      });
  };

  const handleShowModal = (showModalParams: any = {}) => {
    showParamsRef.current = showModalParams || {};
    setShowParams(showModalParams);
    if (formRef.current) {
      if (showModalParams && Object.keys(showModalParams).length > 0) {
        formRef.current.setFieldsValue(showModalParams);
      } else {
        formRef.current.resetFields();
      }
    }
    showCreateAnalysisModal(showModalParams);
  };

  return {
    showCreateAnalysisModal: handleShowModal,
    renderCreateAnalysisModal,
  };
};
export default useCreateAnalysisTopic;
