.advanced-setting-entry {
  cursor: pointer;
}

.advanced-setting-drawer {
  :global {
    .ant-drawer-body {
      padding: 24px 20px;
    }
  }
}

.overlay-content {
  max-width: inherit !important;

  .title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #4e5969;
    line-height: 22px;
    margin-bottom: 16px;
  }

  :global {
    .ant-tooltip-inner {
      background-color: #fff!important;
      padding: 24px!important;
    }
  }
}

.order-filter-entry-wrap {
  height: 32px;
  padding: 9px 12px;
  border-radius: 6px;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  max-width: 340px;
  // margin-bottom: 12px;
  background: #f2f3f5;

  &:hover {
    color: #4080ff;
  }

  &.ant-tooltip-open {
    background: #e5e6eb;
  }

  &-value {
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    display: flex;
  }

  &-children {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    white-space: nowrap;
  }
}

.project-filter-entry-tooltip-title {
  word-break: break-all;
}

.order-filter-entry-wrap-active {
  background: #e8f3ff !important;
  border-radius: 6px;
  color: #165dff;
}

.ant-form-item-control-input {
  margin-bottom: 1px;
}

.order-filter-entry-wrap-border{
  // border:1px solid #d9d9d9;
  // border-radius: 6px;
  // padding: 0px 15px;
  // line-height: 30px;
  font-size: 14px;
}

.order-filter-entry-wrap-value-tooltip {
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}