/*
 * @Date: 2025-05-16 16:33:25
 * @Author: miroku.yang
 * @Description: 
 */
import React, { useState } from 'react';
import { InputNumber, Form } from 'antd';
import debounce from 'lodash/debounce';

interface RangeInputProps {
  label?: string;
  suffix?: string;
  value?: [number, number];
  onChange?: (values: [number | null, number | null]) => void;
}

const RangeInput: React.FC<RangeInputProps> = ({ label, value, onChange, suffix }) => {
  const [minValue, setMinValue] = useState<number | null>(value ? value[0] : null);
  const [maxValue, setMaxValue] = useState<number | null>(value ? value[1] : null);

  const handleMinChange = debounce((newValue: number | null) => {
    setMinValue(newValue);
    onChange && onChange([newValue, maxValue]);
  }, 800);

  const handleMaxChange = debounce((newValue: number | null) => {
    setMaxValue(newValue);
    onChange && onChange([minValue, newValue]);
  }, 800);

  return (
    <Form.Item label={label}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <InputNumber
          placeholder="最小值"
          min={0}
          maxLength={10}
          controls={false}
          style={{ flex: 1 }}
          suffix={suffix}
          defaultValue={value?.[0]}
          onChange={handleMinChange}
        />
        <span style={{ margin: '0 8px' }}>至</span>
        <InputNumber
          placeholder="最大值"
          min={0}
          maxLength={10}
          controls={false}
          style={{ flex: 1 }}
          suffix={suffix}
          defaultValue={value?.[1] || undefined}
          onChange={handleMaxChange}
        />
      </div>
    </Form.Item>
  );
};

export default RangeInput;
