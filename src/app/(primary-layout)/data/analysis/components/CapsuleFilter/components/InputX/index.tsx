/*
 * @Date: 2025-05-16 16:33:25
 * @Author: miroku.yang
 * @Description: 
 */
import React, { useState } from 'react';
import { Input, Form } from 'antd';
import debounce from 'lodash/debounce';

interface InputXProps {
  label?: string;
  suffix?: string;
  placeholder?: string;
  value?: any;
  onChange?: (values: [number | null, number | null]) => void;
}

const InputX: React.FC<InputXProps> = ({ label, value, onChange, suffix, placeholder,...resetProps }) => {

  const handleChange = debounce((newValue: any) => {
    onChange && onChange(newValue.target.value);
  }, 490);

  return (
    <Form.Item label={label}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Input
          {...resetProps}
          placeholder={placeholder}
          style={{ flex: 1 }}
          suffix={suffix}
          defaultValue={value}
          onChange={handleChange}
        />
      </div>
    </Form.Item>
  );
};

export default InputX;
