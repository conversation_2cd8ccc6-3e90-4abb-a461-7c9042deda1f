import React, { useEffect, useRef } from "react";
import { Input, Form, Col, Row, Select, DatePicker, InputNumber } from "antd";
import clsx from "classnames";
import { IconSvg } from "@/components";
import SinoSymbolFont from "@/components/Icon/SinoSymbolFont";
// import ColorfulSelect from '@/pages/components/ColorfulSelect';
import dayjs from "dayjs";
import { CaretDownOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import RangeInput from "../RangeInput";
import InputX from "../InputX";
import find from "lodash/find";
import { ENUM_OPTIONS } from "../../util";

dayjs.locale("zh-cn");

const Item = Form.Item;
const { RangePicker } = DatePicker;

interface IProps {
  firstOption: any[]; // 所有筛选项下拉
  limit?: number; // 限制可以创建的组数
  form: any;
  otherOptionData: any;
  value?: any;
  labelDisabled?: boolean; // 不可新增不可清除统一管理按钮
  updateViewButtonList: (list: any) => void;
  handleValuesChange?: (
    changedValues: any,
    allValues: any,
    viewOptions: any
  ) => void; // 表单值发生修改时触发
  disabledPartName?: any; // 禁用编辑的选项 里面采用title字段名称例如 'launchTime'
  dateGranularity: string;
  dateWeek: any; // 周数据
  upeateFirstOption: (o: any) => void;
}

/**
 * @param {IProps} props
 * @return {JSX.Element}
 */
const FormList = (props: IProps) => {
  const {
    firstOption,
    form,
    otherOptionData,
    value,
    updateViewButtonList,
    handleValuesChange,
    disabledPartName = [],
    dateGranularity,
    dateWeek,
    upeateFirstOption,
  } = props;
  const contentListTpl: any = { title: undefined, content: undefined };
  const scrollNodeTimer = useRef<any>(null);

  const handleAllClear = () => {
    updateViewButtonList([]);
    form.resetFields(["contentList"]);
    handleValuesChange?.([], [], firstOption);
    // 恢复所有 firstOption 的 disabled
    const newOption = firstOption.map((item) => ({
      ...item,
      disabled: false,
    }));
    upeateFirstOption(newOption);
  };

  const handleViewOptionChange = (e: any, index: number) => {
    let fields = form.getFieldValue("contentList");
    if (fields[index]?.content) {
      fields[index].content = undefined;
    }
    // 如果是删除
    if (!e && fields[index]) {
      // 清空当前项
      fields[index] = { title: undefined };
    }
    form.setFieldValue("contentList", fields);
    updateViewButtonList(fields);
    const record = find(firstOption, { code: e });
    // 直接取会拿到旧值，需要更新
    fields = fields.map((item: any) => {
      // 如果选中的还可以再选，那么需要处理选中2个一样的表单，修改第二个值会把所有一样字段的给去掉，当前不存在
      if (record && item.title == record.code) {
        item.code = record.code;
        item.dataType = record.dataType;
        item.id = record.id;
        item.type = record.type;
        item.name = record.name;
        item.dateGranularity = record.dateGranularity;
        item.content = undefined;
      }
      return item;
    });
    const allValues = {
      contentList: fields,
    };
    handleValuesChange?.(allValues, allValues, firstOption);
  };

  useEffect(() => {
    value && form.setFieldValue("contentList", value);
  }, [value]);

  const handleScrollToBottom = () => {
    const scrollNode = document.getElementById("order-filter-field-item-wrap");
    if (scrollNode) {
      const scrollHeight = scrollNode.scrollHeight;
      const height = scrollNode.clientHeight;
      const maxScrollTop = scrollHeight - height;
      if (maxScrollTop > 0) {
        clearTimeout(scrollNodeTimer.current);
        scrollNodeTimer.current = setTimeout(() => {
          scrollNode.scrollTop = scrollHeight;
        }, 0);
      }
    }
  };

  return (
    <div className={clsx("form-list-component", styles["rich-text-component"])}>
      <Form.List name="contentList">
        {(fields, { add, remove }) => {
          return (
            <>
              <Col
                className={styles["field-item-col"]}
                id="order-filter-field-item-wrap"
              >
                {fields.map(({ key, name, ...restField }, index) => {
                  return (
                    <Item
                      key={key}
                      className={styles["form-list-item-wrap"]}
                      // labelCol={{ span: 2 }}
                      label={null}
                    >
                      <Row>
                        <Col className={styles["form-list-item-col-field"]}>
                          <Item
                            className={styles["form-list-item-col-item"]}
                            shouldUpdate={(prevValues, curValues) =>
                              prevValues?.contentList?.[index]?.title !==
                              curValues?.contentList?.[index]?.title
                            }
                          >
                            {({ getFieldValue }) => {
                              const fields = getFieldValue("contentList");
                              const currentField = fields[index];
                              return (
                                <Item
                                  {...restField}
                                  name={[name, "title"]}
                                  label=""
                                  labelAlign="left"
                                  className={styles["form-list-item-col-item"]}
                                >
                                  <Select
                                    options={firstOption}
                                    allowClear={
                                      !disabledPartName.includes(
                                        currentField.title
                                      )
                                    }
                                    disabled={
                                      props?.labelDisabled ||
                                      disabledPartName.includes(
                                        currentField.title
                                      ) ||
                                      false
                                    }
                                    showSearch
                                    optionFilterProp="children"
                                    filterOption={(input: any, option: any) =>
                                      (option?.label ?? "")
                                        ?.toLowerCase?.()
                                        ?.includes?.(input?.toLowerCase?.())
                                    }
                                    defaultOpen={currentField?.open}
                                    autoFocus={!props.value}
                                    onChange={(e: any) => {
                                      handleViewOptionChange(e, index);
                                      if (e) {
                                        // // 禁用当前选中的项
                                        // const newOption = firstOption.map(
                                        //   (item) => ({
                                        //     ...item,
                                        //     disabled:
                                        //       item.code === e
                                        //         ? true
                                        //         : item.disabled,
                                        //   })
                                        // );
                                        // upeateFirstOption(newOption);
                                        // 获取所有已选中的 code
                                        const fields =
                                          form.getFieldValue("contentList") ||
                                          [];
                                        const selectedCodes = fields
                                          .map((item: any) => item?.title)
                                          .filter(Boolean);

                                        // 统一设置 disabled
                                        const newOption = firstOption.map(
                                          (item) => ({
                                            ...item,
                                            disabled: selectedCodes.includes(
                                              item.code
                                            ),
                                          })
                                        );
                                        upeateFirstOption(newOption);
                                      } else {
                                        // 恢复
                                        const fields =
                                          form.getFieldValue("contentList");
                                        const removed: any = fields[index];
                                        if (
                                          removed &&
                                          removed.code &&
                                          !removed.title
                                        ) {
                                          removed.title = removed.code;
                                        }
                                        // 这种是右侧表单有输入有效值，如果没有，仅左侧表单选择了，此时删除，removed是没有其他属性，就一个open,其他都是undefined
                                        if (removed && removed.title) {
                                          const newOption = firstOption.map(
                                            (item) => ({
                                              ...item,
                                              disabled:
                                                item.code === removed.title
                                                  ? false
                                                  : item.disabled,
                                            })
                                          );
                                          upeateFirstOption(newOption);
                                        } else {
                                          // 左侧选择了又立即删除
                                          const disabledFileds = fields
                                            ?.filter?.((f: any) => f.code)
                                            ?.map((m: any) => m.code);
                                          const newOption = firstOption.map(
                                            (item) => ({
                                              ...item,
                                              disabled: disabledFileds.includes(
                                                item.code
                                              ),
                                            })
                                          );
                                          upeateFirstOption(newOption);
                                        }
                                      }
                                    }}
                                    placeholder="请选择筛选项"
                                  />
                                </Item>
                              );
                            }}
                          </Item>
                        </Col>
                        <Col className={styles["form-list-item-col-field"]}>
                          <Form.Item
                            className={styles["form-list-item-col-itemX"]}
                            shouldUpdate={(prevValues, curValues) =>
                              prevValues?.contentList?.[index]?.title !==
                              curValues?.contentList?.[index]?.title
                            }
                          >
                            {({ getFieldValue }) => {
                              // 如果值是下拉框类型的，确定下拉框的options
                              const fields = getFieldValue("contentList");
                              const currentField = fields[index];
                              const firstOptionItem =
                                find(firstOption, {
                                  code: currentField.title,
                                }) || {};
                              const currentOptions =
                                otherOptionData[currentField.title] || [];
                              const labelStr =
                                find(firstOption, {
                                  value: currentField.title,
                                })?.label || "";
                              const placeholderStr = "请选择目标值";
                              let secondFieldNode = (
                                <Select
                                  mode="multiple"
                                  options={currentOptions}
                                  style={{}}
                                  variant="outlined"
                                  dropdownStyle={{}}
                                  maxTagCount="responsive"
                                  // tagRenderProps={{ className: styles['option-select-item'] }}
                                  placeholder={placeholderStr}
                                  // single={['hasMiddleOrder', 'hasBrief', 'orderType'].includes(
                                  //   currentField.title,
                                  // )}
                                  onChange={(e: any, b: any) => {
                                    const fields =
                                      form.getFieldValue("contentList");
                                    if (
                                      [
                                        "hasMiddleOrder",
                                        "hasBrief",
                                        "orderType",
                                      ].includes(currentField.title)
                                    ) {
                                      const end: any = b[b.length - 1] || {};
                                      fields[index].content = [end.value];
                                    } else {
                                      if (
                                        [
                                          "neederIdList",
                                          "goodsIdList",
                                          "supplierIdList",
                                        ].includes(currentField.title)
                                      ) {
                                        // query/filter 接口被需求方、素材类型、外包供应商同时使用，接口返回的filterId 在素材类型上会出现重复问题，引发页面展示错误
                                        fields[index].content = b;
                                      } else {
                                        fields[index].content = e;
                                      }
                                    }
                                    form.setFieldValue("contentList", fields);
                                  }}
                                  className={
                                    styles["form-list-item-multiple-select"]
                                  }
                                />
                              );
                              if (firstOptionItem.dataType == "numeric") {
                                secondFieldNode = (
                                  <RangeInput
                                    key={currentField.title}
                                    suffix={firstOptionItem.symbol}
                                  />
                                );
                              }
                              // 日期类型,周用下拉，月用月的日期组件，日用起止日期组件
                              // 首次上线时间特殊处理
                              if (
                                (firstOptionItem.dataType == "date" &&
                                  dateGranularity == "DAY") ||
                                firstOptionItem.code == "self_material_put_time"
                              ) {
                                secondFieldNode = (
                                  <RangePicker
                                    style={{ width: "100%" }}
                                    format="YYYY-MM-DD"
                                    key={currentField.title}
                                  />
                                );
                              }
                              // 首次上线时间特殊处理,不能是周
                              if (
                                firstOptionItem.dataType == "date" &&
                                dateGranularity == "WEEK" &&
                                firstOptionItem.code !== "self_material_put_time"
                              ) {
                                secondFieldNode = (
                                  <Select
                                    mode="multiple"
                                    allowClear
                                    style={{ width: "100%" }}
                                    placeholder="请选择周"
                                    options={dateWeek}
                                    key={currentField.title}
                                  />
                                );
                              }
                              // 首次上线时间特殊处理,不能是月
                              if (
                                firstOptionItem.dataType == "date" &&
                                dateGranularity == "MONTH" &&
                                firstOptionItem.code !== "self_material_put_time"
                              ) {
                                secondFieldNode = (
                                  <RangePicker
                                    picker="month"
                                    key={currentField.title}
                                  />
                                );
                              }

                              // 除了日期和数值类型，其他都是文本类型
                              // if (
                              //   firstOptionItem.dataType &&
                              //   !["date", "numeric"].includes(
                              //     firstOptionItem.dataType
                              //   )
                              // ) {
                              //   const typeProps =
                              //     currentField.title == "id"
                              //       ? { type: "number" }
                              //       : {};
                              //   secondFieldNode = (
                              //     <InputX
                              //       key={currentField.title}
                              //       placeholder={`请输入${labelStr}`}
                              //       {...typeProps}
                              //     />
                              //   );
                              // }

                              // 前端改枚举
                              // 素材类型：self_material_type
                              // 是否真人：self_is_real_remark
                              // 秒数（组）-real person：custom_group_duration_real_person
                              // 秒数（组）-illustration：custom_group_duration_illustration
                              // 枚举配置
                              // if (
                              //   useSelect &&
                              //   firstOptionItem.code &&
                              //   Object.keys(ENUM_OPTIONS).includes(
                              //     firstOptionItem.code
                              //   )
                              // ) {
                              //   secondFieldNode = (
                              //     <Select
                              //       mode="multiple"
                              //       showSearch
                              //       allowClear
                              //       style={{ width: "100%" }}
                              //       placeholder="请选择"
                              //       options={
                              //         ENUM_OPTIONS[firstOptionItem.code]
                              //       }
                              //       key={currentField.title}
                              //       filterOption={(input: any, option: any) =>
                              //         (option?.label ?? "")
                              //           ?.toLowerCase?.()
                              //           ?.includes?.(input?.toLowerCase?.())
                              //       }
                              //     />
                              //   );
                              // }

                              return (
                                <div className={styles.SecondEditeBlock}>
                                  <Form.Item
                                    name={[name, "content"]}
                                    label=""
                                    labelAlign="left"
                                    className={
                                      styles["form-list-item-col-itemX"]
                                    }
                                  >
                                    {currentField.title ? (
                                      secondFieldNode
                                    ) : (
                                      <Select
                                        options={[]}
                                        placeholder="请选择目标值"
                                        notFoundContent={
                                          <div>请先选择左侧下拉</div>
                                        }
                                        suffixIcon={
                                          <CaretDownOutlined
                                            style={{
                                              color: "#86909C",
                                              fontSize: 12,
                                            }}
                                          />
                                        }
                                      />
                                    )}
                                  </Form.Item>
                                  {!props.labelDisabled && (
                                    <div
                                      className={clsx(
                                        "link-button",
                                        styles.removeBtn
                                      )}
                                      // style={
                                      //   disabledPartName.includes(currentField.title)
                                      //     ? { display: 'none' }
                                      //     : {}
                                      // }
                                      onClick={(e) => {
                                        // 恢复被删除项的可选
                                        const fields =
                                          form.getFieldValue("contentList");
                                        const removed = fields[index];
                                        if (removed && removed.title) {
                                          const newOption = firstOption.map(
                                            (item) => ({
                                              ...item,
                                              disabled:
                                                item.code === removed.title
                                                  ? false
                                                  : item.disabled,
                                            })
                                          );
                                          upeateFirstOption(newOption);
                                        }
                                        remove(name);
                                        e.stopPropagation();
                                      }}
                                    >
                                      <SinoSymbolFont
                                        type="icon-删除删除条件"
                                        style={{
                                          marginLeft: 2,
                                          fontSize: 16,
                                        }}
                                      />
                                    </div>
                                  )}
                                </div>
                              );
                            }}
                          </Form.Item>
                        </Col>
                      </Row>
                    </Item>
                  );
                })}
              </Col>
              {!props.labelDisabled && (
                <Col span={24}>
                  <Item
                    className={clsx(
                      styles["form-list-item-col-item"],
                      styles["action-item"]
                    )}
                  >
                    <div
                      className={clsx(
                        "flex justify-between items-center",
                        styles["form-list-add-btn"]
                      )}
                    >
                      <span
                        className={clsx(
                          "link-button",
                          styles["form-list-item-add-btn"]
                        )}
                        onClick={() => {
                          const fields = form.getFieldValue("contentList");
                          fields.forEach((fieldItem: any) => {
                            fieldItem.open = false;
                          });
                          // 添加的时候讲其他的open 设置为false， 防止删除的时候，多个情况下都展开了下拉
                          form.setFieldValue("contentList", fields);
                          add({ ...contentListTpl, open: true });
                          // 修复多个筛选项，再次添加时，下拉选中项被遮挡，需要操作2次才可以选中
                          handleScrollToBottom();
                        }}
                      >
                        <IconSvg
                          type="icontianjiashaixuantiaojian"
                          style={{ marginRight: 9, fontSize: 16 }}
                        />
                        添加筛选条件
                      </span>
                      {fields.length ? (
                        <span
                          className={styles["clear-btn"]}
                          onClick={handleAllClear}
                        >
                          清空全部筛选条件
                        </span>
                      ) : null}
                    </div>
                  </Item>
                </Col>
              )}
            </>
          );
        }}
      </Form.List>
    </div>
  );
};

export default FormList;
