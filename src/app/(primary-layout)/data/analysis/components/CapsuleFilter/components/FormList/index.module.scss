.form-list-item-wrap {
  margin-bottom: 10px!important;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0px;
  }

  .form-list-item-col {
    display: flex;
    align-items: center;
  }

  .form-list-item-col-field {
    padding-right: 0px !important;
    height: 32px;
  }
}

.form-list-item-col-item {
  margin-bottom: 0!important;
  width: 180px;
  &.action-item{
    width:100%;
  }
}

.form-list-item-col-itemX {
  margin-left: 6px!important;
  margin-bottom: 0!important;
  width: 316px;
  height: 32px
}

.form-list-add-btn {
  padding-top: 16px;
}

.clear-btn {
  cursor: pointer;
  position: relative;
  left: -18px;
}

.option-select-item {
  >div {
    background-color: #F2F3F5 !important;
    border-radius: 6px;
    // border: 1px solid #c9cdd4;
  }
}

.field-item-col {
  max-height: 200px;
  overflow: hidden auto;
}

.form-list-item-add-btn {
  padding: 5px 12px;
  border-radius: 6px;
  display: flex;
  align-items: center;

  &:hover {
    background: #f2f3f5;
  }
}

.SecondEditeBlock {
  display: flex;
  align-items: center;
  position: relative;
  height: 32px;
}

.removeBtn {
  position: absolute;
  right: -26px;
}

.form-list-item-multiple-select{
  :global{
    .ant-select-selection-overflow{
      // 防止选择后又粘贴一个长的进来搜索会导致位置发生变化
      flex-wrap: nowrap! important;
      overflow: hidden;
    }
  }
}