import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Tooltip, Form } from "antd";
import find from "lodash/find";
import isUndefined from "lodash/isUndefined";
import isString from "lodash/isString";
import clsx from "classnames";
import FormList from "./components/FormList";
// import { getProjeceGoodsList, getUserByRole, getUserDesignerList } from '@/services/order';
import {
  getCreativeAutoAnalysisCustomInfo,
  getCreativeAutoAnalysisDateWeek,
  getCreativeAutoAnalysisTextColumnEnumList,
} from "../../../services";
import { getDict } from "@/services/common";
import dayjs from "dayjs";
// import { orderTableFilterOptions } from '../../const';
import SinoSymbolFont from "@/components/Icon/SinoSymbolFont";
import debounce from "lodash/debounce";
import { ENUM_OPTIONS } from "./util";

dayjs.locale("zh-cn");
import styles from "./index.module.scss";

interface CapsuleFilterIprops {
  className?: string;
  tableFilterOption?: any[]; // 表格下拉筛选项
  valuesChange?: (changedValues: any, allValues: any, viewOptions: any) => void; // 表单值发生修改时触发
  value?: any; // 筛选项回显
  labelDisabled?: boolean;
  disabledPartName?: any; // 禁用编辑的选项 里面采用title字段名称例如 'launchTime'
  neederIdListOptions?: any; // 需求方下拉列表
  goodsListOptions?: any; // 素材类型下拉列表
  orderStatusOptions?: any; // 订单状态下拉列表
  supplierIdListOptions?: any; // 供应商下拉列表
  createBriefOptions?: any; // 是否创建brief 下拉列表
  bindOPOptions?: any; // 是否绑定op 下拉列表
  orderTypeOptions?: any; // 订单来源
  filterText?: string; // 筛选文本
  bordered?: boolean; // 是否有边框
  id: number; // 分析主题ID 或 视图ID
  type: string; // 分析主题 或 视图
  showFilterTextSelect?: boolean; // 是否展示筛选文本下拉
}
// 找到option ,从里面拿出需要上传的name 等参数
function mergeViewOptionsToAllValues(allValues: any, viewOptions: any[]) {
  if (!allValues?.contentList?.length) return [];
  return allValues.contentList.map((item: any) => {
    const matched = viewOptions.find((opt) => opt.code === item?.title);
    // 以 contentList 的 item 为主，补充 viewOptions 的字段，不直接析构matched
    const currentValue = matched
      ? {
          id: matched.id,
          name: matched.name,
          code: matched.code,
          dataType: matched.dataType,
          // 筛选type 为BASIS_DIMENSION:基础维度、RANGE:赋值字段
          type: matched.type,
          // date 的类型判断需要结合此字段
          dateGranularity: matched.dateGranularity,
          ...item,
        }
      : item;
    // 拼装接口需要的参数

    if (currentValue.dataType === "date") {
      if (["MONTH", "DAY"].includes(currentValue.dateGranularity)) {
        const dateStr =
          currentValue.dateGranularity === "MONTH" ? "month" : "day";
        if (currentValue.content?.[0]) {
          currentValue.startTime = dayjs(currentValue.content[0])
            .startOf(dateStr)
            .valueOf();
        }
        if (currentValue.content?.[1]) {
          currentValue.endTime = dayjs(currentValue.content[1])
            .endOf(dateStr)
            .valueOf();
        }
        //不传value
        delete currentValue.value;
        delete currentValue.minValue;
        delete currentValue.maxValue;
        delete currentValue.valueList;
        delete currentValue.filterValueList;
      }
      if (currentValue.dateGranularity === "WEEK") {
        currentValue.valueList = currentValue.content;
        //不传value
        delete currentValue.value;
        delete currentValue.startTime;
        delete currentValue.endTime;
        delete currentValue.minValue;
        delete currentValue.maxValue;
      }
    } else if (currentValue.dataType === "numeric") {
      currentValue.minValue = currentValue.content?.[0];
      currentValue.maxValue = currentValue.content?.[1];
      //不传value
      delete currentValue.value;
      delete currentValue.startTime;
      delete currentValue.endTime;
      delete currentValue.valueList;
      delete currentValue.filterValueList;
    } else {
      // 文本类型
      currentValue.valueList = currentValue.content;
      //不传startTime\endTime\maxValue\minValue
      delete currentValue.startTime;
      delete currentValue.endTime;
      delete currentValue.minValue;
      delete currentValue.maxValue;
      delete currentValue.filterValueList;
    }
    // 去除多余字段
    delete currentValue.dateGranularity;
    delete currentValue.content;
    delete currentValue.open;
    return currentValue;
  });
}

const CapsuleFilter = forwardRef((props: CapsuleFilterIprops, ref: any) => {
  const {
    className,
    valuesChange = () => {},
    value = [],
    labelDisabled,
    disabledPartName = [],
    neederIdListOptions = [],
    goodsListOptions = [],
    orderStatusOptions = [],
    supplierIdListOptions = [],
    createBriefOptions = [],
    bindOPOptions = [],
    orderTypeOptions = [],
    filterText = "筛选",
    bordered,
    id,
    type,
    showFilterTextSelect = false,
  } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [viewOptions, setViewOptions] = useState<any>([]);
  // 日期类型，以哪种组件进行展示
  const [dateGranularity, setDateGranularity] = useState<string>("");
  const [viewButtonList, setViewButtonList] = useState<any>([...value]); // 筛选入口展示的筛选项
  const [otherOptionData, setOtherOptionData] = useState<any>({});
  const otherOptionRef = useRef<any>({}); // 缓存带key 的数据，方便后面查找
  const title = "设置筛选条件";
  const [form] = Form.useForm();
  const [dateWeek, setDateWeek] = useState<any>([]);
  // 禁用列
  const [currentDisabledPartName, setCurrentDisabledPartName] =
    useState(disabledPartName);

  // 设置禁用列
  useEffect(() => {
    // props.disabledPartName 变化时优先设置
    if (props.disabledPartName) {
      setCurrentDisabledPartName(props.disabledPartName);
    }
  }, [props.disabledPartName]);

  // 获取左侧动态表单字段
  const fetchViewOption = async () => {
    try {
      const { data } = await getCreativeAutoAnalysisCustomInfo({ id, type });
      if (data?.code == 0) {
        const { result = {} } = data;
        // 存储日期类型
        setDateGranularity(result.dateGranularity);
        if (result?.dateGranularity == "WEEK") {
          // 仅周类型需要执行请求下拉数据，"DAY", "MONTH"不需要
          fetchDateWeek();
        }
        const allList = [
          ...(result.basisDimensionInfoList || []),
          ...(result.basisIndicatorInfoList || []),
          ...(result.indicatorInfoList || []),
          ...(result.partitionDimensionInfoList || []),
        ];
        let currentOptions = allList
          ?.map((m: any) => {
            const currentItem = {
              ...m,
              label: m.name,
              value: m.code,
              key: m.code,
              dateGranularity: result.dateGranularity,
            };
            // 首次上线日期特殊处理
            if(currentItem.code === "self_material_put_time") {
              currentItem.dateGranularity = 'DAY';
            }
            return currentItem;
          })
          ?.filter((item: any) => {
            // 如果showFilterTextSelect为false，则过滤文本类型，仅保留date和numeric类型
            if (!showFilterTextSelect) {
              if (["date", "numeric"].includes(item.dataType)) {
                return true;
              }
              return false;
            }
            return true;
          });
        // 禁用列根据接口数据进行设置
        if (result.filterEditDTOList?.length) {
          // 设置已经选中
          const selectedValue = result.filterEditDTOList?.map((item: any) => {
            const matched = currentOptions.find(
              (opt: any) => opt.code === item?.code
            );
            // 以 contentList 的 item 为主，补充 viewOptions 的字段，不直接析构matched
            const currentItem = matched
              ? {
                  id: matched.id,
                  name: matched.name,
                  code: matched.code,
                  dataType: matched.dataType,
                  // date 的类型判断需要结合此字段
                  dateGranularity: matched.dateGranularity,
                  ...item,
                }
              : item;
            if (currentItem.dataType === "date") {
              if (["MONTH", "DAY"].includes(currentItem.dateGranularity)) {
                const dateStr =
                  currentItem.dateGranularity === "MONTH" ? "month" : "day";
                if (currentItem.startTime && currentItem.endTime) {
                  currentItem.content = [
                    dayjs(currentItem.startTime),
                    dayjs(currentItem.endTime),
                  ];
                }
              }
              if (currentItem.dateGranularity === "WEEK") {
                currentItem.content = item.valueList;
              }
            } else if (currentItem.dataType === "numeric") {
              if (!currentItem.content) {
                currentItem.content = [
                  currentItem.minValue,
                  currentItem.maxValue,
                ];
              } else {
                if (currentItem.content?.length) {
                  currentItem.content[0] = currentItem.minValue;
                  currentItem.conten[1] = currentItem.maxValue;
                }
              }
            } else {
              // 第一版本的文本类型是输入框，用value字段进行回显
              // currentItem.content = item.value;
              // 第二版本的文本类型是下拉，用valueList字段进行回显
              currentItem.content = item.valueList;
            }
            return {
              ...currentItem,
              title: currentItem.code,
            };
          });
          setViewButtonList(selectedValue);
          // 将列表里面的数据进行禁用
          const disabledFileds = result.filterEditDTOList?.map(
            (item: any) => item.code
          );
          // 放开后，需要对删除的项中的中disabled维护更新
          currentOptions = currentOptions.map((item: any) => {
            if (disabledFileds.includes(item.code)) {
              item.disabled = true;
            }
            return item;
          });
          // 放开后，会造成刷新后，设置过的选项无法修改
          // setCurrentDisabledPartName(disabledFileds);
        } else {
          setCurrentDisabledPartName([]);
        }
        setViewOptions(currentOptions);
      }
    } catch (error) {
      console.error(error, "获取下拉列表错误");
    }
  };

  // 获取文本类型下拉
  const fetchTextColumnEnumList = async () => {
    try {
      const { data } = await getCreativeAutoAnalysisTextColumnEnumList({
        id,
        type,
      });
      if (data?.code == 0) {
        const { result = {} } = data;
        // result 是数组，循环result，将result的code作为key，value作为value，存入otherOptionData
        const otherOptionData: Record<string, any> = {};
        result.forEach((item: any) => {
          // valueList = ["#null#"] 需求替换成中文【空值】，valueList需要排序将#null#放在最后
          item.valueList.sort((a: any, b: any) => {
            if (a === "#null#") {
              return 1;
            }
            if (b === "#null#") {
              return -1;
            }
            return 0;
          });
          const valueList = item.valueList.map((item: any) => {
            if (item === "#null#") {
              return { label: "空值", value: "#null#" };
            }
            return { label: item, value: item };
          });
          otherOptionData[item.code] = valueList;
        });
        setOtherOptionData(otherOptionData);
      }
    } catch (error) {
      console.error(error, "获取文本类型下拉错误");
    }
  };

  // 获取周下拉
  const fetchDateWeek = async () => {
    try {
      const { data } = await getCreativeAutoAnalysisDateWeek({ id, type });
      if (data?.code == 0) {
        const { result = {} } = data;
        setDateWeek(
          result?.map?.((item: any) => ({ label: item, value: item })) || []
        );
      }
    } catch (error) {}
  };

  useEffect(() => {
    if ("tableFilterOption" in props && props.tableFilterOption) {
      setViewOptions(props.tableFilterOption);
    } else {
      fetchViewOption();
    }
  }, [props.tableFilterOption]);

  useEffect(() => {
    if (showFilterTextSelect) {
      fetchTextColumnEnumList();
    }
  }, [showFilterTextSelect]);

  //根据value 变化设置表单
  // useEffect(() => {
  //   form.setFieldValue(["contentList"], value);
  //   setViewButtonList(value);
  // }, [value]);

  const handleOpenChange = (v: boolean) => {
    setVisible(v);
    const fields = form
      .getFieldValue("contentList")
      ?.map?.((item: any) => ({ ...item, open: false }));
    form.setFieldValue(["contentList"], fields);
  };

  const resetCapsuleFilter = () => {
    setViewButtonList([]);
    form.resetFields(["contentList"]);
  };

  useImperativeHandle(ref, () => ({
    resetCapsuleFilter,
    refreshViewOption: fetchViewOption,
    refreshTextColumnEnumList: fetchTextColumnEnumList,
  }));

  // 表单值发生修改时触发
  const onFormValuesChange = (changedValues: any, allValues: any) => {
    if (isUndefined(allValues.contentList)) {
      // 修复筛选的时候，点击了最外层，导致选择的值无法设置到筛选文本后面
      return;
    }
    const selectedList = allValues.contentList.map((item: any) => {
      const matched = viewOptions.find((opt: any) => opt.code === item?.title);
      const currentValue = matched
        ? {
            id: matched.id,
            name: matched.name,
            code: matched.code,
            dataType: matched.dataType,
            type: matched.type, // 筛选type 为BASIS_DIMENSION:基础维度、RANGE:赋值字段
            // date 的类型判断需要结合此字段
            dateGranularity: matched.dateGranularity,
            ...item,
          }
        : item;
      return currentValue;
    });
    handleValuesChange(changedValues, allValues, viewOptions);
    setViewButtonList(selectedList);
  };

  const handleValuesChange = debounce(
    (changedValues: any, allValues: any, options: any) => {
      // 新增筛选项名称或筛选项对应表单不选择值不调列表
      const changeContentList = changedValues.contentList || [];
      const newCurrentField =
        changeContentList?.[changeContentList?.length - 1] || {};
      if (
        ("title" in newCurrentField &&
          newCurrentField.title === undefined &&
          newCurrentField.open) ||
        (!newCurrentField.content &&
          newCurrentField.title &&
          newCurrentField.open)
      ) {
        return;
      }
      // 用法示例
      const mergedList = mergeViewOptionsToAllValues(allValues, options);
      // 回调props 上的valuesChange
      valuesChange(changedValues, allValues, mergedList);
    },
    500
  );

  const contentListStr = useMemo(() => {
    const currentList =
      otherOptionRef.current?.[viewButtonList?.[0]?.title] || [];
    if (!currentList?.length) {
      if (
        ["DAY"].includes(viewButtonList?.[0]?.dateGranularity) &&
        ["date"].includes(viewButtonList?.[0]?.dataType)
      ) {
        return viewButtonList?.[0]?.content?.length > 1
          ? `${dayjs(viewButtonList?.[0]?.content?.[0]).format(
              "YYYY-MM-DD"
            )}-${dayjs(viewButtonList?.[0]?.content?.[1]).format("YYYY-MM-DD")}`
          : "";
      }
      if (
        ["MONTH"].includes(viewButtonList?.[0]?.dateGranularity) &&
        ["date"].includes(viewButtonList?.[0]?.dataType)
      ) {
        return viewButtonList?.[0]?.content?.length > 1
          ? `${dayjs(viewButtonList?.[0]?.content?.[0]).format(
              "YYYY-MM"
            )}-${dayjs(viewButtonList?.[0]?.content?.[1]).format("YYYY-MM")}`
          : "";
      }
      if (
        ["WEEK"].includes(viewButtonList?.[0]?.dateGranularity) &&
        ["date"].includes(viewButtonList?.[0]?.dataType)
      ) {
        return viewButtonList?.[0]?.content?.length > 1
          ? `${dayjs(viewButtonList?.[0]?.content?.[0]).format(
              "YYYY-MM-DD"
            )}-${dayjs(viewButtonList?.[0]?.content?.[1]).format("YYYY-MM-DD")}`
          : "";
      }
      if (["numeric"].includes(viewButtonList?.[0]?.dataType)) {
        return viewButtonList?.[0]?.content?.length > 1
          ? `${viewButtonList?.[0]?.content?.[0] || ""}-${
              viewButtonList?.[0]?.content?.[1] || ""
            }`
          : "";
      }
      // 文本下拉类型 ["#null#", "-王建测试编辑111"]，需要将#null# 替换成 空值
      // return viewButtonList?.[0]?.content?.join?.(",");
      const textList = viewButtonList?.[0]?.content?.map?.((item: string) => {
        if (item === "#null#") {
          return "空值";
        }
        return item;
      });
      return textList?.join?.(",");
    }
    const idList = currentList.filter((item: any) => {
      if (
        viewButtonList?.[0]?.content?.includes(
          item.value == 0 ? "0" : String(item.value || item.id || item.goodsId)
        )
      ) {
        return true;
      }
      return false;
    });
    return idList
      .map((m: any) => m.goodsName || m.userLogin || m.name || m.label)
      ?.join(",");
  }, [viewButtonList]);

  const filterNode = useMemo(() => {
    let content =
      viewButtonList?.[0]?.content &&
      Array.isArray(viewButtonList?.[0]?.content)
        ? contentListStr
        : viewButtonList?.[0]?.content;

    if (content?.[0]?.date) {
      content = [
        dayjs(content[0]).format("YYYY-MM"),
        dayjs(content[1]).format("YYYY-MM"),
      ].join(" ~ ");
    }
    return (
      <>
        {viewButtonList?.length && viewButtonList?.[0]?.content ? (
          <span style={{ margin: "0 4px" }}>·</span>
        ) : null}
        {find(viewOptions, { code: viewButtonList?.[0]?.title })?.name ||
          (viewButtonList?.[0]?.title === "launchTime" && "投放时间")}
        &nbsp;
        {viewButtonList?.[0]?.content &&
        Array.isArray(viewButtonList?.[0]?.content)
          ? contentListStr
          : content}
      </>
    );
  }, [viewButtonList, contentListStr, viewOptions]);

  const updateViewOptions = (options: any) => {
    setViewOptions(options);
  };

  return (
    <div>
      <Tooltip
        open={visible}
        title={
          <div style={{ minWidth: 550 }}>
            <div className={styles.title}>{title}</div>
            <div className={styles.body}>
              <Form form={form} onValuesChange={onFormValuesChange}>
                <FormList
                  firstOption={viewOptions}
                  upeateFirstOption={updateViewOptions}
                  form={form}
                  otherOptionData={otherOptionData}
                  value={viewButtonList}
                  labelDisabled={labelDisabled}
                  updateViewButtonList={setViewButtonList}
                  handleValuesChange={handleValuesChange}
                  disabledPartName={currentDisabledPartName}
                  dateGranularity={dateGranularity}
                  dateWeek={dateWeek}
                />
              </Form>
            </div>
          </div>
        }
        trigger={["click"]}
        arrow={false}
        placement="bottomLeft"
        rootClassName={styles["overlay-content"]}
        onOpenChange={handleOpenChange}
        destroyTooltipOnHide={true}
      >
        <div
          className={clsx(styles["order-filter-entry-wrap"], className, {
            [styles["order-filter-entry-wrap-active"]]:
              !!viewButtonList?.length,
            [styles["order-filter-entry-wrap-border"]]: bordered,
          })}
        >
          <div className={clsx(styles["order-filter-entry-wrap-children"])}>
            <div className={styles["project-filter-entry"]}>
              <SinoSymbolFont
                type="icon-zidingyilie"
                style={{ marginRight: 8, fontSize: 14 }}
              />
              {filterText}
            </div>
          </div>
          <div className={styles["order-filter-entry-wrap-value"]}>
            {viewButtonList?.length > 1 ? (
              <>
                <span style={{ margin: "0 4px" }}>·</span>
                <span>{viewButtonList?.length}</span>
              </>
            ) : null}
            {viewButtonList?.length == 1 ? (
              <Tooltip
                className={styles["order-filter-entry-wrap-value-tooltip"]}
                title={
                  <div className={styles["project-filter-entry-tooltip-title"]}>
                    {filterText}
                    {filterNode}
                  </div>
                }
              >
                {filterNode}
              </Tooltip>
            ) : null}
          </div>
        </div>
      </Tooltip>
    </div>
  );
});
CapsuleFilter.displayName = "CapsuleFilter";
export default CapsuleFilter;
