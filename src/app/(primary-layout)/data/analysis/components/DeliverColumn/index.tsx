/*
 * @Date: 
 * @Author: miroku.yang
 * @Description: 素材列
 */
import styles from './index.module.scss';
import clsx from "classnames";
import React from 'react';
import IconSvg from '@/components/IconSvg';

interface IProps {
  showDeliverModal: (p?: any) => void;
  children?: any;
  totalDeliverNum: any;
}
const DeliverColumn = (props: IProps) => {
  const { showDeliverModal, children, totalDeliverNum } = props;
  const handleClick = () => {
    showDeliverModal();
  };
  return (
    <div
      onClick={handleClick}
      className={clsx('flex items-center deliver-component', styles['deliver-component'], {
        [styles['no-total-deliver-num']]: !totalDeliverNum,
      })}
    >
      <span className={styles['deliver-number']}>{children}</span>
      <span className={clsx(styles['deliver-more'], 'flex items-center justify-center')}>
        <IconSvg type="icona-you1" style={{ fontSize: 8 }} />
      </span>
    </div>
  );
};
export default DeliverColumn;
