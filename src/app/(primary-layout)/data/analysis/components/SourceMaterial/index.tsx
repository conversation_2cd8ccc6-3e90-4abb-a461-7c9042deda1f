/*
 * @Date: 2025-05-23 14:30:11
 * @Author: miroku.yang
 * @Description: 素材url 列
 */
import React, { useState } from "react";
import { useModalWrap } from "@/components";
import styles from "./index.module.scss";
import DeliverList from "../DeliverList";
import WorksColumn from "../WorksColumn";
import DeliverColumn from "../DeliverColumn";
import { FileHelper } from "@/lib/common";
import usePreview from "@/components/usePreview";

import clsx from "classnames";

interface IProps {
  materialList: any;
  showCard: number;
  modalTitle:string;
}
const SourceMaterial = (props: IProps) => {
  const { materialList,showCard ,modalTitle} = props;
  const { show: showPreview, render: renderPreview } = usePreview();
  // 卡片hover
  const handlePreview = (list: any, idx: number) => {
    const newFiles = list.map((item: any) => {
      // 有的数据结构是有fileInfo 这层，有的之间在item 中
      const file =
        item.fileInfo && "url" in item.fileInfo ? item.fileInfo : item;
      return {
        url: file.url,
        uuid: file.uuid,
        name: file.name || "",
        mimeType: FileHelper.getType(file.url),
        src: file.url || file?.originData?.url,
        poster: file?.originData?.previewInfo?.resizeImageUrl,
        previewUrl: file?.originData?.previewInfo?.resizeImageUrl,
        iframeUrl:
          file.documentPreviewUrl || file?.originData?.documentPreviewUrl,
        hideFullScreenDownloadIcon: false,
        transcodeVideoUrlList: file?.streamInfo?.transcodeVideoUrlList || [],
      };
    });
    showPreview({
      views: newFiles,
      current: idx,
      useXgPlayer: true,
    });
  };

  // 素材弹窗
  const { showModal, render } = useModalWrap({
    modalProps: {
      width: 1150,
      title: (
        <div id="modalTitle" className={styles["modal-title"]}>
          {modalTitle}
        </div>
      ),
      className: styles["modal-wrap"],
      footer: null,
    },
    children: ({ showParams = {} }: any) => {
      if (showParams?.modalType == "deliver") {
        return (
          <DeliverList showPreview={handlePreview} list={showParams.list} />
        );
      }
      return null;
    },
  });

  const text = materialList?.length || 0;
  return (
    <div className={clsx("flex items-center", styles["padding-left"])}>
      {materialList?.length ? (
        <WorksColumn list={('showCard' in props)?materialList.slice(0,showCard):materialList} showPreview={handlePreview} />
      ) : (
        <span
          style={{
            color: "#86909c",
            fontSize: 12,
            display: "inline-block",
            minWidth: 65,
          }}
        >
          暂无素材
        </span>
      )}
      <DeliverColumn
        showDeliverModal={() => {
          showModal({
            modalType: "deliver",
            list: materialList,
          });
        }}
        totalDeliverNum={text}
      >
        {text || 0}个
      </DeliverColumn>
      <div style={{ height: 0, overflow: "hidden" }}>
        {/* 卡片hover */}
        {renderPreview()}
        {/* 素材弹窗 */}
        {render()}
      </div>
    </div>
  );
};

export default SourceMaterial;
