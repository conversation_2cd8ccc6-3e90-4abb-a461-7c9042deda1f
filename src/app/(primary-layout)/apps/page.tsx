"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Checkbox } from "antd";
import { cn } from "@/lib/utils";
import styles from './page.module.scss'

interface ProductCard {
  id: number;
  title: string;
  description: string;
  image: string;
  category: string;
  categoryName: string;
  path: string;
  footerTitle?: string;
  footerDescription?: any;
}

const allProductCards: ProductCard[] = [
  // 数据工具
  {
    id: 1,
    title: "生成广告视频",
    description: "根据基本信息快速生成新视频。",
    image: "https://static.creativebooster.com/ai/idea.jpg",
    category: "1",
    categoryName: "常用工具",
    // path: "/apps/creative-studio/generate",
    path: "/apps/creative-studio/home",
    footerTitle: "视频混剪",
    footerDescription: (
      <div className="!mt-1 !text-gray-800 !text-sm px-4">
        <div>剪辑方式：</div>
        <div>
          <Checkbox checked className={cn("!text-gray-600 !text-sm",styles['checkbox'])}>数字人</Checkbox><br/>
          <Checkbox checked className={cn("!text-gray-600 !text-sm",styles['checkbox'])}>增加版权素材</Checkbox><br/>
          <Checkbox checked className={cn("!text-gray-600 !text-sm",styles['checkbox'])}>原视频混剪</Checkbox>
        </div>
      </div>
    ),
  },
  {
    id: 2,
    title: "短剧快剪",
    description: "提取剧中精彩故事片段并批量合成素材。",
    image: "https://static.creativebooster.com/ai/video-clip.jpg",
    category: "3",
    categoryName: "素材生产",
    path: "/apps/creative-tools/video-clip",
  },
];

const categories = [
  { key: "all", label: "全部应用" },
  { key: "1", label: "常用工具" },
  { key: "2", label: "编导灵感" },
  { key: "3", label: "素材生产" },
  { key: "4", label: "合成量产" },
  { key: "5", label: "投放分发" },
];

export default function ProductCardsPage() {
  const [favorites, setFavorites] = useState<number[]>([]);
  const [activeCategory, setActiveCategory] = useState("all");

  const router = useRouter();

  // const toggleFavorite = (id: number) => {
  //   if (favorites.includes(id)) {
  //     setFavorites(favorites.filter((favId) => favId !== id))
  //   } else {
  //     setFavorites([...favorites, id])
  //   }
  // }

  const handleCardClick = (card: any) => {
    if (card.path) {
      router.push(card.path);
    }
  };

  // 根据选中的分类过滤卡片
  const filteredCards =
    activeCategory === "all"
      ? allProductCards
      : allProductCards.filter((card) => card.category === activeCategory);

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-4 mb-6">
            {categories.map((category) => (
              <button
                key={category.key}
                className={`flex items-center px-4 py-1 rounded-lg transition-all duration-200 ${
                  activeCategory === category.key
                    ? "bg-blue-500 text-white shadow-md"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300 hover:shadow-sm"
                }`}
                onClick={() => setActiveCategory(category.key)}
              >
                {category.label}
                <span className="ml-1 text-xs opacity-75">
                  (
                  {category.key === "all"
                    ? allProductCards.length
                    : allProductCards.filter(
                        (card) => card.category === category.key
                      ).length}
                  )
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Cards Grid or Empty State */}
        {filteredCards.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 gap-6">
            {filteredCards.map((card) => (
              <div
                key={card.id}
                className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 cursor-pointer"
                onClick={() => handleCardClick(card)}
              >
                {/* Card Image Section */}
                <div className="relative overflow-hidden h-48 group">
                  <Image
                    src={card.image}
                    alt={card.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, (max-width: 1536px) 25vw, 20vw"
                  />
                  {/* Category Badge */}
                  <div className="absolute top-3 left-3 z-20">
                    <span className="bg-black/60 backdrop-blur-sm text-white px-3 py-1 rounded-full text-xs font-medium">
                      {card.categoryName}
                    </span>
                  </div>
                </div>
                {/* Card Content */}
                <div className="py-3 space-y-2">
                  <div className="px-4">
                    <h3
                      className="font-semibold text-gray-900 text-base mb-1 truncate"
                      title={card.title}
                    >
                      {card.title}
                    </h3>
                    <p
                      className="text-gray-600 text-sm line-clamp-2 min-h-[40px]"
                      title={card.description}
                    >
                      {card.description}
                    </p>
                  </div>

                  {/* Footer */}
                  <div className="px-4 flex items-center justify-between pt-2 border-t border-gray-100">
                    <div className="flex items-center space-x-2">
                      <div className="w-5 h-5 bg-blue-500 rounded-sm flex items-center justify-center">
                        <span className="text-white text-xs font-bold">
                          {card?.footerTitle
                            ? card?.footerTitle?.slice(0, 1)
                            : "C"}
                        </span>
                      </div>
                      <span className="text-sm text-gray-600">
                        {card?.footerTitle || "Creative Booster"}
                      </span>
                    </div>
                    {/* <button
                      className={`transition-colors duration-200 ${
                        favorites.includes(card.id) ? "text-red-500" : "text-gray-400 hover:text-red-500"
                      }`}
                      onClick={() => toggleFavorite(card.id)}
                      title={favorites.includes(card.id) ? "取消收藏" : "添加收藏"}
                    >
                      <HeartIcon filled={favorites.includes(card.id)} className="text-lg" />
                    </button> */}
                  </div>
                  {card?.footerDescription || null}
                </div>
              </div>
            ))}
          </div>
        ) : (
          // Empty State - 敬请期待
          <div className="flex flex-col items-center justify-center py-20 px-4">
            <div className="w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-6 shadow-lg">
              <svg
                className="w-16 h-16 text-blue-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">敬请期待</h3>
            <p className="text-gray-600 text-center max-w-md mb-2 leading-relaxed">
              该分类下暂无应用，我们正在努力开发中
            </p>
            <p className="text-gray-500 text-sm text-center max-w-md mb-8">
              请关注后续更新，或查看其他分类的精彩应用
            </p>
            <div className="flex gap-3">
              <button
                className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 shadow-md hover:shadow-lg"
                onClick={() => setActiveCategory("all")}
              >
                查看全部应用
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
