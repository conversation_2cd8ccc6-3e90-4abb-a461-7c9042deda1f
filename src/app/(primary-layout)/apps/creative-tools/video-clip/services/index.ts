import requestHelper from "@/utils/requestHelper";

/**
 * 创建任务
 */
export function taskCreate(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/task/create",
    data
  );
}

/**
 * 上传文件绑定任务
 * @param data
 * @returns
 */
export function bindFileTask(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/bind/video/files",
    data
  );
}

/**
 * 取消任务
 * @param data
 * @returns
 */
export function taskCancel(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/task/cancel",
    data
  );
}

/**
 * 任务重试
 * @param data
 * @returns
 */
export function taskRetry(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/task/retry",
    data
  );
}

/**
 * 使用ai对文件列表进行排序
 * @param data
 * @returns
 */
export function sortNoByAI(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/drama/series/no/recognize",
    data
  );
}

/**
 * 任务列表
 * @param data
 * @returns
 */
export function getTaskList(data = {}) {
  return requestHelper.post("v4/demand/ai/task/list", data);
}

/**
 * 视频片段提取、合成失败重试 （故事线页面）
 * @param data
 * @returns
 */
export function getShortHighlightsRetry(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/video/extra/retry",
    data
  );
}

/**
 * 短剧剪辑版本列表查询
 * @param data
 * @returns
 */
export function getShortDramaVersionList(data = {}) {
  return requestHelper.post(
    "/v4/demand/short/drama/highlights/task/story/line/version/list",
    data
  );
}