/*
 * @Date: 2025-07-25 10:34:24
 * @Author: miroku.yang
 * @Description: 
 */
import { format, addSeconds } from "date-fns";

/**
 * 格式化秒数为时间显示
 * @param seconds 秒数
 * @returns 时间字符串
 */
export const formatSecondsToTime = (seconds: number | string): string => {
  const numSeconds = typeof seconds === 'string' ? parseFloat(seconds) : seconds;
  if (isNaN(numSeconds) || numSeconds < 0) return '00:00';

  const baseDate = new Date(2000, 0, 1, 0, 0, 0);
  const resultDate = addSeconds(baseDate, numSeconds);

  if (numSeconds >= 3600) {
    return format(resultDate, 'HH:mm:ss');
  } else {
    return format(resultDate, 'mm:ss');
  }
};

export const VIDEO_CLIP_EVENT_NAME = {
  GENERATE_CREATE_SUCCESS: Symbol('GENERATE_CREATE_SUCCESS'),
}