"use client";

import { useState, useRef, type DragEvent, type ChangeEvent } from "react";
import { message } from "antd";
import { CloudUploadOutlined } from "@ant-design/icons";

export type FileType = File & { uid: string };

export interface FileDraggerProps {
  disabled?: boolean;
  accept?: string;
  onChange?: (files: FileType[], cb?: () => void) => void;
}

export default function FileDragger({
  disabled = false,
  accept = "*/*",
  onChange,
}: FileDraggerProps) {
  const [loading, setLoading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  };

  const handleFileSelect = (e: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    handleFiles(files);
  };

  const handleFiles = async (files: File[]) => {
    if (files.some((file: File) => !file?.type?.includes("video"))) {
      message.warning("仅支持上传视频格式文件");
      return;
    }
    const newFiles = [...files].map((file: any, index: number) => {
      file.uid = `rc-upload-${Date.now()}-${index + 1}`;
      return file;
    });
    setLoading(true);
    onChange?.(newFiles, () => setLoading(false));
    setTimeout(() => {
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }, 0);
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  if (disabled || loading) {
    return (
      <div className="relative border border-dashed rounded-lg p-12 text-center border-gray-300 bg-gray-50 opacity-60 cursor-not-allowed">
        <div className="flex flex-col items-center justify-center">
          <div className="flex items-center justify-center mb-4 text-[#165dff]">
            <CloudUploadOutlined className="text-[36px]" />
          </div>
          <p className="text-gray-600">点击上传文件或拖拽文件到这里</p>
          <p className="text-gray-400 text-sm mt-2">支持任意格式视频</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`
          relative border border-dashed rounded-lg p-12 text-center cursor-pointer transition-all duration-200
          ${
            isDragOver
              ? "border-blue-400 bg-[#E9F0FF]"
              : "border-gray-300 bg-gray-50 hover:bg-[#E9F0FF]"
          }
        `}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={handleClick}
    >
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={accept}
        onChange={handleFileSelect}
        className="hidden"
      />

      <div className="flex flex-col items-center justify-center">
        <div className="flex items-center justify-center mb-4 text-[#165dff]">
          <CloudUploadOutlined className="text-[36px]" />
        </div>
        <p className="text-gray-600">点击上传文件或拖拽文件到这里</p>
        <p className="text-gray-400 text-sm mt-2">支持任意格式视频</p>
      </div>
    </div>
  );
}
