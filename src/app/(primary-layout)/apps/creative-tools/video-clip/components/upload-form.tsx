"use client";

import { useState, useRef, useEffect, useMemo } from "react";
import {
  Radio,
  Button,
  message,
  Form,
  Input,
  Select,
  Progress,
  Modal,
} from "antd";
import WithConfigProvider from "@/components/with-config-provider";
import type { RadioChangeEvent } from "antd";
import IconSvg from "@/components/Icon/SinoSymbolFont";
import AliOSSUploadLite from "@/components/AliOSSUploadV2/OSSUpload/Lite";
import { DICT_KEY } from "@/constants";
import useDict from "@/hooks/useDict";
import type { FileType } from "./file-dragger";
import Dragger from "./file-dragger";
import FileList from "./file-list";
import { taskCreate, bindFileTask, sortNoByAI } from "../services";

const FormItem = Form.Item;

type UploadFormProps = {
  onSaveCallback: () => void;
};

type SortItem = { name: string; no: number };

function UploadForm({ onSaveCallback }: UploadFormProps) {
  const [uploadType, setUploadType] = useState("local");
  const [sorting, setSorting] = useState<boolean>(false);
  const [uploading, setUploading] = useState<boolean>(false);
  const [files, setFiles] = useState<FileType[]>([]);
  const [fileList, setFileList] = useState<any[]>([]);
  const [modal, contextHolder] = Modal.useModal();

  const [form] = Form.useForm();

  const formRef = useRef<any>({});
  const instanceRef = useRef<any>(new AliOSSUploadLite({ openAnalyze: false }));

  const dictData: any = useDict([DICT_KEY.SHORT_DRAMA_HIGHLIGHTS_LANGUAGE]);

  const options = useMemo(() => {
    const result = dictData?.[DICT_KEY.SHORT_DRAMA_HIGHLIGHTS_LANGUAGE];
    if (Array.isArray(result) && result.length > 0) {
      return result.map((item: any) => {
        return {
          value: item.value,
          label: item.name,
        };
      });
    }
    return [];
  }, [dictData]);

  const handleUploadTypeChange = (e: RadioChangeEvent) => {
    setUploadType(e.target.value);
  };

  /**
   * 排序
   * @param arrA
   * @param arrB
   * @returns
   */
  function handleSort(arrA: FileType[], arrB: SortItem[]) {
    const nameToNoMap = new Map(
      arrB.map((item: SortItem) => [item.name, item.no])
    );
    return arrA.slice().sort((a: FileType, b: FileType) => {
      return (
        (nameToNoMap.get(a.name) ?? Infinity) -
        (nameToNoMap.get(b.name) ?? Infinity)
      );
    });
  }

  /**
   * 选择文件
   */
  const handleFileChange = async (fs: FileType[], cb?: () => void) => {
    // 重置队列
    instanceRef.current.reset();
    const newFiles = [...files, ...fs];
    setSorting(true);
    try {
      // 排序
      const { data = {} } = await sortNoByAI(
        newFiles.map((f: FileType) => ({ name: f.name }))
      );
      const result = data?.result;
      if (Array.isArray(result) && result.length > 0) {
        setFiles(handleSort(newFiles, result));
        cb?.();
        setSorting(false);
        return;
      }
      setFiles(newFiles);
      setSorting(false);
      cb?.();
    } catch (e) {
      setFiles(newFiles);
      setSorting(false);
      cb?.();
    }
  };

  /**
   * 开始上传
   */
  const handleOk = async () => {
    if (uploading) {
      return;
    }
    try {
      const values = await form.validateFields();
      await modal.confirm({
        title: "提示",
        centered: true,
        content:
          "上传前，请将视频文件根据序号调整为正确的集数顺序，否则会导致剧情理解错误",
        onOk: async () => {
          try {
            formRef.current = values;
            setUploading(true);
            instanceRef.current.addTask(files);
          } catch (e) {
            console.log(e);
          }
        },
        okText: "确认上传",
      });
    } catch (e) {
      console.log(e);
    }
  };

  /**
   * 取消上传
   */
  const handleCancel = () => {
    // 取消上传
    instanceRef.current.reset();
    setUploading(false);
    setFileList([]);
  };

  /**
   * 重试
   */
  const handleRetry = () => {
    setUploading(true);
    setFileList([]);
    instanceRef.current.reset();
    instanceRef.current.addTask(files);
  };

  // 进度
  const percent = useMemo(() => {
    const completedList = fileList.filter((item: any) =>
      ["done", "error"].includes(item.status)
    );
    return parseInt(`${(completedList.length / fileList.length) * 100}`);
  }, [fileList]);

  /**
   * 创建任务
   * @param fileList
   */
  const handleTaskCreate = async (fileList: any) => {
    // 文件上传完成，自动创建任务
    if (fileList.every((f: any) => f.status === "done")) {
      try {
        // 创建任务
        const { data: taskCreateData = {} } = await taskCreate({
          name: formRef.current?.name,
          language: formRef.current?.language,
        });
        // 上传文件绑定任务
        const taskId = taskCreateData?.result;
        if (taskId) {
          const { data: bindFileTaskData = {} } = await bindFileTask({
            bindInfoList: fileList.map((file: any) => ({ path: file.path })),
            taskId,
          });
          if (bindFileTaskData?.code === 0) {
            message.destroy();
            message.success("任务创建成功");
            form.setFieldsValue({ name: undefined, language: undefined });
            setFiles([]);
            setFileList([]);
            setUploading(false);
            onSaveCallback?.();
            return;
          }
          onSaveCallback?.();
          setUploading(false);
          return;
        }
        setUploading(false);
      } catch (e: any) {
        setUploading(false);
      }
      return;
    }
    setUploading(false);
  };

  useEffect(() => {
    async function init() {
      instanceRef.current.on("change", (res: any) => {
        setFileList(res);
        handleTaskCreate(res);
      });

      instanceRef.current.on("progress", (res: any) => {
        setFileList(res);
      });

      instanceRef.current.on("error", (msg: string) => {
        message.destroy();
        message.error(msg);
      });
    }
    init();
  }, []);

  useEffect(() => {
    function handleBeforeunload(e: any) {
      e.preventDefault();
      e.returnValue = "在文件上传成功之前，请勿关闭此页面，否则将导致上传失败";
      return "在文件上传成功之前，请勿关闭此页面，否则将导致上传失败";
    }

    function handleUnload(e: any) {
      e.preventDefault();
      e.returnValue = "在文件上传成功之前，请勿关闭此页面，否则将导致上传失败";
      return "在文件上传成功之前，请勿关闭此页面，否则将导致上传失败";
    }

    if (
      Array.isArray(fileList) &&
      fileList.some((file) => !["done", "error"].includes(file.status))
    ) {
      window.addEventListener("beforeunload", handleBeforeunload);
      window.addEventListener("unload", handleUnload);
    }
    return () => {
      window.removeEventListener("beforeunload", handleBeforeunload);
      window.removeEventListener("unload", handleUnload);
    };
  }, [fileList]);

  const renderButton = () => {
    if (uploading) {
      return (
        <div>
          <div className="py-2 px-4 leading-6 font-pingfang text-xs bg-[rgba(22,93,255,0.1)] flex">
            <div className="w-4 h-6 flex items-center mr-1">
              <IconSvg type="icon-信息提示" />
            </div>
            上传过程中，不允许再新增/清空文件，上传成功后，将自动创建任务
          </div>
          <div className="w-full flex items-center gap-5 p-4 border-t border-t-solid border-t-[#E5E6EB]">
            <Progress status="active" percent={percent || 0} type="line" />
            <Button
              className="flex-1 bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
              onClick={() => handleCancel()}
            >
              取消上传
            </Button>
          </div>
        </div>
      );
    }
    if (
      fileList.length > 0 &&
      fileList.some((file: any) => file.status !== "done")
    ) {
      return (
        <div>
          <Button
            type="primary"
            size="large"
            loading={uploading}
            className="w-full bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
            onClick={() => handleRetry()}
          >
            上传中断，点击重试
          </Button>
        </div>
      );
    }

    return (
      <div className="px-4 py-3">
        <Button
          type="primary"
          size="large"
          loading={uploading}
          disabled={!files.length}
          className="w-full bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
          onClick={() => handleOk()}
        >
          开始上传并解析故事线
        </Button>
      </div>
    );
  };

  return (
    <div className="xl:flex-1 flex flex-col rounded-lg pt-5 border border-solid border-gray-200 xl:overflow-hidden">
      <div className="flex-1 xl:overflow-auto">
        <div className="px-5 mb-5">
          <h2 className="text-lg font-medium mb-6">今天你想上传什么内容？</h2>
          <Radio.Group
            value={uploadType}
            onChange={handleUploadTypeChange}
            className="mb-6"
          >
            <Radio value="local" className="text-blue-500">
              本地文件上传
            </Radio>
            {/* <Radio value="cloud" className="ml-8 text-gray-600">
              百度云链接解析
            </Radio> */}
          </Radio.Group>
        </div>
        <div className="px-5 mb-5 pb-6 border-b border-b-solid border-b-gray-200">
          <Dragger
            accept="video/*"
            disabled={uploading}
            onChange={handleFileChange}
          />
        </div>
        <div className="px-5 mb-5 border-b border-b-solid border-b-gray-200">
          <Form form={form}>
            <FormItem
              label="任务名称"
              name="name"
              colon={false}
              rules={[
                { required: true, message: "请输入任务名称", whitespace: true },
              ]}
            >
              <Input
                placeholder="请输入"
                maxLength={50}
                showCount
                disabled={uploading}
              />
            </FormItem>
            <FormItem
              label="原片音频语种"
              name="language"
              colon={false}
              rules={[{ required: true, message: "请选择原片音频语种" }]}
            >
              <Select
                showSearch
                placeholder="请选择"
                disabled={uploading}
                options={options}
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase()) ||
                  (option?.value ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </FormItem>
          </Form>
        </div>
        <FileList
          loading={sorting}
          isDropDisabled={uploading}
          files={files}
          onChange={(v: FileType[]) => setFiles(v)}
          onClear={() => setFiles([])}
        />
      </div>
      <div className="flex-none w-full bg-white">{renderButton()}</div>
      {contextHolder}
    </div>
  );
}

export default WithConfigProvider(UploadForm, {
  theme: {
    components: {
      Button: {
        colorBgContainerDisabled: "rgba(22, 93, 255, 0.6)",
        colorTextDisabled: "rgb(255,255,255)",
        borderColorDisabled: "transparent",
      },
    },
  },
});
