"use client";
import React, {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Table, Tag, Popconfirm, message, Popover } from "antd";
import dayjs from "dayjs";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";
import { INITIAL_PAGINATION } from "@/constants";
import eventBus from "@/utils/eventBus";
import { EVENT_NEMES } from "@/components/Mqtt/consts";
import { getTaskList, taskRetry, getShortDramaVersionList } from "../services";

interface TaskData {
  id: number;
  taskName: string;
  status: 1 | 2 | 3 | 4 | 5 | 6; // 1-上传中，2-已取消，3-排队中，4-处理中，5-处理成功，6-处理失败
  statusDesc: string;
  startTime?: number;
  endTime?: number;
}

const TAG_COLOR: any = {
  1: "",
  2: "magenta",
  3: "orange",
  4: "cyan",
  5: "green",
  6: "red",
};

function TaskList(props: any, ref: React.Ref<any>) {
  const [pagination, setPagination] =
    useState<TablePaginationConfig>(INITIAL_PAGINATION);
  const [loading, setLoading] = useState<boolean>(true);
  const [dataSource, setDataSource] = useState<TaskData[]>([]);
  const [versionMap, setVersionMap] = useState<Record<number, any[]>>({});
  const requestIdRef = useRef<number>(0);

  const { current, pageSize } = pagination;

  // 获取版本信息
  const fetchVersionList = async (taskId: number) => {
    try {
      const { data } = await getShortDramaVersionList({ id: taskId });
      if (data?.code === 0 && data.result?.length > 0) {
        setVersionMap((prev) => ({
          ...prev,
          [taskId]: data.result,
        }));
      }
    } catch (error) {
      console.error("获取版本列表失败:", error);
    }
  };

  const getList = async (params: any) => {
    setLoading(true);
    try {
      const currentRequestId = ++requestIdRef.current;
      const { data = {} } = await getTaskList(params);
      if (data?.code === 0) {
        if (currentRequestId == requestIdRef.current) {
          const result = data?.result || {};
          const items = result?.items || [];
          const total = result?.total || 0;
          setDataSource(items);
          setPagination({
            ...pagination,
            current: params?.pageNo || INITIAL_PAGINATION.current,
            pageSize: params?.pageSize || INITIAL_PAGINATION.pageSize,
            total,
          });
          items.forEach((item: TaskData) => {
            // 3-排队中，4-处理中，6-处理失败
            if (item.status === 3 || item.status === 4 || item.status === 6) {
              // 用于判断按钮展示逻辑，如果这些状态下存在多个版本，则展示【查看故事解析】按钮
              // 任务成功肯定展示【查看故事解析】按钮，所以不需要判断
              fetchVersionList(item.id);
            }
          });
        }
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    async function fetchData(params: any) {
      await getList(params);
    }
    fetchData({ pageNo: current, pageSize });
  }, [current, pageSize]);

  useEffect(() => {
    return () => {
      requestIdRef.current = 0;
    };
  }, []);

  useEffect(() => {
    // 监听mqtt
    const fn = (data: any) => {
      const { taskId, taskName, taskStatus, statusDesc, endTime } = data;
      setDataSource((prev: any) => {
        const idx = prev.findIndex((item: any) => `${item.id}` === `${taskId}`);
        if (idx > -1) {
          const newDataSource = [...prev];
          const current = newDataSource[idx];
          newDataSource.splice(idx, 1, {
            ...current,
            endTime: endTime > 0 ? endTime : current.endTime,
            status: taskStatus,
            statusDesc,
            taskName,
          });
          return newDataSource;
        }
        return prev;
      });
    };
    eventBus.on(EVENT_NEMES.SHORT_DRAMA_HIGHLIGHTS, fn);
    return () => {
      eventBus.off(EVENT_NEMES.SHORT_DRAMA_HIGHLIGHTS, fn);
    };
  }, []);

  /**
   * 重试
   */
  const handleRetry = async (id: number) => {
    const { data = {} } = await taskRetry({ id });
    if (data?.code === 0) {
      message.success("操作成功");
      await getList({ pageNo: current, pageSize });
    }
  };

  const renderUploadTag = (record: TaskData) => {
    const versions = versionMap[record.id] || [];
    return (
      <Tag color={TAG_COLOR[record.status]}>
        {record.status === 6 && versions.length >= 1
          ? "新版本生成失败"
          : record.statusDesc}
      </Tag>
    );
  };

  const columns: ColumnsType<TaskData> = [
    {
      title: "任务序号",
      dataIndex: "id",
      key: "id",
      width: 100,
    },
    {
      title: "任务名称",
      dataIndex: "taskName",
      key: "taskName",
      ellipsis: true,
    },
    {
      title: "任务进度",
      dataIndex: "status",
      key: "status",
      width: 140,
      render: (_: any, record: TaskData) => renderUploadTag(record),
    },
    {
      title: "任务开始时间",
      dataIndex: "startTime",
      key: "startTime",
      width: 180,
      render: (v) => (v > 0 ? dayjs(v).format("YYYY-MM-DD HH:mm:ss") : "-"),
    },
    {
      title: "视频生成完成时间",
      dataIndex: "endTime",
      key: "endTime",
      width: 180,
      render: (v) => (v > 0 ? dayjs(v).format("YYYY-MM-DD HH:mm:ss") : "-"),
    },
    {
      title: "操作",
      key: "action",
      width: 120,
      render: (_, record: TaskData) => {
        const versions = versionMap[record.id] || [];
        if (record.status === 5 || versions.length >= 1) {
          return (
            <div
              className="link-button"
              onClick={() => {
                window.open(`/dam/apps/creative-tools/video-clip/${record.id}`);
              }}
            >
              查看故事解析
            </div>
          );
        }

        // 处理失败，显示重试
        if (record.status === 6) {
          return (
            <Popconfirm
              title="确定要重试吗？"
              placement="top"
              onConfirm={() => handleRetry(record.id)}
            >
              <div className="link-button">重试</div>
            </Popconfirm>
          );
        }
        return null;
      },
    },
  ];

  const handlePaginationChange = ({
    current,
    pageSize,
  }: TablePaginationConfig) => {
    setPagination({
      ...pagination,
      current: pageSize !== pagination.pageSize ? 1 : current,
      pageSize,
    });
  };

  useImperativeHandle(ref, () => ({
    refresh: async () => await getList({ pageNo: 1, pageSize }),
  }));

  return (
    <Table
      sticky={{ offsetHeader: 0 }}
      columns={columns}
      dataSource={dataSource}
      pagination={pagination}
      loading={loading}
      size="middle"
      rowKey="id"
      onChange={handlePaginationChange}
    />
  );
}

export default forwardRef(TaskList);
