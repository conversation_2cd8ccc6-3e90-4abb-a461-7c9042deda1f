"use client";
import {
  DragDropContext,
  Droppable,
  Draggable,
  type DropResult,
} from "react-beautiful-dnd";
import { GripVertical, Trash2, Video, Loader2 } from "lucide-react";
import { Empty, Spin } from "antd";
import { cn } from "@/lib/utils";
import type { FileType } from "./file-dragger";

type FileListProps = {
  loading?: boolean;
  isDropDisabled?: boolean;
  files: FileType[];
  onChange: (files: FileType[]) => void;
  onClear: () => void;
};

export default function FileList({
  loading,
  isDropDisabled = false,
  files,
  onChange,
  onClear,
}: FileListProps) {
  /**
   * 拖拽结束
   * @param result
   * @returns
   */
  const onDragEnd = (result: DropResult) => {
    if (isDropDisabled) return;
    if (!result.destination) {
      return;
    }
    const items = Array.from(files);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    onChange?.(items);
  };

  /**
   * 删除
   * @param uid
   */
  const handleDeleteFile = (uid: string) => {
    if (isDropDisabled) return;
    const newFileList = [...files];
    const idx = newFileList.findIndex((file) => file.uid === uid);
    if (idx > -1) {
      newFileList.splice(idx, 1);
      onChange?.(newFileList);
    }
  };

  function middleEllipsis(text: string = "", headLen = 12, tailLen = 12) {
    if (text.length <= headLen + tailLen + 3) return text;
    return text.slice(0, headLen) + "..." + text.slice(-tailLen);
  }

  if (loading) {
    return (
      <div className="px-5 pb-6">
        <h3 className="text-[16px] font-semibold mb-8">文件列表</h3>
        <div className="min-h-[150px] flex items-center justify-center">
          <div className={`flex flex-col items-center justify-center gap-3`}>
            <Loader2
              className={`w-6 h-6 animate-spin text-blue-600`}
              strokeWidth={3}
            />
            <p className={`text-sm text-gray-600 font-medium`}>智能排序中...</p>
          </div>
        </div>
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="px-5 pb-6">
        <h3 className="text-[16px] font-semibold mb-8">文件列表</h3>
        <div className="min-h-[150px] flex items-center justify-center">
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      </div>
    );
  }

  return (
    <div className="px-5 pb-10">
      <div className="flex items-center justify-between mb-8 gap-5">
        <h3 className="text-[16px] font-semibold">{`文件列表（已选${files.length}个）`}</h3>
        <div
          className={cn("link-button", { "opacity-60": isDropDisabled })}
          onClick={() => {
            if (!isDropDisabled) {
              onClear();
            }
          }}
        >
          一键清空
        </div>
      </div>
      <div className="flex">
        <ul className="w-[70px] flex-none space-y-3 sticky left-0 top-0 z-20 bg-white">
          {files.map((item: any, index: number) => {
            return (
              <li
                key={item.uid}
                className="w-16 text-sm font-medium text-gray-700 h-[40px] leading-[40px]"
              >
                第{index + 1}集
              </li>
            );
          })}
        </ul>
        <div className="flex-1 relative overflow-hidden">
          {isDropDisabled && (
            <div className="absolute w-full h-full left-0 top-0 z-10 bg-white opacity-60 cursor-not-allowed" />
          )}
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable
              droppableId="fileList"
              ignoreContainerClipping={false}
              isDropDisabled={isDropDisabled}
              isCombineEnabled={false}
            >
              {(provided) => (
                <ul
                  className="w-full space-y-3"
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                >
                  {files.map((file, index) => (
                    <Draggable
                      key={file.uid}
                      draggableId={file.uid}
                      index={index}
                    >
                      {(provided, snapshot) => (
                        <li
                          className="w-full flex items-center bg-[#F2F3F3] p-3 rounded-md h-[40px] overflow-hidden"
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          data-is-dragging={snapshot.isDragging}
                          data-testid={file.uid}
                        >
                          <Video className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                          <div
                            title={file.name}
                            className="flex-1 text-sm text-slate-700 single-line-ellipsis"
                          >
                            {middleEllipsis(file.name || "")}
                          </div>
                          <div className="flex-none flex items-center justify-end gap-2 ml-2">
                            <div
                              className="cursor-pointer text-slate-500 hover:text-red-500 flex-shrink-0"
                              onClick={() => handleDeleteFile(file.uid)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </div>
                            <div
                              className="cursor-grab text-slate-400 hover:text-slate-600 flex-shrink-0"
                              aria-label="拖拽排序"
                            >
                              <GripVertical className="h-5 w-5" />
                            </div>
                          </div>
                        </li>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </ul>
              )}
            </Droppable>
          </DragDropContext>
        </div>
      </div>
    </div>
  );
}
