"use client";

import React from "react";
import { useEditorStore } from "../../stores/editor";
import { useTimelineStore } from "../../stores/timeline";
import { SubtitleClip } from "../../types";

const TIMELINE_CONSTANTS = {
  PIXELS_PER_SECOND: 50,
  VIDEO_TRACK_WIDTH: 40, // 视频轨道宽度
};

/**
 * 字幕轨道组件
 * 显示字幕条和翻译文本，支持单语/双语模式切换
 */
export const SubtitleTrack = React.memo(function SubtitleTrack() {
  const { subtitleClips, showSubtitles } = useEditorStore();
  const { zoomLevel } = useTimelineStore();

  if (!subtitleClips || subtitleClips.length === 0) {
    return null;
  }

  return (
    <div
      className="absolute top-0 h-full"
      style={{
        left: `${TIMELINE_CONSTANTS.VIDEO_TRACK_WIDTH}px`,
        width: `calc(100% - ${TIMELINE_CONSTANTS.VIDEO_TRACK_WIDTH}px)`,
      }}
    >
      <div className="relative w-full h-full pt-8">
        {subtitleClips.map((subtitle: SubtitleClip) => {
          const startPosition =
            subtitle.startTime *
            TIMELINE_CONSTANTS.PIXELS_PER_SECOND *
            zoomLevel;
          const subtitleHeight =
            (subtitle.endTime - subtitle.startTime) *
            TIMELINE_CONSTANTS.PIXELS_PER_SECOND *
            zoomLevel;

          return (
            <div
              key={subtitle.id}
              className="absolute left-2 right-2"
              style={{
                top: `${startPosition}px`,
                height: `${Math.max(subtitleHeight, 24)}px`, // 最小高度24px，确保文字可读
              }}
            >
              {showSubtitles && subtitle.translationText ? (
                // 双语显示模式 - 原文和翻译各占50%
                <div className="flex w-full h-full gap-2">
                  <div className="w-1/2 bg-gray-100/50 rounded-md px-2 py-1 cursor-pointer flex items-start border border-gray-200">
                    <span className="text-xs leading-tight break-words block text-gray-800 font-medium">
                      {subtitle.text}
                    </span>
                  </div>
                  <div className="w-1/2 bg-gray-100/50 rounded-md px-2 py-1 cursor-pointer flex items-start border border-gray-200">
                    <span className="text-xs text-gray-600 leading-tight break-words block">
                      {subtitle.translationText}
                    </span>
                  </div>
                </div>
              ) : (
                // 单语显示模式 - 原文占100%宽度
                <div className="w-full h-full bg-gray-100/50 rounded-md px-2 py-1 cursor-pointer flex items-start border border-gray-200">
                  <span className="text-xs leading-tight break-words block text-gray-800 font-medium">
                    {subtitle.text}
                  </span>
                </div>
              )}

              {/* 字幕时间标识 - 在hover时显示 */}
              <div className="absolute -left-1 top-0 opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                <div className="bg-gray-800 text-white text-[10px] px-1 py-0.5 rounded shadow-lg whitespace-nowrap">
                  {Math.floor(subtitle.startTime)}s -{" "}
                  {Math.floor(subtitle.endTime)}s
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
});
