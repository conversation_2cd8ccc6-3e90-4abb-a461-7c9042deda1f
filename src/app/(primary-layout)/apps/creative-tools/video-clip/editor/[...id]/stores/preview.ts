import { create } from "zustand";

interface PreviewStore {
  isPlaying: boolean;
  setIsPlaying: (isPlaying: boolean) => void;
  playMode: string;
  setPlayMode: (playMode: string) => void;
  playbackSpeed: number;
  setPlaybackSpeed: (speed: number) => void;
}

export const usePreviewStore = create<PreviewStore>((set) => ({
  isPlaying: false,
  setIsPlaying: (isPlaying) => set({ isPlaying }),
  playMode: "default",
  setPlayMode: (playMode) => set({ playMode }),
  playbackSpeed: 1,
  setPlaybackSpeed: (speed) => set({ playbackSpeed: speed }),
}));
