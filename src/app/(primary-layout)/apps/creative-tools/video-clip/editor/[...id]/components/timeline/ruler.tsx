"use client";

import React, { useMemo } from "react";
import {
  formatTimeWithInterval,
  getTimeInterval,
  isMainMarker,
} from "../../utils";

interface RulerProps {
  duration: number;
  zoomLevel: number;
  pixelsPerSecond: number;
  onRulerClick: (e: React.MouseEvent) => void;
}

/**
 * 时间刻度尺组件
 * 显示时间标记和刻度线，支持智能缩放和格式化
 */
export const Ruler = React.memo(function Ruler({
  duration,
  zoomLevel,
  pixelsPerSecond,
  onRulerClick,
}: RulerProps) {
  // 计算智能刻度间隔
  const interval = useMemo(() => getTimeInterval(zoomLevel, pixelsPerSecond), [zoomLevel, pixelsPerSecond]);

  // 计算刻度标记数量
  const markerCount = useMemo(() => Math.ceil(duration / interval) + 1, [duration, interval]);

  // 计算时间刻度尺的实际高度
  const rulerHeight = useMemo(() => Math.max(duration * pixelsPerSecond * zoomLevel, 1000), [duration, pixelsPerSecond, zoomLevel]);

  /**
   * 生成时间刻度标记
   */
  const generateTimeMarkers = useMemo(() => {
    const markers = [];

    for (let i = 0; i < markerCount; i++) {
      const time = i * interval;
      if (time > duration) break;

      const isMain = isMainMarker(time, interval);
      const position = time * pixelsPerSecond * zoomLevel;

      markers.push(
        <div
          key={`marker-${i}-${time}`}
          className="absolute left-0 right-0 flex flex-row items-center"
          style={{ top: `${position}px` }}
        >
          {/* 刻度线 */}
          <div
            className={`${
              isMain
                ? "border-t border-muted-foreground/40 w-full"
                : "border-t border-muted-foreground/20 w-2/3"
            }`}
          />
          {/* 时间标签 */}
          {(isMain || interval < 1) && time >= 0 && (
            <span
              className={`absolute text-[0.6rem] whitespace-nowrap select-none ${
                isMain
                  ? "text-muted-foreground font-medium left-1 -top-3"
                  : "text-muted-foreground/70 left-1 -top-3"
              }`}
              style={{
                fontSize: isMain ? "10px" : "9px",
              }}
            >
              {formatTimeWithInterval(time, interval)}
            </span>
          )}
        </div>
      );
    }

    return markers;
  }, [markerCount, interval, duration, pixelsPerSecond, zoomLevel]);

  return (
    <div
      className="relative w-8 cursor-pointer select-none"
      style={{ height: `${rulerHeight}px` }}
      onClick={onRulerClick}
    >
      {/* 背景网格线（在高缩放级别时显示） */}
      {zoomLevel > 2 && (
        <div className="absolute inset-0 opacity-10">
          {Array.from({ length: Math.ceil(duration) }).map((_, i) => (
            <div
              key={`grid-${i}`}
              className="absolute left-0 right-0 border-t border-muted-foreground/10"
              style={{
                top: `${i * pixelsPerSecond * zoomLevel}px`,
              }}
            />
          ))}
        </div>
      )}

      {/* 时间刻度标记 */}
      <div className="relative w-full h-full border-l border-muted-foreground/40 ml-2">
        {generateTimeMarkers}
      </div>
    </div>
  );
});
