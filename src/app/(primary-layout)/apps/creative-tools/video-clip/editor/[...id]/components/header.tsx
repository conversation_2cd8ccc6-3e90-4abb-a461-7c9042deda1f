import { Button, Switch, message, Modal } from "antd";
import {
  ArrowLeftOutlined,
  ExportOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { useEditorStore } from "../stores/editor";
import { useRouter } from "next/navigation";
import { updateShortDramaInAndOutPoint, getGenerateCreate } from "../services";
import { useState } from "react";
import { useFingerprint } from '@/hooks';
import DownloadCenter from "../../../[id]/components/download-center";
import eventBus from "@/utils/eventBus";
import { VIDEO_CLIP_EVENT_NAME } from "../../../utils";

interface HeaderProps {
  id: string;
  taskId: string;
  allClips?: any[]; // 所有切片数据
  version: number;
  taskName: string;
}

export const Header = ({
  id,
  taskId,
  allClips = [],
  version,
  taskName,
}: HeaderProps) => {
  const {
    showSubtitles,
    setShowSubtitles,
    selectedClips,
    videoClips,
    hasUnsavedChanges,
    setHasUnsavedChanges,
    setOriginalSelectedClips,
    setOriginalVideoClips,
    storyName,
  } = useEditorStore();
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  // 获取设备id，用于用户行为追踪和统计分析
  const { visitorId } = useFingerprint();

  const handleBack = () => {
    if (hasUnsavedChanges) {
      Modal.confirm({
        title: "确认离开",
        content: "您有未保存的更改，确定要离开吗？",
        okText: "确定离开",
        cancelText: "取消",
        onOk: () => {
          router.push(`/apps/creative-tools/video-clip/${taskId}`);
        },
      });
    } else {
      router.push(`/apps/creative-tools/video-clip/${taskId}`);
    }
  };

  const handleExport = async () => {
    if (selectedClips.length === 0) {
      message.warning("请至少选择一个切片进行导出");
      return;
    }

    if (hasUnsavedChanges) {
      Modal.confirm({
        title: "检测到未保存的更改",
        content: "导出前建议先保存当前更改，是否继续导出？",
        okText: "继续导出",
        cancelText: "取消",
        onOk: () => {
          performExport();
        },
      });
    } else {
      performExport();
    }
  };

  const performExport = async () => {
    try {
      setIsExporting(true);

      // 构建导出数据
      const exportData = {
        id: parseInt(id) || 0,
        name: `${taskName}${version}`,
        status: 0,
        device: visitorId,
      };

      // 调用导出接口
      const response = await getGenerateCreate(exportData);

      if (response.data.success) {
        if (response?.data?.code === 0) {
          eventBus.emit(VIDEO_CLIP_EVENT_NAME.GENERATE_CREATE_SUCCESS, {});
        }
      } else {
        message.error(response.data.message || "导出失败，请重试");
      }
    } catch (error) {
      console.error("导出失败:", error);
      message.error("导出失败，请重试");
    } finally {
      setIsExporting(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);

      // 构建保存数据 - 包含所有切片
      const saveData = allClips.map((clip: any) => {
        // 检查是否为选中的切片
        const isSelected = selectedClips.some(
          (selected: any) => selected.id === clip.id
        );

        if (isSelected) {
          // 勾选的切片：从videoClips中找到对应的时间轴数据，获取修改后的入点出点
          const timelineClip = videoClips.find(
            (vc: any) => String(vc.id) === String(clip.id)
          );

          return {
            enable: true, // 勾选的切片启用
            endTime: timelineClip
              ? timelineClip.outPoint
              : clip.endTimeSeconds || 0,
            id: clip.id,
            startTime: timelineClip
              ? timelineClip.inPoint
              : clip.startTimeSeconds || 0,
            storyLineId: parseInt(id) || 0,
          };
        } else {
          // 未勾选的切片：设置enable为false
          return {
            enable: false,
            id: clip.id,
            storyLineId: parseInt(id) || 0,
          };
        }
      });

      // 调用保存接口 - 直接传递数组
      await updateShortDramaInAndOutPoint(saveData);

      // 保存成功后重置未保存状态
      setHasUnsavedChanges(false);
      // 更新原始数据 - 只更新选中的切片，不更新时间轴数据
      setOriginalSelectedClips([...selectedClips]);

      // 更新原始视频片段数据中的时间信息
      const updatedOriginalVideoClips = allClips.map((clip: any) => {
        const isSelected = selectedClips.some(
          (selected: any) => selected.id === clip.id
        );

        if (isSelected) {
          // 对于选中的切片，更新时间信息
          const timelineClip = videoClips.find(
            (vc: any) => String(vc.id) === String(clip.id)
          );

          return {
            ...clip,
            startTimeSeconds: timelineClip
              ? timelineClip.inPoint
              : clip.startTimeSeconds,
            endTimeSeconds: timelineClip
              ? timelineClip.outPoint
              : clip.endTimeSeconds,
          };
        } else {
          // 对于未选中的切片，保持原样
          return clip;
        }
      });

      setOriginalVideoClips(updatedOriginalVideoClips);

      message.success("保存成功");
    } catch (error) {
      console.error("保存失败:", error);
      message.error("保存失败，请重试");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="h-12 px-4 flex justify-between items-center border-b border-gray-200">
      <div className="flex items-center gap-2">
        <div
          className="flex items-center gap-1 cursor-pointer text-blue-500 hover:text-blue-600"
          onClick={handleBack}
        >
          <ArrowLeftOutlined />
          <span>返回</span>
        </div>
        <span className="text-gray-500">|</span>
        <p className="text-md">短剧切片编辑</p>
        {hasUnsavedChanges && (
          <span className="text-orange-500 text-sm">• 未保存</span>
        )}
      </div>

      <div className="flex items-center gap-4">
        <DownloadCenter taskId={taskId} />
        <div className="flex items-center gap-2">
          <Switch
            size="small"
            checked={showSubtitles}
            onChange={setShowSubtitles}
          />
          <p>翻译字幕</p>
        </div>
        <Button
          type="primary"
          size="middle"
          icon={<ExportOutlined />}
          loading={isExporting}
          onClick={handleExport}
        >
          导出素材成片
        </Button>
        <Button
          type="primary"
          size="middle"
          icon={<SaveOutlined />}
          loading={isSaving}
          onClick={handleSave}
        >
          保存切片
        </Button>
      </div>
    </div>
  );
};
