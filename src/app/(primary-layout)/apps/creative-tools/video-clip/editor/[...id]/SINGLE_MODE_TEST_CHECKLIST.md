# Single Mode (播放当前切片) 功能测试清单

## 功能概述
Single模式应该只播放当前选中切片的入点和出点之间的视频内容，时间轴指示线应该从入点开始移动到出点停止。

## 测试场景

### 1. 基本播放功能
- [ ] 切换到single模式时，播放头自动跳转到当前激活切片的入点
- [ ] 播放时只播放入点到出点之间的内容
- [ ] 播放到出点时自动停止
- [ ] 时间轴指示线正确显示播放位置（从入点开始到出点结束）

### 2. 切片切换
- [ ] 在single模式下切换到不同切片时，播放头自动跳转到新切片的入点
- [ ] 切换切片后播放仍然只在新切片的入点出点范围内
- [ ] 时间轴指示线位置正确对应新切片的播放范围

### 3. 播放模式切换
- [ ] 从default模式切换到single模式时，播放头跳转到当前激活切片的入点
- [ ] 从single模式切换到default模式时，播放恢复正常的全时间轴播放
- [ ] 从single模式切换到all模式时，播放恢复正常的全时间轴播放

### 4. 用户交互
- [ ] 在single模式下点击时间轴，只能在当前激活切片的时间范围内跳转
- [ ] 播放/暂停按钮在single模式下正常工作
- [ ] 拖拽播放头在single模式下被限制在切片范围内

### 5. 入点出点修改
- [ ] 在single模式下修改入点时，播放范围实时更新
- [ ] 在single模式下修改出点时，播放范围实时更新
- [ ] 修改入点出点后，播放头位置仍然正确

### 6. 边界情况
- [ ] 没有激活切片时single模式的行为（应该回退到第一个切片）
- [ ] 只有一个切片时single模式正常工作
- [ ] 切片的入点和出点相同时的处理
- [ ] 切片时长很短（<1秒）时的播放

### 7. 性能和稳定性
- [ ] 频繁切换播放模式不会导致卡顿
- [ ] 快速切换切片不会导致播放异常
- [ ] 长时间播放single模式不会出现内存泄漏

## 测试步骤

### 准备工作
1. 打开视频剪辑编辑器
2. 导入至少2个视频切片
3. 设置不同的入点和出点
4. 确保有一个切片被激活

### 基本功能测试
1. 选择一个切片使其激活
2. 切换到"播放当前切片"模式
3. 验证播放头是否跳转到入点
4. 点击播放按钮
5. 观察播放是否只在入点出点范围内
6. 验证播放到出点时是否自动停止

### 交互测试
1. 在single模式下点击时间轴不同位置
2. 验证是否只能在当前切片范围内跳转
3. 拖拽播放头测试范围限制
4. 测试播放/暂停按钮

### 切换测试
1. 在single模式下切换到不同切片
2. 验证播放头是否跳转到新切片入点
3. 切换不同播放模式
4. 验证行为是否符合预期

## 已知问题和注意事项

### 实现细节
- single模式下的时间映射：全局时间轴时间 = 切片开始时间 + (视频时间 - 入点时间)
- 播放头位置计算基于全局时间轴
- 视频时间同步考虑入点偏移

### 可能的问题
- 时间同步精度问题（容差设置为0.1-0.2秒）
- 快速切换时的状态同步
- 边界条件的处理

## 测试结果记录

测试日期：_______
测试人员：_______

| 测试项目 | 状态 | 备注 |
|---------|------|------|
| 基本播放功能 | ⬜ |  |
| 切片切换 | ⬜ |  |
| 播放模式切换 | ⬜ |  |
| 用户交互 | ⬜ |  |
| 入点出点修改 | ⬜ |  |
| 边界情况 | ⬜ |  |
| 性能和稳定性 | ⬜ |  |

## 问题报告

如发现问题，请记录：
1. 问题描述
2. 重现步骤
3. 预期行为
4. 实际行为
5. 浏览器和版本信息
