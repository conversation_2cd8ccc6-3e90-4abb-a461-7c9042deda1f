import { create } from "zustand";
import { SubtitleClip } from "../types";

interface EditorStore {
  storyName: string;
  setStoryName: (storyName: string) => void;
  activeClip: any;
  setActiveClip: (activeClip: any) => void;
  selectedClips: Array<any>;
  setSelectedClips: (selectedClips: Array<any>) => void;
  showSubtitles: boolean;
  setShowSubtitles: (showSubtitles: boolean) => void;
  videoClips: any[];
  setVideoClips: (videoClips: any[]) => void;
  subtitleClips: SubtitleClip[];
  setSubtitleClips: (subtitleClips: SubtitleClip[]) => void;
  hasUnsavedChanges: boolean;
  setHasUnsavedChanges: (hasUnsavedChanges: boolean) => void;
  originalSelectedClips: Array<any>;
  setOriginalSelectedClips: (originalSelectedClips: Array<any>) => void;
  originalVideoClips: any[];
  setOriginalVideoClips: (originalVideoClips: any[]) => void;
}

export const useEditorStore = create<EditorStore>((set) => ({
  storyName: "",
  setStoryName: (storyName) => set({ storyName }),
  activeClip: null,
  setActiveClip: (activeClip) => set({ activeClip }),
  selectedClips: [],
  setSelectedClips: (selectedClips) => set({ selectedClips }),
  showSubtitles: true,
  setShowSubtitles: (showSubtitles) => set({ showSubtitles }),
  videoClips: [],
  setVideoClips: (videoClips) => set({ videoClips }),
  subtitleClips: [],
  setSubtitleClips: (subtitleClips) => set({ subtitleClips }),
  hasUnsavedChanges: false,
  setHasUnsavedChanges: (hasUnsavedChanges) => set({ hasUnsavedChanges }),
  originalSelectedClips: [],
  setOriginalSelectedClips: (originalSelectedClips) => set({ originalSelectedClips }),
  originalVideoClips: [],
  setOriginalVideoClips: (originalVideoClips) => set({ originalVideoClips }),
}));