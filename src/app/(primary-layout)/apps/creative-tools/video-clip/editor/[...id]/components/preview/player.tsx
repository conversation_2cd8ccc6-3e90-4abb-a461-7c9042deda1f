"use client";

import { useRef, useEffect, useCallback, useState, useMemo } from "react";
import { useEditorStore } from "../../stores/editor";
import { usePreviewStore } from "../../stores/preview";
import { drawRoundedRect, wrapText } from "../../utils";

type ComposedTimelineItem = {
  clipId: string;
  videoUrl: string;
  inPoint: number;
  outPoint: number;
  duration: number;
  globalStart: number;
  globalEnd: number;
};
interface PlayerProps {
  currentTime: number;
  isPlaying: boolean;
  onTimeUpdate: (time: number) => void;
  previewTime?: number; // 可选的预览时间，用于拖拽入点/出点时的预览
}

export function Player({
  currentTime,
  isPlaying,
  onTimeUpdate,
  previewTime,
}: PlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [canvasDimensions, setCanvasDimensions] = useState({
    width: 360,
    height: 640,
  });
  const [videoAspectRatio, setVideoAspectRatio] = useState(9 / 16); // 默认竖屏比例

  const { showSubtitles, subtitleClips, videoClips, activeClip } =
    useEditorStore();
  const { playMode, playbackSpeed } = usePreviewStore();

  // 创建成片时间轴
  const composedTimeline = useMemo((): ComposedTimelineItem[] => {
    if (!videoClips || videoClips.length === 0) return [];

    return videoClips.map((clip) => {
      const clipDuration = clip.outPoint - clip.inPoint;
      const item: ComposedTimelineItem = {
        clipId: clip.id,
        videoUrl: clip.videoUrl,
        inPoint: clip.inPoint,
        outPoint: clip.outPoint,
        duration: clipDuration,
        globalStart: clip.startTime, // 使用 clip.startTime 作为全局开始时间
        globalEnd: clip.endTime, // 使用 clip.endTime 作为全局结束时间
      };
      return item;
    });
  }, [videoClips]);

  // 计算响应式Canvas尺寸
  const calculateCanvasDimensions = useCallback(() => {
    // 使用60vh作为基准高度
    const targetHeight = window.innerHeight * 0.6;
    const targetWidth = targetHeight * videoAspectRatio;

    // 确保宽度不超过窗口宽度的90%
    const maxWidth = window.innerWidth * 0.9;

    let finalWidth = targetWidth;
    let finalHeight = targetHeight;

    if (targetWidth > maxWidth) {
      finalWidth = maxWidth;
      finalHeight = maxWidth / videoAspectRatio;
    }

    return {
      width: Math.floor(finalWidth),
      height: Math.floor(finalHeight),
    };
  }, [videoAspectRatio]);

  // 窗口大小变化监听
  useEffect(() => {
    const handleResize = () => {
      const newDimensions = calculateCanvasDimensions();
      setCanvasDimensions(newDimensions);
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [calculateCanvasDimensions]);

  // 获取当前应该播放的视频和时间
  const getCurrentComposedItem = () => {
    if (composedTimeline.length === 0) {
      return null;
    }

    if (playMode === "single") {
      // single模式：播放当前激活的切片
      if (activeClip) {
        return (
          composedTimeline.find(
            (item) => item.clipId === String(activeClip.id)
          ) || composedTimeline[0]
        );
      }
      return composedTimeline[0] || null;
    }

    // default和all模式：根据当前时间查找对应的片段
    return composedTimeline.find(
      (item) => currentTime >= item.globalStart && currentTime < item.globalEnd
    );
  };

  const currentComposedItem = getCurrentComposedItem();
  const currentVideoUrl = currentComposedItem?.videoUrl || "";
  const currentGlobalStart = currentComposedItem?.globalStart || 0;

  // 用于跟踪播放模式和激活切片的变化
  const prevPlayModeRef = useRef(playMode);
  const prevActiveClipRef = useRef(activeClip);

  // 添加前一个片段的引用，用于检测片段切换
  const prevComposedItemRef = useRef<ComposedTimelineItem | null>(null);

  // 获取当前应该使用的视频元素
  const getCurrentVideoElement = useCallback(() => {
    return videoRef.current;
  }, []);

  // 初始化Canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const { width, height } = canvasDimensions;
    canvas.width = width;
    canvas.height = height;
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;

    const ctx = canvas.getContext("2d");
    if (ctx) {
      ctx.fillStyle = "#000000";
      ctx.fillRect(0, 0, width, height);
    }
  }, [canvasDimensions, currentVideoUrl]);

  // Canvas绘制函数
  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const video = getCurrentVideoElement();
    if (!canvas || !video || !isVideoLoaded) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 设置黑色背景
    ctx.fillStyle = "#000000";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制视频帧
    const videoWidth = video.videoWidth;
    const videoHeight = video.videoHeight;

    if (videoWidth > 0 && videoHeight > 0) {
      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;

      // 计算视频在Canvas中的显示尺寸和位置（保持宽高比，居中显示）
      const videoAspect = videoWidth / videoHeight;
      const canvasAspect = canvasWidth / canvasHeight;

      let drawWidth, drawHeight, drawX, drawY;

      if (videoAspect > canvasAspect) {
        // 视频更宽，以宽度为准
        drawWidth = canvasWidth;
        drawHeight = canvasWidth / videoAspect;
        drawX = 0;
        drawY = (canvasHeight - drawHeight) / 2;
      } else {
        // 视频更高，以高度为准
        drawHeight = canvasHeight;
        drawWidth = canvasHeight * videoAspect;
        drawX = (canvasWidth - drawWidth) / 2;
        drawY = 0;
      }

      // 绘制视频帧
      try {
        ctx.drawImage(video, drawX, drawY, drawWidth, drawHeight);
      } catch (error) {
        console.warn("Canvas drawImage error:", error);
      }
    }

    // 绘制字幕
    if (showSubtitles && subtitleClips && subtitleClips.length > 0) {
      // 使用预览时间（如果存在）或当前时间来查找字幕
      const effectiveTime =
        previewTime !== undefined ? previewTime : currentTime;
      const currentSubtitle = subtitleClips.find(
        (subtitle) =>
          effectiveTime >= subtitle.startTime &&
          effectiveTime <= subtitle.endTime
      );

      if (currentSubtitle && currentSubtitle.translationText) {
        const centerX = canvas.width / 2;
        const bottomMargin = 40;
        const lineHeight = 24;
        const maxWidth = canvas.width - 40; // 左右留20px边距
        const padding = 12; // 背景内边距
        const borderRadius = 8; // 圆角半径

        // 设置字幕样式
        ctx.font = "12px Arial, sans-serif";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";

        // 只显示翻译文本
        const lines = wrapText(ctx, currentSubtitle.translationText, maxWidth);
        const totalHeight = lines.length * lineHeight;
        const startY =
          canvas.height - bottomMargin - totalHeight + lineHeight / 2;

        // 计算背景矩形的尺寸
        let maxLineWidth = 0;
        lines.forEach((line: string) => {
          const lineWidth = ctx.measureText(line).width;
          if (lineWidth > maxLineWidth) {
            maxLineWidth = lineWidth;
          }
        });

        // 背景矩形参数
        const bgWidth = Math.min(maxLineWidth + padding * 2, canvas.width - 20);
        const bgHeight = totalHeight + padding * 2;
        const bgX = centerX - bgWidth / 2;
        const bgY = startY - lineHeight / 2 - padding;

        // 绘制半透明黑色背景
        ctx.fillStyle = "rgba(0, 0, 0, 0.75)";
        drawRoundedRect(ctx, bgX, bgY, bgWidth, bgHeight, borderRadius);
        ctx.fill();

        // 绘制翻译字幕文字
        ctx.fillStyle = "#ffffff";
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = 2;

        lines.forEach((line: string, index: number) => {
          const y = startY + index * lineHeight;
          ctx.strokeText(line, centerX, y);
          ctx.fillText(line, centerX, y);
        });
      }
    }
  }, [
    currentTime,
    previewTime,
    subtitleClips,
    showSubtitles,
    isVideoLoaded,
    getCurrentVideoElement,
  ]);

  // 动画循环
  useEffect(() => {
    let animationId: number;

    const animate = () => {
      drawCanvas();
      animationId = requestAnimationFrame(animate);
    };

    if (isVideoLoaded) {
      animate();
    }

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [drawCanvas, isVideoLoaded]);

  // 处理视频加载
  const handleLoadedData = useCallback(() => {
    const video = getCurrentVideoElement();
    const canvas = canvasRef.current;
    if (!video || !canvas) return;

    const videoWidth = video.videoWidth;
    const videoHeight = video.videoHeight;

    setIsVideoLoaded(true);
    setLoadingError(null);

    // 计算并保存视频宽高比
    const aspectRatio = videoWidth / videoHeight;
    setVideoAspectRatio(aspectRatio);

    // 重新计算响应式尺寸
    const newDimensions = calculateCanvasDimensions();
    setCanvasDimensions(newDimensions);

    if (isPlaying) {
      video.play().catch((e: any) => console.error("Auto-play failed", e));
    }
  }, [isPlaying, calculateCanvasDimensions, getCurrentVideoElement]);

  // 当视频宽高比改变时重新计算尺寸
  useEffect(() => {
    if (videoAspectRatio && isVideoLoaded) {
      const newDimensions = calculateCanvasDimensions();
      setCanvasDimensions(newDimensions);
    }
  }, [videoAspectRatio, calculateCanvasDimensions, isVideoLoaded]);

  // 视频播放控制
  useEffect(() => {
    const video = getCurrentVideoElement();
    if (!video || !isVideoLoaded) return;

    if (isPlaying) {
      // 在single模式下，开始播放时检查是否需要跳转到入点
      if (playMode === "single" && currentComposedItem) {
        // 检查当前播放位置是否在入点出点范围内
        const currentVideoTime = video.currentTime;
        const isOutOfRange =
          currentVideoTime < currentComposedItem.inPoint ||
          currentVideoTime >= currentComposedItem.outPoint;

        if (isOutOfRange) {
          console.log("single模式：播放时跳转到入点", {
            currentVideoTime,
            inPoint: currentComposedItem.inPoint,
            outPoint: currentComposedItem.outPoint,
          });

          // 跳转到入点
          video.currentTime = currentComposedItem.inPoint;
          // 更新全局时间到入点对应的位置
          const inPointGlobalTime = currentGlobalStart;
          onTimeUpdate(inPointGlobalTime);
        }
      }

      video.play().catch((e) => console.error("Play failed", e));
    } else {
      video.pause();
    }
  }, [
    isPlaying,
    isVideoLoaded,
    getCurrentVideoElement,
    playMode,
    currentComposedItem,
    currentGlobalStart,
    onTimeUpdate,
  ]);

  // 播放速度控制
  useEffect(() => {
    const video = getCurrentVideoElement();
    if (!video || !isVideoLoaded) return;

    video.playbackRate = playbackSpeed;
    console.log("设置播放速度:", playbackSpeed);
  }, [playbackSpeed, isVideoLoaded, getCurrentVideoElement]);

  // 检测播放模式和激活切片变化，更新引用但不自动跳转
  useEffect(() => {
    // 只更新引用，不执行自动跳转逻辑
    prevPlayModeRef.current = playMode;
    prevActiveClipRef.current = activeClip;
  }, [playMode, activeClip]);

  // 检测片段切换
  useEffect(() => {
    const prevItem = prevComposedItemRef.current;
    const hasClipChanged = prevItem?.clipId !== currentComposedItem?.clipId;

    if (hasClipChanged && currentComposedItem) {
      console.log(
        "片段切换:",
        prevItem?.clipId,
        "->",
        currentComposedItem.clipId
      );

      console.log("使用主视频元素");
      // 使用主视频元素
      const video = videoRef.current;
      if (video && video.src !== currentVideoUrl) {
        setIsVideoLoaded(false);
        video.pause();
        video.src = currentVideoUrl;
        video.load();
      }
    }

    prevComposedItemRef.current = currentComposedItem || null;
  }, [currentComposedItem, currentVideoUrl, currentTime]);

  // 视频时间同步 - 根据播放模式处理时间映射
  useEffect(() => {
    const video = getCurrentVideoElement();
    if (!video || !isVideoLoaded || !currentComposedItem) return;

    // 使用预览时间（如果存在）或当前时间
    const effectiveTime = previewTime !== undefined ? previewTime : currentTime;

    let videoPlayTime: number;

    if (playMode === "single") {
      // single模式：时间轴时间直接映射到视频时间（考虑入点偏移）
      const relativeTime = effectiveTime - currentGlobalStart;
      videoPlayTime = currentComposedItem.inPoint + relativeTime;

      // 确保播放时间在入点和出点之间
      videoPlayTime = Math.max(
        currentComposedItem.inPoint,
        Math.min(currentComposedItem.outPoint, videoPlayTime)
      );
    } else {
      // default和all模式：直接映射时间轴位置到视频时间，不受入点出点限制
      const relativeTime = effectiveTime - currentGlobalStart;
      videoPlayTime = relativeTime;
    }

    const timeDiff = Math.abs(video.currentTime - videoPlayTime);

    // 增加时间同步的容差，避免频繁跳转
    if (timeDiff > 0.2) {
      console.log(
        "时间同步:",
        video.currentTime,
        "->",
        videoPlayTime,
        previewTime !== undefined ? "(预览模式)" : "",
        playMode === "single" ? "(single模式)" : ""
      );
      video.currentTime = videoPlayTime;
    }
  }, [
    currentTime,
    previewTime,
    currentComposedItem,
    isVideoLoaded,
    currentGlobalStart,
    getCurrentVideoElement,
    playMode,
  ]);

  // 处理视频时间更新 - 根据播放模式处理时间映射和停止逻辑
  const handleTimeUpdate = useCallback(
    (event?: any) => {
      const video = getCurrentVideoElement();
      if (!video || !isPlaying || !currentComposedItem) return;

      // 如果事件来自特定的视频元素，确保它是当前活跃的视频
      if (event && event.target && event.target !== video) {
        return; // 忽略非当前活跃视频的时间更新事件
      }

      if (playMode === "single") {
        // single模式：检查是否到达出点，如果是则停止播放
        if (video.currentTime >= currentComposedItem.outPoint) {
          console.log("single模式：到达出点，自动暂停");
          // 设置到出点位置
          video.currentTime = currentComposedItem.outPoint;
          const outPointGlobalTime =
            currentGlobalStart +
            (currentComposedItem.outPoint - currentComposedItem.inPoint);
          onTimeUpdate(outPointGlobalTime);

          // 暂停播放
          video.pause();
          return;
        }

        // 确保播放时间不小于入点
        if (video.currentTime < currentComposedItem.inPoint) {
          video.currentTime = currentComposedItem.inPoint;
        }

        // 计算相对于入点的时间
        const relativeTime = video.currentTime - currentComposedItem.inPoint;
        const globalTime = currentGlobalStart + relativeTime;
        onTimeUpdate(globalTime);
        return;
      }

      // default和all模式的原有逻辑
      const relativeTime = video.currentTime;

      // 检查是否超过当前片段的总时长
      if (video.currentTime >= video.duration) {
        if (playMode === "all") {
          // 自动切换到下一个片段
          const nextIndex =
            composedTimeline.findIndex(
              (item) => item.clipId === currentComposedItem.clipId
            ) + 1;
          if (nextIndex < composedTimeline.length) {
            const nextItem = composedTimeline[nextIndex];
            onTimeUpdate(nextItem.globalStart);
            // onClipActivate?.(nextItem.clipId); // 激活下一个片段
          } else {
            // 播放结束，停止播放
            console.log("播放完成，自动暂停");
            onTimeUpdate(currentComposedItem.globalEnd);

            // 暂停视频元素
            video.pause();

            // 触发暂停事件
            setTimeout(() => {
              // 这里可以触发播放器的暂停状态
            }, 100);
          }
          return;
        } else {
          // 单片播放模式，停留在视频结尾
          onTimeUpdate(currentComposedItem.globalStart + video.duration);
          setTimeout(() => {
            if (video) video.pause();
          }, 100);
          return;
        }
      }

      // 更新全局时间 - 直接映射视频时间到时间轴
      const globalTime = currentGlobalStart + relativeTime;
      onTimeUpdate(globalTime);
    },
    [
      getCurrentVideoElement,
      isPlaying,
      currentComposedItem,
      currentGlobalStart,
      onTimeUpdate,
      playMode,
      composedTimeline,
    ]
  );

  // 视频加载处理
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !currentVideoUrl) return;

    // 如果视频URL没有变化，不需要重新加载
    if (video.src === currentVideoUrl) return;

    console.log("加载视频:", currentVideoUrl);
    setIsVideoLoaded(false);
    setLoadingError(null);

    video.pause();
    video.src = currentVideoUrl;
    video.load();

    // 视频加载完成后，根据播放模式设置起始位置
    const handleLoadedMetadata = () => {
      console.log("视频元数据加载完成");
      setIsVideoLoaded(true);
      if (currentComposedItem) {
        if (playMode === "single") {
          // single模式：设置到入点
          video.currentTime = currentComposedItem.inPoint;
          console.log(
            "single模式：设置视频时间到入点:",
            currentComposedItem.inPoint
          );
        } else {
          // 其他模式：设置到片段起点（通常是0）
          video.currentTime = 0;
          console.log("设置视频时间到起点:", 0);
        }
      }
      video.removeEventListener("loadedmetadata", handleLoadedMetadata);
    };

    const handleError = () => {
      console.error("视频加载失败:", video.error);
      setLoadingError("视频加载失败");
      setIsVideoLoaded(false);
    };

    video.addEventListener("loadedmetadata", handleLoadedMetadata);
    video.addEventListener("error", handleError);

    // 清理函数
    return () => {
      video.removeEventListener("loadedmetadata", handleLoadedMetadata);
      video.removeEventListener("error", handleError);
    };
  }, [currentVideoUrl, currentComposedItem, playMode]);

  // 视频加载完成后的精确时间同步 - 根据播放模式处理时间映射
  useEffect(() => {
    const video = getCurrentVideoElement();
    if (!video || !isVideoLoaded || !currentComposedItem) return;

    // 使用预览时间（如果存在）或当前时间
    const effectiveTime = previewTime !== undefined ? previewTime : currentTime;
    const relativeTime = effectiveTime - currentGlobalStart;

    let videoPlayTime: number;

    if (playMode === "single") {
      // single模式：映射到入点+相对时间
      videoPlayTime = currentComposedItem.inPoint + relativeTime;
      // 确保在入点和出点之间
      videoPlayTime = Math.max(
        currentComposedItem.inPoint,
        Math.min(currentComposedItem.outPoint, videoPlayTime)
      );
    } else {
      // default和all模式：直接使用相对时间作为视频播放时间
      videoPlayTime = relativeTime;
    }

    // 确保时间在视频总时长范围内
    if (videoPlayTime >= 0 && videoPlayTime <= video.duration) {
      const timeDiff = Math.abs(video.currentTime - videoPlayTime);
      if (timeDiff > 0.1) {
        console.log(
          "精确时间同步:",
          video.currentTime,
          "->",
          videoPlayTime,
          previewTime !== undefined ? "(预览模式)" : "",
          playMode === "single" ? "(single模式)" : ""
        );
        video.currentTime = videoPlayTime;
      }
    }
  }, [
    isVideoLoaded,
    currentTime,
    previewTime,
    currentComposedItem,
    currentGlobalStart,
    getCurrentVideoElement,
    playMode,
  ]);

  const handleError = () => {
    const video = getCurrentVideoElement();
    const errorMessage = video?.error?.message || "视频加载失败";
    setLoadingError(errorMessage);
    setIsVideoLoaded(false);
  };

  return (
    <div className="relative flex items-center justify-center w-full">
      {/* 隐藏的视频元素 */}
      <video
        ref={videoRef}
        style={{ display: "none" }}
        onLoadedData={handleLoadedData}
        onError={handleError}
        onTimeUpdate={handleTimeUpdate}
        crossOrigin="anonymous"
        preload="auto"
        playsInline
      />

      <div
        className="relative bg-black flex items-center justify-center overflow-hidden"
        style={{
          width: `${canvasDimensions.width}px`,
          height: `${canvasDimensions.height}px`,
        }}
      >
        {/* Canvas渲染层 */}
        <canvas
          ref={canvasRef}
          className="w-full h-full"
          style={{
            width: `${canvasDimensions.width}px`,
            height: `${canvasDimensions.height}px`,
          }}
        />

        {/* 加载状态 */}
        {!isVideoLoaded && !loadingError && currentComposedItem && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <div>视频加载中...</div>
            </div>
          </div>
        )}

        {/* 错误状态 */}
        {loadingError && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="text-red-500 text-center p-4">
              <div className="mb-2">视频加载失败</div>
              <div className="text-sm">{loadingError}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
