import requestHelper from "@/utils/requestHelper";

/**
 * 获取故事线切片详情
 * @param data
 * @returns
 */
export function getStoryLineDetailInfo(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/story/line/detail/info",
    data
  );
}

/**
 * 短剧剪辑故事线结果查询
 * @param data
 * @returns
 */
export function getShortDramaList(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/task/story/line/info",
    data
  );
}

/**
 * 获取短剧帧信息
 * @param data
 * @returns
 */
export function getShortDramaFrame(data = {}) {
  return requestHelper.post(
    "v4/demand/assemble/works/files/video/frame/v2",
    data
  );
}

/**
 * 更新短剧片段的入点和出点
 * @param data
 * @returns
 */
export function updateShortDramaInAndOutPoint(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/story/line/detail/duration/update/batch",
    data
  );
}

/**
 * 创建导出任务
 * @param data
 * @returns
 */
export function getGenerateCreate(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/story/line/video/generate/create",
    data
  );
}