.table-loading {
  :global {
    .ant-spin-spinning {
      max-height: 100% !important;
    }
    .ant-spin-container {
      opacity: 1 !important;
      &::after {
        opacity: 0 !important;
      }
    }
    .ant-table {
      background-color: #e5e5e5 !important;
    }
    .ant-table-tbody {
      .ant-table-cell {
        border-color: #cecfd3 !important;
      }
    }
  }
}

.story-line-table {
  :global {
    .ant-table-thead {
      .ant-table-cell {
        padding-top: 8px !important;
        padding-bottom: 9px !important;
      }
    }
    .ant-table-tbody {
      .ant-table-cell {
        padding-top: 4px !important;
        padding-bottom: 4px !important;
      }
    }
    .selectedStoryline {
      background-color: #e6f7ff !important;
    }
  }
}

.action-btn {
  :global {
    .ant-btn-icon {
      display: flex;
    }
  }
}