/*
 * @Date: 2025-07-25 10:34:24
 * @Author: miroku.yang
 * @Description: 
 */
import requestHelper from "@/utils/requestHelper";

/**
 * 短剧剪辑版本列表查询
 * @param data
 * @returns
 */
export function getShortDramaVersionList(data = {}) {
  return requestHelper.post(
    "/v4/demand/short/drama/highlights/task/story/line/version/list",
    data
  );
}

/**
 * 短剧剪辑故事线结果查询
 * @param data
 * @returns
 */
export function getShortDramaList(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/task/story/line/info",
    data
  );
}

/**
 * 创建导出任务
 * @param data
 * @returns
 */
export function getShortDramaGenerate(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/story/line/regenerate",
    data
  );
}

/**
 * 短剧故事线版本删除
 * @param data
 * @returns
 */
export function deleteShortDramaVersion(data = {}) {
  return requestHelper.post(
    "/v4/demand/short/drama/highlights/task/story/line/version/delete",
    data
  );
}

/**
 * 短剧剪辑故事线删除
 * @param data
 * @returns
 */
export function deleteShortDramaStoryLine(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/task/story/line/delete",
    data
  );
}

/**
 * 重新生成故事线
 * @param data
 * @returns
 */
export function getShortHighlightsRetry(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/video/extra/retry",
    data
  );
}

/**
 * 创建导出任务
 * @param data
 * @returns
 */
export function getGenerateCreate(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/story/line/video/generate/create",
    data
  );
}

/**
 * 获取故事线切片详情
 * @param data
 * @returns
 */
export function getStoryLineDetailInfo(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/story/line/detail/info",
    data
  );
}

/**
 * 创建导出任务
 * @param data
 * @returns
 */
export function downloadSubtitle(id: string) {
  return requestHelper.get(
    `v4/demand/short/drama/highlights/story/line/video/download/captions/story/line/${id}`
  );
}