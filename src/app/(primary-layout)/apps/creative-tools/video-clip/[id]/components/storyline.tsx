"use client";

import React, { useState, useEffect, useRef } from "react";
import { But<PERSON>, Divider, Too<PERSON><PERSON>, message, Popover, Modal, Alert } from "antd";
import { cn } from "@/lib/utils";
import SinoSymbolFont from "@/components/Icon/SinoSymbolFont";
import {
  getShortDramaList,
  getShortDramaVersionList,
  getShortDramaGenerate,
  deleteShortDramaVersion,
  getShortHighlightsRetry,
  deleteShortDramaStoryLine,
} from "../services";
import { setUserStorage, getUserStorage } from "@/services/common";
import { useRouter } from "next/navigation";
import { getUniqueId } from "@/utils";
import { IconSvg } from "@/components";
import eventBus from "@/utils/eventBus";
import { EVENT_NEMES } from "@/components/Mqtt/consts";
import { VersionContent } from "./version-popover";
import styles from "../styles/storyline.module.scss";
import { StoryLineTable } from "./storyline-table";
import DownloadCenter from "./download-center";

interface Props {
  id: string;
}

export default function StoryLine({ id }: Props) {
  const [loading, setLoading] = useState(false);
  const [versionLoading, setVersionLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [shortName, setShortName] = useState<string>("");
  const [tableContainerSize, setTableContainerSize] = useState<any>({});
  const [disableReloadShort, setDisableReloadShort] = useState<boolean>(false);
  const [versionList, setVersionList] = useState<any[]>([]);
  const [currentVersion, setCurrentVersion] = useState<number>();
  const [versionOpen, setVersionOpen] = useState(false);
  const [selectedStorylineIds, setSelectedStorylineIds] = useState<string[]>([]);
  const [deleting, setDeleting] = useState<boolean>(false);
  const [regenerating, setRegenerating] = useState<boolean>(false);
  const [showNewVersionAlert, setShowNewVersionAlert] = useState<boolean>(false);
  const [showNewVersionBadge, setShowNewVersionBadge] = useState<boolean>(false);
  const tableContainerRef = useRef<any>(null);

  const router = useRouter();

  // 存储最后查看的版本 key
  const STORAGE_KEY = "last_viewed_short_drama_version_" + id;

  // 保存最后查看的版本
  const saveLastViewedVersion = async (version: number) => {
    try {
      await setUserStorage({
        key: STORAGE_KEY,
        value: version,
      });
    } catch (error) {
      console.error("保存用户版本记录失败:", error);
    }
  };

  // 获取版本列表
  const fetchVersionList = async (switchToLatest = false) => {
    setVersionLoading(true);
    try {
      // 1. 获取云端存储的最后访问版本
      const { data: storageData } = await getUserStorage({
        keys: [STORAGE_KEY],
      });
      const lastViewedVersion =
        storageData?.result?.[STORAGE_KEY] !== undefined
          ? Number(storageData.result[STORAGE_KEY])
          : null;

      // 2. 获取所有版本列表
      const { data: versionData } = await getShortDramaVersionList({ id });
      if (versionData?.code === 0 && versionData.result?.length > 0) {
        const sortedVersions = [...versionData.result].sort(
          (a, b) =>
            new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
        );
        setVersionList(sortedVersions);

        // 3. 决定当前版本
        const latestVersion = sortedVersions[0].version;
        const lastViewedVersionExists = sortedVersions.some(
          (v) => v.version === lastViewedVersion
        );

        if (switchToLatest) {
          setCurrentVersion(latestVersion);
        } else if (lastViewedVersion !== null && lastViewedVersionExists) {
          setCurrentVersion(lastViewedVersion);
        } else {
          setCurrentVersion(latestVersion);
        }
      } else {
        setVersionList([]);
        setCurrentVersion(undefined);
      }
    } catch (error) {
      console.error("获取版本列表失败:", error);
      message.error("获取版本列表失败，请稍后重试");
    } finally {
      setVersionLoading(false);
    }
  };

  // 处理数据源，为每个故事线生成唯一标识
  const processDataSource = (data: any[]) => {
    return data.map((storyline) => ({
      id: storyline.id,
      key: getUniqueId(),
      storyName: storyline.storyName,
      previewUrl: storyline.previewUrl,
      storyLineDetailCount: storyline.storyLineDetailCount,
      summary: storyline.summary,
      duration: storyline.duration || 0,
      updateTime: storyline.updateTime || "未知",
      storyLineDetailInfoVOList: storyline.storyLineDetailInfoVOList || [],
    }));
  };

  // 获取短剧列表数据
  const fetchShortDramaList = async () => {
    if (currentVersion === undefined) return;

    setLoading(true);
    try {
      const { data } = await getShortDramaList({ id, version: currentVersion });
      if (data?.code === 0) {
        const { taskName, status, storyLineInfoVOList = [] } = data.result;
        setShortName(taskName);
        setRegenerating(status === 3 || status === 4);
        setDisableReloadShort(
          !storyLineInfoVOList.some(
            (item: any) =>
              item.videoCompositingStatus !== 3 ||
              item.storyLineDetailInfoVOList?.some(
                (ep: any) => ep.sliceStatus !== 3
              )
          )
        );
        setDataSource(processDataSource(storyLineInfoVOList));
      }
    } catch (error) {
      console.error("获取短剧列表失败:", error);
      message.error("获取短剧列表失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  // 重新生成短剧故事线
  const regenerateShortDrama = async () => {
    try {
      setRegenerating(true);
      const { data } = await getShortDramaGenerate({ id });
      if (data?.code == 0) {
        message.success("重新生成任务已提交");
      }
    } catch (error) {
      console.error("重新生成短剧故事线失败:", error);
    }
  };

  // 只更新版本列表，不切换当前版本
  const updateVersionListWithoutSwitching = async () => {
    try {
      const { data: versionData } = await getShortDramaVersionList({ id });
      if (versionData.result.some((v: any) => v.isRead === 0)) {
        setShowNewVersionBadge(true);
      } else {
        setShowNewVersionBadge(false);
      }
      if (versionData?.code === 0 && versionData.result?.length > 0) {
        const sortedVersions = [...versionData.result].sort(
          (a, b) =>
            new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
        );
        setVersionList(sortedVersions);
      }
    } catch (error) {
      console.error("静默更新版本列表失败:", error);
    }
  };

  // 返回
  const handleBack = () => {
    router.push("/apps/creative-tools/video-clip");
  };

  // 版本变更处理
  const handleVersionChange = (version: number) => {
    const newVersion = version ?? versionList[versionList.length - 1].version;
    setCurrentVersion(newVersion);
    setVersionOpen(false); // 关闭 Popover
    setShowNewVersionAlert(false);
    // 重置表格滚动条位置
    if (tableContainerRef.current) {
      const tableBody =
        tableContainerRef.current.querySelector(".ant-table-body");
      if (tableBody) {
        tableBody.scrollTop = 0;
      }
    }
  };

  // 删除版本
  const handleDeleteVersion = async (e: React.MouseEvent, version: number) => {
    e.stopPropagation(); // 阻止事件冒泡

    // 检查是否只剩一个版本
    if (versionList.length <= 1) {
      message.warning("不支持删除最后一个任务版本");
      return;
    }

    // 显示确认弹窗
    Modal.confirm({
      title: "确认删除",
      content: "版本删除后不可恢复。",
      okText: "确认",
      cancelText: "取消",
      async onOk() {
        try {
          const { data } = await deleteShortDramaVersion({ id, version });
          if (data?.code === 0) {
            message.success("删除成功");
            await fetchVersionList();
          }
        } catch (error) {
          console.error("删除版本失败:", error);
          message.error("删除版本失败，请稍后重试");
        }
      },
    });
  };

  // 删除故事线
  const handleDeleteStorylines = () => {
    if (!selectedStorylineIds.length) return;

    const isDeletingLastStoryline =
      selectedStorylineIds.length === dataSource.length;
    if (isDeletingLastStoryline && versionList.length <= 1) {
      message.warning("不支持删除最后一个任务版本");
      return;
    }

    Modal.confirm({
      title: "确认删除选中的故事线吗？",
      content: "故事线删除后，不可恢复。",
      okText: "确认",
      cancelText: "取消",
      onOk: async () => {
        try {
          setDeleting(true);
          const { data } = await deleteShortDramaStoryLine({
            id,
            version: currentVersion,
            lineIdList: selectedStorylineIds,
          });
          if (data?.code === 0) {
            message.success("删除成功");
            isDeletingLastStoryline
              ? await fetchVersionList()
              : fetchShortDramaList();
            setSelectedStorylineIds([]);
          }
        } catch (error) {
          console.error("删除故事线失败:", error);
          message.error("删除失败");
        } finally {
          setDeleting(false);
        }
      },
    });
  };

  // 删除单条故事线
  const handleDeleteSingleStoryline = (storyLineId: string) => {
    const isDeletingLastStoryline = dataSource.length === 1;
    if (isDeletingLastStoryline && versionList.length <= 1) {
      message.warning("不支持删除最后一个任务版本");
      return;
    }

    Modal.confirm({
      title: "确认删除该故事线吗？",
      content: "故事线删除后，不可恢复。",
      okText: "确认",
      cancelText: "取消",
      onOk: async () => {
        try {
          setDeleting(true);
          const { data } = await deleteShortDramaStoryLine({
            id,
            version: currentVersion,
            lineIdList: [storyLineId],
          });
          if (data?.code === 0) {
            message.success("删除成功");
            isDeletingLastStoryline
              ? await fetchVersionList()
              : fetchShortDramaList();
            setSelectedStorylineIds(prev => prev.filter(id => id !== storyLineId));
          }
        } catch (error) {
          console.error("删除故事线失败:", error);
          message.error("删除失败");
        } finally {
          setDeleting(false);
        }
      },
    });
  };

  // 监听 currentVersion 变化，获取对应版本的数据
  useEffect(() => {
    const fetchData = async () => {
      if (currentVersion !== undefined) {
        await fetchShortDramaList();
        saveLastViewedVersion(currentVersion);
        await updateVersionListWithoutSwitching();
      }
    };
    fetchData();
  }, [currentVersion]);

  // 监听任务状态
  useEffect(() => {
    const handleTaskUpdate = (data: any) => {
      console.log("Storyline page received MQTT message:", data);
      const { taskId, taskStatus } = data;

      if (taskId === Number(id) && taskStatus === 5) {
        console.log(
          `Task ${taskId} completed successfully, showing new version alert.`
        );
        updateVersionListWithoutSwitching();
        setShowNewVersionAlert(true);
        setRegenerating(false);
      }
    };

    eventBus.on(EVENT_NEMES.SHORT_DRAMA_HIGHLIGHTS, handleTaskUpdate);
    return () => {
      eventBus.off(EVENT_NEMES.SHORT_DRAMA_HIGHLIGHTS, handleTaskUpdate);
    };
  }, [id]);

  // 初始化
  useEffect(() => {
    fetchVersionList(); // 初始化时获取版本列表，会自动设置最新版本并触发上面的 effect

    const tabHeight = 50,
      tableHeaderHieght = 40;
    if (tableContainerRef.current) {
      const width = tableContainerRef.current.offsetWidth;
      const height =
        tableContainerRef.current.offsetHeight - tabHeight - tableHeaderHieght;
      setTableContainerSize({
        width: width,
        height: height,
      });
    }
  }, []);

  return (
    <div className="w-full h-full overflow-hidden" ref={tableContainerRef}>
      <div className="flex justify-between items-center">
        <div className="flex items-center mb-4">
          <span
            onClick={handleBack}
            className={"cursor-pointer flex items-center hover:text-[#3A84FF]"}
          >
            <SinoSymbolFont type="icon-返回" className="text-[16px] mr-[4px]" />
            <span className="text-[14px] cursor-pointer leading-[22px]">
              返回
            </span>
          </span>
          <Divider type="vertical" className="!top-[1px] !h-[12px]" />
          <div className="flex items-center">
            <span className="text-[#86909C] text-[14px] leading-[22px]">
              当前版本：
            </span>
            <Popover
              content={
                <VersionContent
                  versionLoading={versionLoading}
                  versionList={versionList}
                  currentVersion={currentVersion}
                  shortName={shortName}
                  onVersionChange={handleVersionChange}
                  onDeleteVersion={handleDeleteVersion}
                />
              }
              trigger="click"
              placement="bottomLeft"
              arrow={false}
              open={versionOpen}
              onOpenChange={setVersionOpen}
            >
              <div className="flex items-center text-[14px] leading-[22px] cursor-pointer hover:bg-[#F2F3F5] rounded-[4px] p-1">
                <div className="flex items-center gap-2">
                  <span className="text-[12px] leading-[22px] bg-blue-200 text-blue-500 px-1 rounded-md">
                    {`版本${
                      currentVersion !== undefined
                        ? currentVersion + 1
                        : "加载中..."
                    }`}
                  </span>
                  <Tooltip
                    placement="bottomLeft"
                    title={shortName.length > 15 ? shortName : ""}
                  >
                    <span className="text-[14px] leading-[22px] inline-block max-w-[300px] truncate">
                      {shortName}
                    </span>
                  </Tooltip>
                </div>
                <IconSvg
                  type="icon-arrow-down"
                  style={{ fontSize: 12, marginLeft: 8 }}
                  className="text-[#86909C]"
                />
              </div>
            </Popover>

            {showNewVersionBadge && (
              <span className="ml-2 bg-red-500 text-[10px] text-white rounded-full px-2 py-1">
                新增
              </span>
            )}
          </div>
        </div>
        <div className="mb-4">
          <DownloadCenter taskId={id} />
          <Button
            color="primary"
            variant="outlined"
            onClick={handleDeleteStorylines}
            disabled={selectedStorylineIds.length === 0 || deleting}
            loading={deleting}
            className={cn("mr-4", styles["action-btn"])}
            icon={<IconSvg type="icon-delete" style={{ fontSize: 14 }} />}
          >
            删除故事线
          </Button>
          <Button
            color="primary"
            variant="solid"
            onClick={regenerateShortDrama}
            disabled={!dataSource.length || regenerating}
            icon={
              <IconSvg
                type={"icon-refresh"}
                style={{ fontSize: 14 }}
                className={regenerating ? "animate-spin" : ""}
              />
            }
            className={styles["action-btn"]}
          >
            {regenerating ? "重新生成中..." : "重新生成故事线"}
          </Button>
        </div>
      </div>

      {showNewVersionAlert && (
        <div className="fixed top-0 left-0 right-0 z-50 mx-auto mt-6 mb-4 w-[300px] transition-all duration-300 ease-in-out">
          <Alert
            message="新版本已生成, 点击查看"
            type="success"
            showIcon
            closable
            onClose={() => setShowNewVersionAlert(false)}
            action={
              <Button
                size="small"
                type="link"
                onClick={() => {
                  fetchVersionList(true);
                  setShowNewVersionAlert(false);
                }}
              >
                立即查看
              </Button>
            }
          />
        </div>
      )}

      <StoryLineTable
        loading={loading}
        dataSource={dataSource}
        storyLineId={id}
        selectedStorylineIds={selectedStorylineIds}
        onSelectChange={(selectedKeys) => setSelectedStorylineIds(selectedKeys as string[])}
        containerHeight={tableContainerSize.height}
        onDelete={handleDeleteSingleStoryline}
        shortName={shortName}
      />
    </div>
  );
}
