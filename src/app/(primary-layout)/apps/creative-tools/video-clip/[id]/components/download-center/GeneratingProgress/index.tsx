/*
 * @Date: 2025-07-25 14:20:50
 * @Author: miroku.yang
 * @Description:
 */

const GeneratingProgress = () => {
  return (
    <div className="relative w-full h-1.5 overflow-hidden rounded bg-[#E5E6EB]">
      <div
        className="absolute inset-0 w-full h-full bg-[length:40px_40px] bg-[linear-gradient(135deg,#D1D5DB_25%,transparent_25%,transparent_50%,#D1D5DB_50%,#D1D5DB_75%,transparent_75%,transparent)] animate-stripe"
        style={{ backgroundPosition: '0 0' }}
      />
    </div>
  );
};
export default GeneratingProgress;
