"use client";

import React from "react";
import { IconSvg } from "@/components";
import { Trash } from "lucide-react";
import dayjs from "dayjs";
import { cn } from "@/lib/utils";
import { Tooltip } from "antd";

interface VersionContentProps {
  versionLoading: boolean;
  versionList: any[];
  currentVersion: number | undefined;
  shortName: string;
  onVersionChange: (version: number) => void;
  onDeleteVersion: (e: React.MouseEvent, version: number) => void;
}

export const VersionContent: React.FC<VersionContentProps> = ({
  versionLoading,
  versionList,
  currentVersion,
  shortName,
  onVersionChange,
  onDeleteVersion,
}) => {
  if (versionLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <IconSvg type="icon-loading" className="animate-spin mr-2" />
        <span>加载中...</span>
      </div>
    );
  }

  if (!versionList?.length) {
    return (
      <div className="flex items-center justify-center p-4 text-[#86909C]">
        暂无版本
      </div>
    );
  }

  return (
    <div className="flex flex-col min-w-[200px] max-h-[520px] overflow-y-auto gap-1">
      {versionList.map((item) => (
        <div
          key={item.version}
          className={cn(
            "flex items-center justify-between gap-5 p-2 hover:bg-[#F2F3F5] group rounded-lg cursor-pointer transition-all duration-300",
            item.version === currentVersion &&
              "bg-blue-100 border border-solid border-blue-400"
          )}
          onClick={() => onVersionChange(item.version)}
        >
          <div className="flex flex-col">
            <div className="flex items-center gap-2 max-w-[900px]">
              <span className="text-[12px] leading-[22px] bg-blue-200 text-blue-500 px-1 rounded-md">
                {`版本${item.version + 1}`}
              </span>
              <span className="text-[14px] leading-[22px] inline-block max-w-[300px] truncate">
                {shortName}
              </span>
            </div>

            <span className="text-[12px] text-[#86909C]">
              {dayjs(item.createTime).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
          <Trash
            className="w-4 h-4 text-gray-500 hover:text-red-500 cursor-pointer hover:scale-110 transition-all duration-300"
            onClick={(e) => onDeleteVersion(e, item.version)}
          />
        </div>
      ))}
    </div>
  );
};
