# React Video.js 多视频播放器

## 概述

这是一个完全基于 React 和 Video.js 的多视频合并播放器组件，将原始的 HTML/JavaScript 代码转换为现代的 React 组件。

## 特性

- ✅ 基于 Video.js 的专业视频播放
- ✅ 多个视频无缝连续播放
- ✅ 统一的全局进度条
- ✅ 现代化播放器控制界面
- ✅ 进度条点击跳转功能（修复了网页端点击问题）
- ✅ 交互式进度条（hover 时显示圆点并变大）
- ✅ 播放/暂停控制（SVG 图标，hover 背景高亮）
- ✅ 音量控制（hover 显示滑块，支持静音切换）
- ✅ 全屏功能（支持进入/退出全屏）
- ✅ 更多选项按钮（hover 背景高亮）
- ✅ 实时时间显示
- ✅ 适应父容器的响应式设计

## 组件结构

```tsx
export const VideojsPlayer: React.FC = () => {
  // 状态管理
  const [currentTime, setCurrentTime] = useState('0:10');
  const [totalDuration, setTotalDuration] = useState('0:36');
  const [progress, setProgress] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  
  // Video.js 播放器实例
  const playerRef = useRef<any>(null);
  
  // 视频配置
  const videos = useMemo(() => [
    { src: 'video1.mp4', duration: 36 },
    { src: 'video2.mp4', duration: 52 }
  ], []);
  
  // ...组件逻辑
};
```

## 核心功能实现

### 1. 播放器初始化

```tsx
useEffect(() => {
  const player = videojs(videoRef.current, {
    controls: false,        // 隐藏默认控制条
    fluid: false,
    responsive: false,
    preload: 'metadata',
    playsinline: true,
    sources: [{
      src: videos[0].src,
      type: 'video/mp4'
    }]
  });
  
  playerRef.current = player;
}, [videos, totalDurationSeconds]);
```

### 2. 进度更新机制

```tsx
const updateProgress = () => {
  const currentTimeSeconds = accumulatedTime + player.currentTime();
  const percent = (currentTimeSeconds / totalDurationSeconds) * 100;
  
  setProgress(percent);
  setCurrentTime(formatTime(currentTimeSeconds));
};
```

### 3. 视频切换逻辑

```tsx
const playNextVideo = () => {
  const newAccumulatedTime = accumulatedTime + player.currentTime();
  setAccumulatedTime(newAccumulatedTime);
  
  const nextIndex = currentVideoIndex + 1;
  
  if (nextIndex < videos.length) {
    setCurrentVideoIndex(nextIndex);
    player.src(videos[nextIndex].src);
    player.play();
  } else {
    player.pause();
    setIsPlaying(false);
  }
};
```

### 4. 进度条跳转

```tsx
const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
  const rect = e.currentTarget.getBoundingClientRect();
  const percent = (e.clientX - rect.left) / rect.width;
  const seekTime = percent * totalDurationSeconds;
  
  // 计算目标视频和时间点
  let timeAccumulated = 0;
  for (let i = 0; i < videos.length; i++) {
    if (seekTime <= timeAccumulated + videos[i].duration) {
      setCurrentVideoIndex(i);
      setAccumulatedTime(timeAccumulated);
      playerRef.current.src(videos[i].src);
      playerRef.current.currentTime(seekTime - timeAccumulated);
      break;
    }
    timeAccumulated += videos[i].duration;
  }
};
```

## 样式设计

组件使用 Tailwind CSS 类名，完全复制了原始 HTML 的视觉效果：

### 主要样式类名

- **容器布局**：`relative w-full h-full bg-black text-white`（适应父容器大小）
- **视频播放器**：`w-full h-full object-contain`
- **顶部状态栏**：`absolute top-0 left-0 right-0 p-4 flex justify-between bg-gradient-to-b from-black/70 to-transparent`
- **底部控制栏**：`absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent`
- **进度条容器**：`w-full h-1 bg-white/30 mb-4 rounded-sm cursor-pointer`
- **进度条填充**：`h-full bg-blue-500 rounded-sm relative`
- **控制按钮**：`w-11 h-11 rounded-full flex justify-center items-center bg-white/10`
- **播放按钮**：`w-14 h-14 rounded-full flex justify-center items-center bg-white/20`

### 设计特点

- **全屏布局**：`w-screen h-screen`
- **渐变背景**：`bg-gradient-to-b from-black/70 to-transparent`
- **移动端优化**：`touch-manipulation`
- **响应式设计**：`object-contain`
- **半透明效果**：`bg-white/10`, `bg-white/20`, `bg-white/30`

## 使用方法

### 基本使用

```tsx
import { VideojsPlayer } from './videojs-player';

export default function App() {
  return (
    {/* 重要：需要为播放器提供明确的容器大小 */}
    <div style={{ width: "850px", height: "478px" }}>
      <VideojsPlayer />
    </div>
  );
}
```

### 在弹窗中使用

```tsx
// 在 Modal 或其他容器中使用时，确保设置容器大小
<div className="relative" style={{ width: "850px", height: "478px" }}>
  <VideojsPlayer />
</div>
```

### 进度条交互优化

- **点击事件修复**：添加了 `preventDefault()` 和 `stopPropagation()` 确保网页端点击正常工作
- **扩大点击区域**：进度条容器添加了 `py-2` 增加垂直点击区域
- **Hover 效果**：进度条圆点默认隐藏，hover 时显示并变大（12px → 16px），提供精确的视觉反馈
- **精确计算**：使用 `Math.max(0, Math.min(1, ...))` 确保点击位置计算准确

### 控制按钮交互

- **Hover 背景**：所有按钮都有 `hover:bg-white/20` 圆形背景高亮效果
- **音量控制**：
  - hover 音量按钮时显示垂直滑块
  - 支持拖拽调节音量 0-100%
  - 点击按钮可快速静音/取消静音
  - 静音时显示禁音图标
- **全屏功能**：
  - 点击全屏按钮进入/退出全屏模式
  - 图标会根据当前状态切换
- **平滑过渡**：所有交互都有 `transition-all duration-200` 动画效果

### 测试页面

访问测试页面查看效果：
```
http://localhost:8003/dam/apps/creative-tools/video-clip/test
```

## 技术栈

- **React 18+**：现代 React Hooks
- **TypeScript**：完整类型支持
- **Video.js 7.x**：专业视频播放库
- **Tailwind CSS**：实用优先的 CSS 框架

## 优势

1. **现代化架构**：使用 React Hooks 和函数组件
2. **类型安全**：完整的 TypeScript 支持
3. **性能优化**：使用 useMemo 优化重渲染
4. **易于维护**：清晰的组件结构和状态管理
5. **无外部依赖**：除了 Video.js 外无其他依赖

## 扩展性

组件设计为可扩展的：

- 可以轻松修改视频列表
- 可以添加更多控制功能
- 可以自定义样式主题
- 可以集成到更大的应用中

## 注意事项

1. **Video.js 依赖**：确保项目中已安装 video.js
2. **视频格式**：建议使用 MP4 格式
3. **CORS 设置**：确保视频服务器支持跨域
4. **浏览器兼容性**：支持现代浏览器

## 与原始 HTML 的对比

| 特性 | 原始 HTML | React 组件 |
|------|-----------|------------|
| 架构 | 命令式 DOM 操作 | 声明式 React |
| 状态管理 | 全局变量 | React State |
| 事件处理 | addEventListener | React 事件处理器 |
| 样式管理 | CSS 类 | Tailwind CSS 类名 |
| 类型安全 | 无 | TypeScript |
| 可维护性 | 低 | 高 |
| 可测试性 | 低 | 高 |
| 样式复用 | 低 | 高（Tailwind 实用类） |
| 响应式设计 | 手动媒体查询 | Tailwind 响应式前缀 |

这个 React 组件完全保持了原始 HTML 版本的功能和视觉效果，同时提供了更好的开发体验和可维护性。
