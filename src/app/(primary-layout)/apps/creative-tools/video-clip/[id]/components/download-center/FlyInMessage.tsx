/*
 * @Date: 2025-07-26 15:12:50
 * @Author: miroku.yang
 * @Description: 飞入提示信息组件
 */
import React from 'react';
import { motion } from 'framer-motion';
import { exp } from 'mathjs';

const FlyInMessage: React.FC<{
  anchorRef: React.RefObject<HTMLElement>;
  onFinish: () => void;
}> = ({ anchorRef, onFinish }) => {
  const [show, setShow] = React.useState(true);
  const [start, setStart] = React.useState<{
    left: number;
    top: number;
  } | null>(null);
  const [end, setEnd] = React.useState<{ left: number; top: number } | null>(
    null
  );
  React.useEffect(() => {
    // 起点：顶部中间 message 位置
    const msg = document.querySelector(".ant-message-notice");
    let startPos = { left: window.innerWidth / 2, top: 60 };
    if (msg) {
      const rect = msg.getBoundingClientRect();
      startPos = {
        left: rect.left + rect.width / 2,
        top: rect.top + rect.height / 2,
      };
    }
    setStart(startPos);
    // 终点：按钮中心
    if (anchorRef.current) {
      const rect = anchorRef.current.getBoundingClientRect();
      setEnd({
        left: rect.left + rect.width / 2,
        top: rect.top + rect.height / 2,
      });
    }
    // 动画0.5秒后消失
    const timer = setTimeout(() => {
      setShow(false);
      setTimeout(onFinish, 300);
    }, 500);
    return () => clearTimeout(timer);
  }, [anchorRef, onFinish]);
  if (!show || !start || !end) return null;
  return (
    <motion.div
      initial={{ opacity: 1, scale: 1, left: start.left, top: start.top }}
      animate={{ opacity: 0, scale: 0.7, left: end.left-30, top: end.top-10 }}
      transition={{ duration: 0.6 }}
        style={{
          position: 'fixed',
          transform: 'translate(-50%, -50%)',
          zIndex: 9999,
          pointerEvents: 'none',
          background: '#fff',
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
          borderRadius: 8,
          display: 'flex',
          alignItems: 'center',
          padding: 0,
        }}
    >
      <span
        role="img"
        aria-label="check-circle"
        className="anticon anticon-check-circle"
        style={{color:'#52c41a'}}
      >
        <svg
          viewBox="64 64 896 896"
          focusable="false"
          data-icon="check-circle"
          width="1em"
          height="1em"
          fill="currentColor"
          aria-hidden="true"
        >
          <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"></path>
        </svg>
      </span>
      导出成片成功
    </motion.div>
  );
};

export default FlyInMessage;