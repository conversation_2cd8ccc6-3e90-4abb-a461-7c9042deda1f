/*
 * @Date: 2025-07-25 10:42:53
 * @Author: miroku.yang
 * @Description: 下载中心，先获取导出记录列表，列表里面有导出中的、导出失败的、导出成功的记录；针对导出成功的记录，提供下载链接，前端执行下载，需要把下载进度展示出来，以及下载状态
 */
import React, {
    useState,
    useEffect,
    useMemo,
    useCallback,
    useRef,
} from "react";
import {
    Popover,
    Tabs,
    Button,
    Pagination,
    Empty,
    message,
    Spin,
    Modal,
} from "antd";
import eventBus from "@/utils/eventBus";
import SinoSymbolFont from "@/components/Icon/SinoSymbolFont";
import { EVENT_NEMES } from "@/components/Mqtt/consts";
import { cn } from "@/lib/utils";
import styles from "./index.module.scss";
import { getInitialPagination } from "@/constants/table";
import { VIDEO_CLIP_EVENT_NAME } from "../../../utils";
import FlyInMessage from "./FlyInMessage";
import ListItem from "./ListItem";
import Prompt from "@/components/Prompt";
import { FileHelper } from "@/common";
import { useFingerprint } from '@/hooks';

// 假设有获取任务列表的接口
import { getExportTasks, notifyDownloadSuccess } from "./services";

const TAB_KEYS = {
    DOING: "doing",
    DONE: "done",
};

const statusMap = {
    0: { text: "待处理", color: "default" }, // WAITING
    1: { text: "处理中", color: "processing" }, // PROCESSING
    2: { text: "处理成功", color: "success" }, // SUCCESS
    3: { text: "处理失败", color: "error" }, // FAIL
    4: { text: "下载成功", color: "success" }, // DOWNLOAD
};
interface IProps {
    taskId: string;
}

const DownloadCenter = (props: IProps) => {
    const { taskId } = props;
    const [tabKey, setTabKey] = useState(TAB_KEYS.DOING);
    const [tasks, setTasks] = useState<any>([]);
    const [loading, setLoading] = useState(false);
    const INITIAL_PAGINATION = getInitialPagination({ pageSize: 20 });
    const [pagination, setPagination] = useState({ ...INITIAL_PAGINATION });
    // message提示后飞入动画控制
    const [showFlyIn, setShowFlyIn] = useState(false);
    // 飞入后的高亮状态
    const [highlight, setHighlight] = useState(false);
    const requestIdRef = useRef<number>(0);
    // 跟踪正在下载的任务ID，防止重复下载
    const downloadingTasksRef = useRef<Set<string>>(new Set());

    // Popover 控制
    const [visible, setVisible] = useState(false);
    const btnRef = useRef<HTMLButtonElement>(null);
    // 是否触发拦截提示，根据id 存储
    const [promptObj, setPromptObj] = useState({});
    // 记录已经自动下载过的任务ID，防止重复下载
    const [autoDownloadedTasks, setAutoDownloadedTasks] = useState<Set<string>>(() => {
        try {
            const saved = localStorage.getItem(`autoDownloadedTasks_${taskId}`);
            return saved ? new Set(JSON.parse(saved)) : new Set();
        } catch {
            return new Set();
        }
    });
    // 获取设备id，用于用户行为追踪和统计分析
    const { visitorId } = useFingerprint();
    const isPrompt = Object.values(promptObj).includes(true);

    // 同步已下载任务到 localStorage
    useEffect(() => {
        try {
            localStorage.setItem(`autoDownloadedTasks_${taskId}`, JSON.stringify([...autoDownloadedTasks]));
        } catch (error) {
            console.error('保存已下载任务状态失败:', error);
        }
    }, [autoDownloadedTasks, taskId]);

    // 监听MQTT，实时更新任务状态
    useEffect(() => {
        const handleTaskUpdate = (data: any) => {
            const { id, status, statusName, downloadUrl } = data;

            if (id && status === 2) {
                // 更新列表，但保持已有的下载状态（progress、speed等）
                setTasks((prev: any) =>
                    prev.map((item: any) =>
                        item.id === id
                            ? {
                                ...item,
                                status: status,
                                downloadUrl: downloadUrl,
                                statusName: statusName,
                                // 保持已有的下载进度信息，避免重置
                                ...(item.progress !== undefined && { progress: item.progress }),
                                ...(item.speed !== undefined && { speed: item.speed })
                            }
                            : item
                    )
                );
            }
        };

        const handleButtonVisibility = () => {
            // 1. 先用 antd message.success 顶部中间提示
            message.success({
                content: "导出成片成功",
                duration: 2,
                onClose: () => {
                    // 2. 关闭时触发飞入动画
                    setShowFlyIn(true);
                },
            });
        };

        // 监听任务更新
        eventBus.on(
            EVENT_NEMES.AIGC_SHORT_DRAMA_VIDEO_GENERATE_CHANGE,
            handleTaskUpdate
        );
        // 监听按钮显示状态
        eventBus.on(
            VIDEO_CLIP_EVENT_NAME.GENERATE_CREATE_SUCCESS,
            handleButtonVisibility
        );
        return () => {
            eventBus.off(
                EVENT_NEMES.AIGC_SHORT_DRAMA_VIDEO_GENERATE_CHANGE,
                handleTaskUpdate
            );
            eventBus.off(
                VIDEO_CLIP_EVENT_NAME.GENERATE_CREATE_SUCCESS,
                handleButtonVisibility
            );
            setShowFlyIn(false);
            setPromptObj({});
        };
    }, []);

    const fetchTasks = useCallback(
        async (params: {
            taskId: string | number | undefined;
            pageNo?: number;
            pageSize?: number;
            status?: number;
            isLoading?: boolean;
            statusList?: number[];
        }) => {
            try {
                const currentRequestId = ++requestIdRef.current;
                const isLoading = params?.isLoading ?? true;
                setLoading(isLoading);
                const submitPagination = {
                    pageNo: params.pageNo || pagination.current,
                    pageSize: params.pageSize || pagination.pageSize,
                };
                params.taskId = params.taskId ? Number(params.taskId) : (taskId ? Number(taskId) : undefined);
                const { data } = await getExportTasks({
                    // status: tabKey === TAB_KEYS.DOING ? 0 : 1,
                    ...submitPagination,
                    statusList: tabKey === TAB_KEYS.DOING ? [0, 1, 2] : [3, 4],
                    device:visitorId,
                    ...params,
                });
                if (currentRequestId == requestIdRef.current) {
                    setTasks(data?.result?.items || []);
                    setPagination({
                        ...pagination,
                        current: params?.pageNo || INITIAL_PAGINATION.current,
                        pageSize: params?.pageSize || INITIAL_PAGINATION.pageSize,
                        total: data?.result?.total || 0,
                    });
                }
            } catch (error) {
                console.error("获取导出任务失败", error);
            } finally {
                setLoading(false);
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [pagination, INITIAL_PAGINATION]
    );

    useEffect(() => {
        if (visible && taskId) fetchTasks({ taskId });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [visible, taskId]);

    useEffect(() => {
        if (!visible) {
            setTabKey(TAB_KEYS.DOING);
            // 注意：不清理已下载状态，保持持久化
        }
    }, [visible]);

    // 2. 下载逻辑
    const handleDownload = useCallback(async (record: any) => {
        const { downloadUrl, id, storyLineName, status } = record;

        // 检查是否已经在下载中或已下载(任务已在下载或已下载，跳过)
        if (autoDownloadedTasks.has(id)) {
            console.log(`任务 ${id} 已经下载过，跳过重复下载`);
            return;
        }

        // 检查是否正在下载中
        if (downloadingTasksRef.current.has(id)) {
            console.log(`任务 ${id} 正在下载中，跳过重复下载`);
            return;
        }

        // 检查任务是否已经有下载进度，如果有说明正在下载中
        const currentTask = tasks.find((task: any) => task.id === id);
        if (currentTask && currentTask.progress !== undefined && currentTask.progress > 0 && currentTask.progress < 100) {
            console.log(`任务 ${id} 正在下载中（有进度），跳过重复下载`);
            return;
        }

        console.log(`开始下载任务 ${id}`);

        // 立即标记为正在下载，防止重复下载
        downloadingTasksRef.current.add(id);
        setAutoDownloadedTasks(prev => new Set([...prev, id]));
        setPromptObj(prev => ({ ...prev, [record.id]: true }));
        const currentFileType = FileHelper.getType(downloadUrl);

        try {
            // 初始化进度
            setTasks((prev: any) =>
                prev.map((item: any) =>
                    item.id === id
                        ? { ...item, progress: 0, speed: "0MB/s" }
                        : item
                )
            );

            const response = await fetch(downloadUrl);
            if (!response.body) {
                downloadingTasksRef.current.delete(id);
                setTasks((prev: any) =>
                    prev.map((item: any) =>
                        item.id === id
                            ? { ...item, progress: 0, speed: "0MB/s" }
                            : item
                    )
                );
                return;
            }
            const contentLengthHeader = response.headers.get("Content-Length");
            if (!contentLengthHeader) {
                downloadingTasksRef.current.delete(id);
                setTasks((prev: any) =>
                    prev.map((item: any) =>
                        item.id === id
                            ? { ...item, progress: 0, speed: "0MB/s" }
                            : item
                    )
                );
                return;
            }
            const contentLength = +contentLengthHeader;
            const reader = response.body.getReader();
            let received = 0;
            const chunks = [];
            const startTime = Date.now(); // 记录开始时间
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                chunks.push(value);
                received += value.length;
                // 计算进度和速度
                const percent = Math.round((received / contentLength) * 100);
                const now = Date.now();

                // 计算实时速度 - 简化逻辑，每次都计算
                let speed = "0MB/s";

                // 使用总体平均速度，更稳定
                const totalTime = (now - startTime) / 1000;
                if (totalTime > 0.1 && received > 0) { // 至少100ms后开始显示速度
                    const avgSpeedMBps = received / totalTime / 1024 / 1024;
                    if (isFinite(avgSpeedMBps) && avgSpeedMBps > 0) {
                        if (avgSpeedMBps >= 1) {
                            speed = avgSpeedMBps.toFixed(2) + "MB/s";
                        } else if (avgSpeedMBps >= 0.001) {
                            const avgSpeedKBps = avgSpeedMBps * 1024;
                            speed = avgSpeedKBps.toFixed(0) + "KB/s";
                        } else {
                            speed = "0MB/s";
                        }
                    }
                }
                // 更新当前任务进度
                setTasks((prev: any) =>
                    prev.map((item: any) =>
                        item.id === id
                            ? { ...item, progress: percent, speed }
                            : item
                    )
                );
            }

            // 计算最终平均速度
            const totalTime = (Date.now() - startTime) / 1000;
            let finalSpeed = "已完成";
            if (totalTime > 0 && received > 0) {
                const avgSpeedMBps = received / totalTime / 1024 / 1024;
                if (isFinite(avgSpeedMBps) && avgSpeedMBps > 0) {
                    if (avgSpeedMBps >= 1) {
                        finalSpeed = avgSpeedMBps.toFixed(2) + "MB/s";
                    } else {
                        const avgSpeedKBps = avgSpeedMBps * 1024;
                        finalSpeed = avgSpeedKBps.toFixed(0) + "KB/s";
                    }
                }
            }

            // 下载完成
            const blob = new Blob(chunks);
            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = storyLineName + '.' + (currentFileType?.split('/')[1] || '');
            link.click();
            setTasks((prev: any) =>
                prev.map((item: any) =>
                    item.id === id
                        ? { ...item, progress: 100, speed: finalSpeed }
                        : item
                )
            );
            const { data } = await notifyDownloadSuccess({
                id: id,
                name: storyLineName,
                status,
            });
            if (data?.code === 0) {
                // 下载成功后，清除提示标记和下载状态
                setPromptObj(prev => ({ ...prev, [record.id]: false }));
                downloadingTasksRef.current.delete(id);

                // 通知后端下载成功后，前端需要将这条数据从列表中移除，如果删除后的数组为空，直接请求第一页
                setTasks((prev: any) => {
                    const newArr = prev.filter(
                        (item: any) => item.id !== id
                    );
                    // 如果删除后为空，自动请求第一页
                    if (newArr.length === 0) {
                        fetchTasks({ taskId: taskId, pageNo: 1, isLoading: false });
                    }
                    return newArr;
                });
            } else {
                // 下载失败时也要清理状态
                downloadingTasksRef.current.delete(id);
            }
        } catch (error) {
            console.error(`下载任务 ${id} 失败:`, error);
            // 下载异常时清理状态
            downloadingTasksRef.current.delete(id);
            setPromptObj(prev => ({ ...prev, [record.id]: false }));
            setTasks((prev: any) =>
                prev.map((item: any) =>
                    item.id === id
                        ? { ...item, progress: 0, speed: "下载失败" }
                        : item
                )
            );
        }
    }, [autoDownloadedTasks, taskId, fetchTasks, tasks]);

    // 表格翻页
    const handleTableChange = (page: number, pageSize: number) => {
        setPagination({
            ...pagination,
            current: pageSize !== pagination.pageSize ? 1 : page,
            pageSize,
        });
        fetchTasks({
            taskId: taskId,
            pageNo: pageSize !== pagination.pageSize ? 1 : page,
        });
    };

    const isEmpty = tasks.length === 0;

    const rightContent = useMemo(() => {
        return (
            <Spin
                spinning={loading}
                wrapperClassName={cn("h-full", styles["spin"])}
                tip="加载中..."
            >
                {loading ? null : (
                    <div className="flex-1 flex flex-col h-full py-3">
                        {/* 头部统计（如需可保留） */}
                        {!isEmpty && (
                            <div className="mb-4 text-base font-medium border-b border-[#E5E6EB] pb-2 pl-4">
                                {tabKey === TAB_KEYS.DOING
                                    ? `正在导出：${tasks.filter((t: any) => t.status === 2 && t.downloadUrl).length
                                    }/${pagination.total}`
                                    : `共 ${pagination.total} 个故事线`}
                            </div>
                        )}
                        {/* 列表内容（无表头） */}
                        <div className="flex flex-col overflow-y-auto flex-1 pr-4 pl-4">
                            {tasks.length === 0 && (
                                <Empty
                                    style={{ paddingTop: 126, textAlign: "center" }}
                                    imageStyle={{ display: "flex", justifyContent: "center" }}
                                    image="//static.creativebooster.com/ad-web/dam/no-data-light.svg"
                                    description={null}
                                >
                                    <div>暂无导出记录</div>
                                </Empty>
                            )}
                            {tasks.map((record: any) => (
                                <ListItem
                                    key={record.id}
                                    record={record}
                                    onDownload={handleDownload}
                                    tabKey={tabKey}
                                    TAB_KEYS={TAB_KEYS}
                                    hasAutoDownloaded={autoDownloadedTasks.has(record.id)}
                                />
                            ))}
                        </div>
                        {/* 分页 */}
                        {!isEmpty && (
                            <div className="flex justify-end mt-4">
                                <Pagination
                                    size="small"
                                    current={pagination.current}
                                    pageSize={pagination.pageSize}
                                    total={pagination.total}
                                    onChange={handleTableChange}
                                    showSizeChanger={false}
                                    className="!mb-0"
                                />
                            </div>
                        )}
                    </div>
                )}
            </Spin>
        );
    }, [tabKey, tasks, pagination.total, pagination, handleDownload, isEmpty, autoDownloadedTasks]);

    const handleTabChange = (key: string) => {
        setTabKey(key);
        const newPagination = { ...pagination, current: 1 };
        setPagination(newPagination);
        fetchTasks({
            taskId: taskId,
            pageNo: newPagination.current,
            statusList: key === TAB_KEYS.DOING ? [0, 1, 2] : [3, 4],
        });
    };

    // Popover内容，左右布局，右侧上下列表
    const popoverContent = (
        <div className="w-[500px] h-[480px] flex flex-col">
            {/* 左侧tab */}
            <Tabs
                tabPosition="left"
                activeKey={tabKey}
                onChange={handleTabChange}
                className={cn("h-full", styles["tabs"])}
                items={[
                    {
                        key: TAB_KEYS.DOING,
                        label: (
                            <span
                                className={cn("text-[#4E5969]", {
                                    "text-[#165DFF]": tabKey === TAB_KEYS.DOING,
                                })}
                            >
                                进行中
                            </span>
                        ),
                        children: rightContent,
                    },
                    {
                        key: TAB_KEYS.DONE,
                        label: (
                            <span
                                className={cn("text-[#4E5969]", {
                                    "text-[#165DFF]": tabKey === TAB_KEYS.DONE,
                                })}
                            >
                                已完成
                            </span>
                        ),
                        children: rightContent,
                    },
                ]}
            />
        </div>
    );

    const handleExportCenterClick = () => {
        setVisible(!visible);
        setHighlight(false);
    };

    // 导出按钮 + Popover
    return (
        <>
            {showFlyIn && (
                <FlyInMessage
                    anchorRef={btnRef}
                    onFinish={() => {
                        setShowFlyIn(false);
                        setHighlight(true);
                        // 手动打开弹窗
                        setVisible(true);
                    }}
                />
            )}
            <Popover
                content={visible ? popoverContent : null}
                title={null}
                trigger="click"
                open={visible}
                onOpenChange={setVisible}
                placement="bottomRight"
                rootClassName={cn("z-50", styles["popover"])}
                arrow={false}
            >
                <Button
                    ref={btnRef}
                    variant="outlined"
                    icon={<SinoSymbolFont type="icon-报告记录2" style={{ fontSize: "15px" }} />}
                    className={cn(
                        "flex mr-4 bg-white border border-[#E5E6EB] text-[#4E5969] hover:bg-[#F2F3F5] hover:border-[#165DFF] hover:text-[#165DFF] active:bg-[#E5E6EB] active:border-[#165DFF] active:text-[#165DFF] rounded-[6px] px-4 py-2 font-medium cursor-pointer",
                        styles["action-btn"],
                        {
                            ["!text-[#165DFF] !border-[#165DFF]"]: visible || highlight,
                        }
                    )}
                    onClick={handleExportCenterClick}
                >
                    导出中心
                </Button>
            </Popover>
            <Prompt
                when={isPrompt}
                message={({ pathname }) => {
                    return new Promise((resolve) => {
                        Modal.confirm({
                            title: "提示",
                            content: <div>
                                <div>退出后正在下载的任务可能会被取消，确定吗?</div>
                                <div>取消后的任务可以在浏览器下载记录里面点击继续下载。</div>
                            </div>,
                            okText: "确定",
                            cancelText: "取消",
                            onOk: () => {
                                setPromptObj((prev) => ({}));
                                // 因为浏览器的关闭才会触发浏览器的下载取消（一般不常发生，所以这里通知后端将任务标记为下载完成，让任务到已完成中去）
                                // 关闭页签不会取消下载任务，然而无法区分浏览器是关闭页签还是关闭浏览器，所以这样让下载任务状态正常流转
                                // todo: 需要后端将标记下载完成接口改成数组，一次性都进行标记
                                // Object.keys(promptObj).forEach((key) => {
                                //     notifyDownloadSuccess({
                                //         id: key,
                                //         name: "",
                                //         status: 5, // 假设5表示下载完成
                                //     });
                                // });
                                resolve(true); // 允许跳转
                            },
                            onCancel: () => {
                                resolve(false); // 阻止跳转
                            },
                        });
                    });
                }}
            />
        </>
    );
};

export default DownloadCenter;
