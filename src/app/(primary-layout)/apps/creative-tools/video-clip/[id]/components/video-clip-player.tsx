import React, { useRef, useEffect, useState, useCallback } from "react";
import { message } from "antd";
import { CirclePlay, CirclePause, Volume2, VolumeX } from "lucide-react";

interface VideoClip {
  id: number;
  episodeUrl: string;
  startTimeSeconds: number;
  endTimeSeconds: number;
  script: string;
  episode: number;
}

interface VideoClipPlayerProps {
  clips: VideoClip[];
  width?: number;
  height?: number;
  onError?: (error: string) => void;
  autoPlay?: boolean;
}

export const VideoClipPlayer: React.FC<VideoClipPlayerProps> = ({
  clips,
  width = 800,
  height = 450,
  onError,
  autoPlay = false,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const animationFrameRef = useRef<number>();

  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentClipIndex, setCurrentClipIndex] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [totalDuration, setTotalDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [progress, setProgress] = useState(0);
  const [videoAspectRatio, setVideoAspectRatio] = useState(16 / 9);
  const [isDragging, setIsDragging] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isVideoReady, setIsVideoReady] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);

  // 计算总时长
  useEffect(() => {
    const total = clips.reduce((acc, clip) => {
      return acc + (clip.endTimeSeconds - clip.startTimeSeconds);
    }, 0);
    setTotalDuration(total);
  }, [clips]);

  // 视频加载和播放逻辑
  const loadVideo = useCallback(
    async (clipIndex: number) => {
      if (!videoRef.current || !clips[clipIndex]) return;

      const video = videoRef.current;
      const clip = clips[clipIndex];

      setIsLoading(true);

      try {
        video.src = clip.episodeUrl;
        video.currentTime = clip.startTimeSeconds;

        await new Promise((resolve, reject) => {
          const handleCanPlay = () => {
            // 获取视频的宽高比
            if (video.videoWidth && video.videoHeight) {
              setVideoAspectRatio(video.videoWidth / video.videoHeight);
            }

            video.removeEventListener("canplay", handleCanPlay);
            video.removeEventListener("error", handleError);
            setIsVideoReady(true);
            resolve(void 0);
          };

          const handleError = () => {
            video.removeEventListener("canplay", handleCanPlay);
            video.removeEventListener("error", handleError);
            setIsVideoReady(false);
            reject(new Error("视频加载失败"));
          };

          video.addEventListener("canplay", handleCanPlay);
          video.addEventListener("error", handleError);
          video.load();
        });

        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        const errorMsg = `切片 ${clipIndex + 1} 加载失败`;
        console.error(errorMsg, error);
        onError?.(errorMsg);
        message.error(errorMsg);
      }
    },
    [clips, onError]
  );

  // Canvas绘制逻辑
  const drawVideoFrame = useCallback(() => {
    if (!canvasRef.current || !videoRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    const video = videoRef.current;

    if (!ctx || video.readyState < 2) return;

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 计算视频在canvas中的显示尺寸，保持原比例
    const canvasAspectRatio = canvas.width / canvas.height;
    let drawWidth = canvas.width;
    let drawHeight = canvas.height;
    let offsetX = 0;
    let offsetY = 0;

    if (videoAspectRatio > canvasAspectRatio) {
      // 视频更宽，以宽度为准
      drawHeight = canvas.width / videoAspectRatio;
      offsetY = (canvas.height - drawHeight) / 2;
    } else {
      // 视频更高，以高度为准
      drawWidth = canvas.height * videoAspectRatio;
      offsetX = (canvas.width - drawWidth) / 2;
    }

    // 填充黑色背景
    ctx.fillStyle = "black";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制视频帧，保持比例
    ctx.drawImage(video, offsetX, offsetY, drawWidth, drawHeight);
  }, [clips, currentClipIndex, videoAspectRatio]);

  // 播放循环
  const playLoop = useCallback(() => {
    if (!videoRef.current || !isPlaying) return;

    const video = videoRef.current;
    const currentClip = clips[currentClipIndex];

    if (!currentClip) return;

    // 检查当前切片是否播放完毕
    if (video.currentTime >= currentClip.endTimeSeconds) {
      // 切换到下一个切片
      if (currentClipIndex < clips.length - 1) {
        setCurrentClipIndex((prev) => prev + 1);
        return;
      } else {
        // 所有切片播放完毕
        setIsPlaying(false);
        setCurrentClipIndex(0);
        setCurrentTime(0);
        setProgress(0);
        return;
      }
    }

    // 确保视频在正确的时间范围内播放
    if (video.currentTime < currentClip.startTimeSeconds) {
      video.currentTime = currentClip.startTimeSeconds;
    }

    // 更新时间和进度（拖动时不更新）
    if (!isDragging) {
      let elapsed = 0;
      for (let i = 0; i < currentClipIndex; i++) {
        elapsed += clips[i].endTimeSeconds - clips[i].startTimeSeconds;
      }
      elapsed += Math.max(0, video.currentTime - currentClip.startTimeSeconds);

      setCurrentTime(elapsed);
      setProgress(totalDuration > 0 ? (elapsed / totalDuration) * 100 : 0);
    }

    // 绘制视频帧
    drawVideoFrame();

    // 继续播放循环
    animationFrameRef.current = requestAnimationFrame(playLoop);
  }, [
    isPlaying,
    currentClipIndex,
    clips,
    totalDuration,
    drawVideoFrame,
    isDragging,
  ]);

  // 当切片索引改变时加载新视频
  useEffect(() => {
    if (clips.length > 0) {
      setIsVideoReady(false); // 重置视频准备状态
      loadVideo(currentClipIndex);
    }
  }, [currentClipIndex, loadVideo, clips]);

  // 自动播放逻辑
  useEffect(() => {
    if (
      autoPlay &&
      clips.length > 0 &&
      !isLoading &&
      !isPlaying &&
      isVideoReady &&
      !hasUserInteracted
    ) {
      // 视频已准备好，立即开始播放
      setIsPlaying(true);
    }
  }, [
    autoPlay,
    clips.length,
    isLoading,
    isPlaying,
    isVideoReady,
    hasUserInteracted,
  ]);

  // 播放状态改变时的处理
  useEffect(() => {
    if (isPlaying && videoRef.current) {
      videoRef.current.play().catch(console.error);
      playLoop();
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (videoRef.current) {
        videoRef.current.pause();
      }
      // 暂停时更新一次进度，确保显示正确的当前时间
      if (!isDragging && videoRef.current && clips.length > 0) {
        const video = videoRef.current;
        const currentClip = clips[currentClipIndex];

        if (currentClip) {
          let elapsed = 0;
          for (let i = 0; i < currentClipIndex; i++) {
            elapsed += clips[i].endTimeSeconds - clips[i].startTimeSeconds;
          }
          elapsed += Math.max(
            0,
            video.currentTime - currentClip.startTimeSeconds
          );

          setCurrentTime(elapsed);
          setProgress(totalDuration > 0 ? (elapsed / totalDuration) * 100 : 0);
        }
      }
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isPlaying, playLoop, isDragging, clips, currentClipIndex, totalDuration]);

  // 音量控制
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.volume = isMuted ? 0 : volume;
    }
  }, [volume, isMuted]);

  const handlePlayPause = () => {
    setHasUserInteracted(true); // 标记用户已手动交互
    setIsPlaying(!isPlaying);
  };

  const handleVolumeToggle = () => {
    setIsMuted(!isMuted);
  };

  // 进度条拖动处理
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!totalDuration) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const progressPercent = Math.max(
      0,
      Math.min(100, (clickX / rect.width) * 100)
    );
    const targetTime = (progressPercent / 100) * totalDuration;

    // 立即更新UI状态，避免卡顿感
    setProgress(progressPercent);
    setCurrentTime(targetTime);
    setIsDragging(true);

    seekToTime(targetTime);

    // 短暂延迟后恢复拖动状态
    setTimeout(() => {
      setIsDragging(false);
    }, 100);
  };

  const seekToTime = useCallback(
    (targetTime: number) => {
      if (!videoRef.current || !clips.length) return;

      let accumulatedTime = 0;
      let targetClipIndex = 0;
      let timeInClip = 0;

      for (let i = 0; i < clips.length; i++) {
        const clipDuration =
          clips[i].endTimeSeconds - clips[i].startTimeSeconds;
        if (accumulatedTime + clipDuration >= targetTime) {
          targetClipIndex = i;
          timeInClip = targetTime - accumulatedTime;
          break;
        }
        accumulatedTime += clipDuration;
      }

      const targetVideoTime =
        clips[targetClipIndex].startTimeSeconds + timeInClip;

      // 判断是否需要切换视频源
      if (clips[targetClipIndex].episodeUrl !== clips[currentClipIndex].episodeUrl) {
        setCurrentClipIndex(targetClipIndex);
        // 等待 loadVideo 后再设置 currentTime
        const setTimeWhenReady = () => {
          if (videoRef.current) {
            if (videoRef.current.readyState >= 2) {
              videoRef.current.currentTime = targetVideoTime;
            } else {
              const handleCanPlay = () => {
                if (videoRef.current) {
                  videoRef.current.currentTime = targetVideoTime;
                  videoRef.current.removeEventListener('canplay', handleCanPlay);
                }
              };
              videoRef.current.addEventListener('canplay', handleCanPlay);
              setTimeout(() => {
                if (videoRef.current && videoRef.current.readyState >= 2) {
                  videoRef.current.currentTime = targetVideoTime;
                  videoRef.current.removeEventListener('canplay', handleCanPlay);
                }
              }, 100);
            }
          }
        };
        setTimeout(setTimeWhenReady, 10);
      } else {
        // 同一个视频，直接跳转
        if (videoRef.current && videoRef.current.readyState >= 2) {
          videoRef.current.currentTime = targetVideoTime;
        }
        setCurrentClipIndex(targetClipIndex); // 只更新索引，不重新加载
      }
    },
    [clips, currentClipIndex]
  );

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  if (clips.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        暂无切片数据
      </div>
    );
  }

  return (
    <div className="relative bg-black rounded-lg overflow-hidden shadow-lg">
      {/* Canvas画布 */}
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className="w-full h-auto block"
        style={{ maxWidth: "100%", height: "auto" }}
      />

      {/* 隐藏的视频元素 */}
      <video
        ref={videoRef}
        style={{ display: "none" }}
        crossOrigin="anonymous"
        preload="metadata"
      />

      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80 z-20">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <div className="text-sm">加载中...</div>
          </div>
        </div>
      )}

      {/* 控制栏 */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent p-4">
        <div className="flex items-center gap-3 text-white flex-col">
          <div
            className="flex-1 relative cursor-pointer group py-3 w-full"
            onClick={handleProgressClick}
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
          >
            {/* 进度条容器 - 不使用overflow-hidden */}
            <div className="relative h-1.5">
              {/* 进度条背景 */}
              <div className="absolute inset-0 bg-white/30 rounded-full" />

              {/* 进度填充 */}
              <div
                className="absolute left-0 top-0 h-full bg-blue-500 rounded-full transition-all duration-100 ease-out"
                style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
              />

              {/* 悬停时的增强效果 */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div className="w-full h-full bg-white/10 rounded-full" />
              </div>
            </div>

            {/* 进度圆点 - 独立于进度条容器 */}
            <div
              className="absolute top-1/2 w-3 h-3 bg-white rounded-full shadow-lg transition-all duration-150 ease-out z-20"
              style={{
                left: `${Math.max(0, Math.min(100, progress))}%`,
                transform: "translate(-50%, -50%)",
              }}
            />

            {/* 切片分割线 */}
            {clips.length > 1 && (
              <div className="absolute top-1/2 left-0 right-0 pointer-events-none -translate-y-1/2">
                {clips.slice(1).map((_, index) => {
                  // 计算每个切片分割点的位置
                  let accumulatedDuration = 0;
                  for (let i = 0; i <= index; i++) {
                    accumulatedDuration +=
                      clips[i].endTimeSeconds - clips[i].startTimeSeconds;
                  }
                  const position = (accumulatedDuration / totalDuration) * 100;

                  return (
                    <div
                      key={index}
                      className="absolute top-1/2 -translate-y-1/2 w-0.5 h-2 bg-white/60 group-hover:bg-white/80 transition-colors duration-200 rounded-full z-10"
                      style={{ left: `${position}%` }}
                    />
                  );
                })}
              </div>
            )}
          </div>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-4">
              {isPlaying ? (
                <CirclePause
                  className="text-white cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={handlePlayPause}
                  size={24}
                />
              ) : (
                <CirclePlay
                  className="text-white cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={handlePlayPause}
                  size={24}
                />
              )}

              <span className="text-sm text-white font-mono min-w-[80px]">
                {formatTime(currentTime)} / {formatTime(totalDuration)}
              </span>
            </div>

            {isMuted ? (
              <VolumeX
                className="text-white"
                size={20}
                onClick={handleVolumeToggle}
              />
            ) : (
              <Volume2
                className="text-white"
                size={20}
                onClick={handleVolumeToggle}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
