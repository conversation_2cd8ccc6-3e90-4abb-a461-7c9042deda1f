/*
 * @Date: 2025-07-26 16:11:00
 * @Author: miroku.yang
 * @Description:
 */
import React, { useEffect } from "react";
import GeneratingProgress from "./GeneratingProgress";
import SinoSymbolFont from "@/components/Icon/SinoSymbolFont";
import { Progress, Button, Tooltip } from "antd";
import dayjs from "dayjs";

interface ListItemProps {
  record: any;
  onDownload: (item: any) => void;
  tabKey: string;
  TAB_KEYS: { DOING: string; DONE: string };
  hasAutoDownloaded: boolean;
}
const ListItem: React.FC<ListItemProps> = (props) => {
  const { record, onDownload, tabKey, TAB_KEYS, hasAutoDownloaded } = props;

  useEffect(() => {
    // 只有当任务状态为成功(2)、有下载链接、未自动下载过、且没有下载进度时才触发下载
    if (
      record.status === 2 &&
      record.downloadUrl &&
      !hasAutoDownloaded &&
      record.progress === undefined // 确保没有下载进度，避免重复下载
    ) {
      console.log(`ListItem 触发下载: 任务ID ${record.id}`);
      onDownload(record);
    }
  }, [record.status, record.downloadUrl, record.id, hasAutoDownloaded, record.progress, onDownload]);

  return (
    <>
      {tabKey === TAB_KEYS.DOING ? (
        <div className="flex items-center py-3 border-b border-[#E5E6EB]">
          {/* 图标区，可自定义 */}
          <div className="flex-shrink-0 rounded flex items-center justify-center mr-4">
            <SinoSymbolFont type="icon-VIDEO" style={{ fontSize: "30px" }} />
          </div>
          {/* 右侧内容区 */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 text-sm text-[#1D2129] mb-2 line-clamp-1">
              {record.storyLineName}
            </div>
            {/* 进度/状态/速度 */}
            <>
              {/* 0待处理,1处理中 */}
              {[0, 1].includes(record.status) && <GeneratingProgress />}
              {/* 处理中 */}
              {[2].includes(record.status) && (
                <Progress
                  percent={record.progress}
                  size="small"
                  showInfo={false}
                  status={record.status === 3 ? "exception" : "normal"}
                  className="h-[6px] !leading-[6px]"
                />
              )}
            </>
            <div className="flex justify-between items-center">
              <div className="flex text-xs text-[#86909C] mt-2 line-clamp-1">
                <span className="max-w-[94px] line-clamp-1">{record.name || "-"}</span>
                <span className="ml-2">{dayjs(record.createTime).format("YYYY-MM-DD HH:mm:ss")}</span>
              </div>
              {/* 操作区 */}
              <div className="w-[80px] text-right">
                {[0, 1, 2].includes(record.status) && <>
                  {[0, 1].includes(record.status) && <span className="text-[#165DFF] text-xs px-0">生成中...</span>}
                  {[2].includes(record.status) && ('progress' in record) && (
                    <span className="text-[#165DFF] text-xs px-0 mt-1.5">
                      {record.speed || "5.5MB/s"}
                    </span>
                  )}
                </>}
              </div>
            </div>
          </div>
        </div>
      ) : null}
      {tabKey === TAB_KEYS.DONE ? (
        <div className="flex items-center py-3 border-b border-[#E5E6EB]">
          {/* 图标区 */}
          <div className="flex-shrink-0 rounded flex items-center justify-center mr-4">
            <SinoSymbolFont type="icon-VIDEO" style={{ fontSize: "30px" }} />
          </div>
          {/* 右侧内容区 */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 text-sm text-[#1D2129] mt-2 line-clamp-1 mb-2">
              {record.storyLineName}
            </div>
            <div className="flex text-xs text-[#86909C] mb-2 line-clamp-1">
              <span className="max-w-[94px] line-clamp-1">{record.name || "-"}</span>
              <span className="ml-2">{dayjs(record.createTime).format("YYYY-MM-DD HH:mm:ss")}</span>
            </div>
          </div>
          <Tooltip
            title="请前往浏览器下载目录中查看"
            trigger="hover"
          >
            <Button
              type="link"
              className="text-[#165DFF] hover:text-[#3b82f6] active:text-[#1d4ed8] !bg-[#E7EEFF] !rounded-full w-[28px] h-[28px]"
            >
              <SinoSymbolFont
                type="icon-shoucangjia"
                style={{ fontSize: "14px", color: "#165DFF" }}
              />
            </Button>
          </Tooltip>
        </div>
      ) : null}
    </>
  );
};

export default ListItem;
