import { useState } from "react";
import { Table } from "antd";
import { cn } from "@/lib/utils";
import Image from "next/image";
import styles from "../styles/storyline.module.scss";
import dayjs from "dayjs";
import { formatSecondsToTime, VIDEO_CLIP_EVENT_NAME } from "../../utils";
import { downloadSubtitle, getGenerateCreate } from "../services";
import eventBus from "@/utils/eventBus";
import { VideoPreviewModal } from "./video-preview-modal";
import { CirclePlay } from "lucide-react";
import { useFingerprint } from '@/hooks';

interface StoryLineTableProps {
  loading: boolean;
  storyLineId: string;
  dataSource: any[];
  selectedStorylineIds: string[];
  onSelectChange: (selectedKeys: React.Key[]) => void;
  containerHeight: number;
  onDelete: (storyLineId: string) => void;
  shortName: string;
}

const columns = (
  onDelete: (id: string) => void,
  storyLineId: string,
  fetchGenerateCreate: ({
    id,
    name,
    device,
  }: {
    id: string;
    name: string;
    device: string;
  }) => Promise<void>,
  onPreview: (record: any) => void,
  handleDownloadSubtitle: (id: string, storyName: string) => Promise<void>,
  shortName: string,
  visitorId: string,
) => [
    {
      title: "故事线名称",
      dataIndex: "storyName",
      width: "80%",
      render: (_: any, record: any) => (
        <div className="flex gap-3 my-2 items-center">
          {record?.previewUrl ? (
            <div
              className="w-[100px] h-[150px] flex-shrink-0 relative cursor-pointer group"
              onClick={() => onPreview(record)}
            >
              <Image
                src={record?.previewUrl}
                alt="storyline"
                width={100}
                height={150}
                className="w-full h-full object-cover"
                unoptimized={true}
              />
              {/* 播放按钮覆盖层 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <CirclePlay
                  className="text-white drop-shadow-lg transition-transform duration-200 ease-in-out group-hover:scale-125"
                  size={28}
                />
              </div>
              {/* 视频时长显示 */}
              <div className="absolute top-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1 py-0.5 rounded">
                {record.duration.toFixed(1) + "s" || "未知"}
              </div>
            </div>
          ) : (
            <div className="w-[100px] h-[150px] bg-gray-200 flex items-center justify-center text-gray-400 flex-shrink-0">
              无图片
            </div>
          )}

          <div className="flex flex-col gap-2 flex-1">
            <p className="font-bold">{record.storyName}</p>
            <p className="text-gray-500 line-clamp-2">{record.summary}</p>
            <p>
              <span className="text-gray-500">时长：</span>
              {formatSecondsToTime(record.duration) || "未知"}
            </p>
            <p>
              <span className="text-gray-500">切片数量：</span>
              {record.storyLineDetailCount}
            </p>
            <p>
              <span className="text-gray-500">更新时间：</span>
              {record.updateTime
                ? dayjs(record.updateTime).format("YYYY-MM-DD HH:mm:ss")
                : "未知"}
            </p>
          </div>
        </div>
      ),
    },
    {
      title: "操作",
      key: "action",
      render: (_: any, record: any) => (
        <div className="flex flex-col gap-2 my-2">
          <a
            onClick={() => {
              window.open(
                `/dam/apps/creative-tools/video-clip/editor/${storyLineId}/${record.id}`
              );
            }}
          >
            编辑切片
          </a>
          <a
            onClick={() =>
              fetchGenerateCreate({
                id: record.id,
                name: record.version
                  ? `${shortName}${record.version}`
                  : shortName,
                device: visitorId,
              })
            }
          >
            导出成片
          </a>
          <a
            onClick={() => {
              handleDownloadSubtitle(record.id, record.storyName);
            }}
          >
            下载中文字幕
          </a>
          <a className="cursor-pointer" onClick={() => onDelete(record.id)}>
            删除
          </a>
        </div>
      ),
    },
  ];

export const StoryLineTable: React.FC<StoryLineTableProps> = ({
  loading,
  dataSource,
  selectedStorylineIds,
  onSelectChange,
  containerHeight,
  onDelete,
  storyLineId,
  shortName,
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [currentStoryline, setCurrentStoryline] = useState<any>(null);
  // 获取设备id，用于用户行为追踪和统计分析
  const { visitorId } = useFingerprint();
  // 处理字幕下载
  const handleDownloadSubtitle = async (id: string, storyName: string) => {
    try {
      const response = await downloadSubtitle(id);

      // 直接下载后端返回的二进制流
      const blob = new Blob([response.data], {
        type: "text/plain;charset=utf-8",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${storyName || "字幕"}.srt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      console.log("字幕文件下载完成");
    } catch (error) {
      console.error("下载字幕失败:", error);
    }
  };

  const fetchGenerateCreate = async ({
    id,
    name,
    device,
  }: {
    id: string;
    name: string;
    device: string;
  }) => {
    try {
      const { data } = await getGenerateCreate({ id, name, device });
      if (data.code === 0) {
        eventBus.emit(VIDEO_CLIP_EVENT_NAME.GENERATE_CREATE_SUCCESS, {});
      }
    } catch (error) {
      console.error(error, "创建导出任务失败");
    }
  };

  const handlePreview = (record: any) => {
    setCurrentStoryline(record);
    setPreviewVisible(true);
  };

  const handleClosePreview = () => {
    setPreviewVisible(false);
    setCurrentStoryline(null);
  };

  return (
    <>
      <Table
        columns={columns(
          onDelete,
          storyLineId,
          fetchGenerateCreate,
          handlePreview,
          handleDownloadSubtitle,
          shortName,
          visitorId,
        )}
        dataSource={dataSource}
        pagination={false}
        bordered
        loading={{
          wrapperClassName: cn({ [styles["table-loading"]]: loading }),
          spinning: loading,
        }}
        rowKey="id"
        rowSelection={{
          type: "checkbox" as const,
          selectedRowKeys: selectedStorylineIds,
          onChange: (selectedRowKeys: React.Key[]) =>
            onSelectChange(selectedRowKeys),
          getCheckboxProps: (record: any) => ({ name: record.id.toString() }),
          columnWidth: 32,
        }}
        rowClassName={(record) =>
          cn({
            [styles.selectedStoryline]: selectedStorylineIds.includes(
              record.id
            ),
          })
        }
        rowHoverable={false}
        scroll={{ y: containerHeight }}
        className={styles["story-line-table"]}
      />

      {/* 视频预览弹窗 */}
      {previewVisible && currentStoryline && (
        <VideoPreviewModal
          visible={previewVisible}
          onClose={handleClosePreview}
          storyline={currentStoryline}
        />
      )}
    </>
  );
};
