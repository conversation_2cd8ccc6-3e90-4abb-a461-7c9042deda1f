import React, { useState, useEffect } from "react";
import { Modal, message, Spin } from "antd";
import { VideoClipPlayer } from "./video-clip-player";
import { getStoryLineDetailInfo } from "../services";
import { VideojsPlayer } from "./videojs-player";

interface VideoPreviewModalProps {
  visible: boolean;
  onClose: () => void;
  storyline: {
    id: string;
    storyName: string;
    [key: string]: any;
  };
}

export function VideoPreviewModal({
  visible,
  onClose,
  storyline,
}: VideoPreviewModalProps) {
  const [clips, setClips] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取切片详情数据
  useEffect(() => {
    if (visible && storyline?.id) {
      fetchClipsData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, storyline?.id]);

  const fetchClipsData = async () => {
    try {
      setLoading(true);
      const response = await getStoryLineDetailInfo({ id: storyline.id });

      if (
        response.data.success &&
        response.data.result?.storyLineDetailInfoVOList
      ) {
        // 只获取启用的切片并按顺序排序
        const enabledClips = response.data.result.storyLineDetailInfoVOList
          .filter((clip: any) => clip.enable === true)
          .sort((a: any, b: any) => a.sort - b.sort);

        setClips(enabledClips);
      } else {
        message.error("获取切片数据失败");
        setClips([]);
      }
    } catch (error) {
      console.error("获取切片数据失败:", error);
      message.error("获取切片数据失败");
      setClips([]);
    } finally {
      setLoading(false);
    }
  };

  const handleError = (error: string) => {
    console.error("播放器错误:", error);
  };

  const handleClose = () => {
    setClips([]);
    onClose();
  };

  // 转换后端数据格式为播放器需要的格式
  const transformClipsToVideos = (clips: any[]) => {
    return clips.map((clip) => ({
      src: clip.episodeUrl,
      duration: clip.endTimeSeconds - clip.startTimeSeconds,
      endTimeSeconds: clip.endTimeSeconds,
      startTimeSeconds: clip.startTimeSeconds,
      poster:clip.videoClipPreviewUrl,
    }));
  };

  // 渲染模态框内容
  const renderModalContent = () => {
    if (loading) {
      return (
        <div className="relative" style={{ width: "850px", height: "478px" }}>
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <div className="flex flex-col items-center text-white">
              <Spin size="large" />
              <div className="mt-2">加载中...</div>
            </div>
          </div>
        </div>
      );
    }

    if (clips.length > 0) {
      const videos = transformClipsToVideos(clips);
      return (
        <div className="relative" style={{ width: "850px", height: "478px" }}>
          {/* <VideoClipPlayer
            clips={clips.map((clip) => ({
              id: clip.id,
              episodeUrl: clip.episodeUrl,
              startTimeSeconds: clip.startTimeSeconds,
              endTimeSeconds: clip.endTimeSeconds,
              script: clip.script,
              episode: clip.episode,
            }))}
            width={850}
            height={478}
            onError={handleError}
            autoPlay={true}
          /> */}
          <VideojsPlayer videos={videos} useTimeSecond={true} />
        </div>
      );
    }

    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 text-gray-500 text-base">
        <div className="text-center">
          <div className="text-4xl mb-4">📹</div>
          <div>暂无可播放的切片</div>
        </div>
      </div>
    );
  };

  return (
    <Modal
      title={`预览: ${storyline?.storyName || "未知故事线"}`}
      open={visible}
      onCancel={handleClose}
      onClose={handleClose}
      footer={null}
      width={900}
      height={500}
      centered
      keyboard={true}
    >
      {renderModalContent()}
    </Modal>
  );
}
