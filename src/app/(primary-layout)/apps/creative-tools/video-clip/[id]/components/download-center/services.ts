/*
 * @Date: 2025-07-25 11:10:02
 * @Author: miroku.yang
 * @Description: 获取导出任务列表
 */
import requestHelper from "@/utils/requestHelper";

/**
 * 获取导出任务列表
 * @param data
 * @returns
 */
export function getExportTasks(data = {}) {
    return requestHelper.post(
        "v4/demand/short/drama/highlights/story/line/video/generate/list",
        data
    );
}

/**
 * 通知下载成功
 * @param storyLineId
 * @returns
 */
export function notifyDownloadSuccess(data = {}) {
    return requestHelper.post(
        "v4/demand/short/drama/highlights/story/line/video/download/complete",
        data
    );
}

