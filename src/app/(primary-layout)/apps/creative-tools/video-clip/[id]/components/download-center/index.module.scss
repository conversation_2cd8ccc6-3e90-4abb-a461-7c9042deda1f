.tabs {
  :global {
    .ant-tabs-content,
    .ant-tabs-tabpane {
      height: 100% !important;
    }
    .ant-tabs-tabpane{
      padding-left: 0 !important;
    }
  }
}
.action-btn {
  :global {
    .ant-btn-icon {
      display: flex;
    }
  }
}

.popover {
  :global {
    .ant-popover-inner {
      border: 1px solid #d9d9d9 !important;
      box-shadow: none !important;
      border-radius: 8px !important;
      padding: 0 !important;
    }
    .ant-tabs-nav-list {
      padding: 12px;
    }
    .ant-tabs-tab-active {
      background: #EDF5FE!important;
      border-radius: 6px;
    }
  }
}

.spin {
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}
