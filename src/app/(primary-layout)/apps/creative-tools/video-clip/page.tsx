"use client";

import { useRef } from "react";
import { cn } from "@/lib/utils";
import TaskList from "./components/task-list";
import UploadForm from "./components/upload-form";
import styles from "./styles/page.module.scss";

export default function VideoClip() {
  const taskListRef = useRef<any>(null);

  // 当保存操作完成后，刷新任务列表以显示最新数据
  const handleSaveCallback = async () => {
    taskListRef.current?.refresh?.();
  };

  return (
    <div className="xxl:h-full py-10 px-5 2xl:px-10 flex flex-col xxl:flex-row gap-6">
      <UploadForm onSaveCallback={handleSaveCallback} />
      <div
        className={cn(
          "pb-20 xxl:h-full xxl:flex-[2] xxl:overflow-hidden rounded-lg border border-solid border-gray-200",
          styles["task-list-wrapper"]
        )}
      >
        <TaskList ref={taskListRef} />
      </div>
    </div>
  );
}
