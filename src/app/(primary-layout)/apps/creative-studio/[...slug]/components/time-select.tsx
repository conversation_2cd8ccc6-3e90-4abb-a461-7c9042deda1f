"use client";

export default function TimeSelect({ value, onChange }: any) {
  const handleChange = (v: string) => {
    onChange?.(v);
  };
  return (
    <div className="grid grid-cols-3 gap-3">
      <button
        type="button"
        onClick={() => handleChange("recommended")}
        className={`h-10 flex items-center justify-center rounded-sm border transition-colors ${
          value === "recommended"
            ? "border-[#165dff] text-[#165dff] bg-[rgba(22,93,255,0.1)]"
            : "border-gray-300 text-gray-600 hover:border-[#165dff] hover:text-[#165dff]"
        }`}
      >
        推荐
      </button>
      <button
        type="button"
        onClick={() => handleChange("15s")}
        className={`h-10 flex items-center justify-center rounded-sm border transition-colors ${
          value === "15s"
            ? "border-[#165dff] text-[#165dff] bg-[rgba(22,93,255,0.1)]"
            : "border-gray-300 text-gray-600 hover:border-[#165dff] hover:text-[#165dff]"
        }`}
      >
        15 秒
      </button>
      <button
        type="button"
        onClick={() => handleChange("30s")}
        className={`h-10 flex items-center justify-center rounded-sm border transition-colors ${
          value === "30s"
            ? "border-[#165dff] text-[#165dff] bg-[rgba(22,93,255,0.1)]"
            : "border-gray-300 text-gray-600 hover:border-[#165dff] hover:text-[#165dff]"
        }`}
      >
        30 秒
      </button>
    </div>
  );
}
