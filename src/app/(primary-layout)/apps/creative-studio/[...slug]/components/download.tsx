"use client";
import { useEffect } from "react";
import { Form, Input } from "antd";

const FormItem = Form.Item;

export default function SaveToMySpace({ initialValues, form }: any) {
  const count = initialValues?.items?.length || 0;
  const name = initialValues?.items?.[0]?.name || '';

  useEffect(() => {
    if (initialValues && form) {
      form.setFieldsValue(initialValues);
    }
  }, []);

  return (
    <div>
      <div className="text-[#1D2129]">
        <p className="mb-1">{`您将下载${name}等${count}个视频！`}</p>
        <p>下载过程可能较长，请耐心等待。</p>
      </div>
      <FormItem name="items" noStyle>
        <Input type="hidden" />
      </FormItem>
    </div>
  );
}
