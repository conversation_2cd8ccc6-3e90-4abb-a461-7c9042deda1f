/**
 * 过渡页
 * @returns 
 */

// export default function LoadingPage() {
//   return (
//     <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center overflow-hidden relative">
//       {/* Background particles */}
//       <div className="absolute inset-0">
//         {[...Array(20)].map((_, i) => (
//           <div
//             key={i}
//             className="absolute w-1 h-1 bg-white rounded-full opacity-30 animate-pulse"
//             style={{
//               left: `${Math.random() * 100}%`,
//               top: `${Math.random() * 100}%`,
//               animationDelay: `${Math.random() * 3}s`,
//               animationDuration: `${2 + Math.random() * 2}s`,
//             }}
//           />
//         ))}
//       </div>

//       {/* Main container */}
//       <div className="relative z-10 text-center">
//         {/* Lightbulb */}
//         <div className="absolute -top-20 -left-16 animate-bounce">
//           <div className="relative">
//             <div className="w-8 h-12 bg-gradient-to-b from-yellow-200 to-yellow-400 rounded-t-full animate-pulse" />
//             <div className="w-8 h-3 bg-gray-300 rounded-sm mt-1" />
//             <div className="w-6 h-1 bg-gray-400 mx-auto mt-1" />
//             {/* Glow effect */}
//             <div className="absolute -inset-2 bg-yellow-300 rounded-full opacity-20 animate-ping" />
//           </div>
//         </div>

//         {/* Screw */}
//         <div className="absolute -top-16 -right-20 animate-spin-slow">
//           <div className="w-6 h-6 bg-gray-400 rounded-full relative">
//             <div className="absolute inset-1 bg-gray-500 rounded-full" />
//             <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-0.5 bg-gray-600" />
//             <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-4 bg-gray-600" />
//           </div>
//         </div>

//         {/* Book */}
//         <div className="absolute -bottom-16 -left-20 animate-float">
//           <div className="relative">
//             <div className="w-10 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-r-sm shadow-lg transform rotate-12" />
//             <div className="absolute top-0 left-0 w-10 h-12 bg-gradient-to-r from-red-400 to-red-500 rounded-r-sm transform -rotate-3" />
//             <div className="absolute top-2 left-1 w-6 h-0.5 bg-red-200 opacity-60" />
//             <div className="absolute top-4 left-1 w-5 h-0.5 bg-red-200 opacity-60" />
//             <div className="absolute top-6 left-1 w-7 h-0.5 bg-red-200 opacity-60" />
//           </div>
//         </div>

//         {/* Gear */}
//         <div className="absolute -bottom-12 -right-16 animate-spin-reverse">
//           <div className="w-8 h-8 relative">
//             <div className="absolute inset-0 bg-gray-500 rounded-full" />
//             <div className="absolute inset-2 bg-gray-600 rounded-full" />
//             {/* Gear teeth */}
//             {[...Array(8)].map((_, i) => (
//               <div
//                 key={i}
//                 className="absolute w-1 h-2 bg-gray-500"
//                 style={{
//                   left: "50%",
//                   top: "-2px",
//                   transformOrigin: "50% 18px",
//                   transform: `translateX(-50%) rotate(${i * 45}deg)`,
//                 }}
//               />
//             ))}
//           </div>
//         </div>

//         {/* Pencil */}
//         <div className="absolute top-8 -right-24 animate-wiggle">
//           <div className="relative transform rotate-45">
//             <div className="w-2 h-16 bg-gradient-to-b from-yellow-400 to-yellow-500" />
//             <div className="w-2 h-2 bg-pink-300 -mt-0.5" />
//             <div className="w-1.5 h-3 bg-gray-700 mx-auto -mt-0.5" />
//           </div>
//         </div>

//         {/* Main loading text */}
//         <div className="mb-8">
//           <h1 className="text-4xl md:text-6xl font-bold text-white mb-4 animate-pulse">视频正在生成</h1>
//           <div className="flex justify-center space-x-2">
//             {[...Array(3)].map((_, i) => (
//               <div
//                 key={i}
//                 className="w-3 h-3 bg-white rounded-full animate-bounce"
//                 style={{ animationDelay: `${i * 0.2}s` }}
//               />
//             ))}
//           </div>
//         </div>

//         {/* Progress bar */}
//         <div className="w-64 h-2 bg-gray-700 rounded-full mx-auto overflow-hidden">
//           <div className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-progress" />
//         </div>

//         {/* Status text */}
//         <p className="text-gray-300 mt-4 animate-fade-in-out">正在整理思路和灵感...</p>
//       </div>

//       {/* Floating elements */}
//       <div className="absolute top-1/4 left-1/4 animate-float-delayed">
//         <div className="w-4 h-4 bg-blue-400 rounded-full opacity-60" />
//       </div>
//       <div className="absolute top-3/4 right-1/4 animate-float-delayed-2">
//         <div className="w-3 h-3 bg-green-400 rounded-full opacity-60" />
//       </div>
//       <div className="absolute top-1/2 left-1/6 animate-float-delayed-3">
//         <div className="w-2 h-2 bg-yellow-400 rounded-full opacity-60" />
//       </div>
//     </div>
//   )
// }

export default function LoadingPage() {
  return (
    <div className="min-h-full min-w-full bg-white flex items-center justify-center overflow-hidden relative">
      {/* Background particles */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-gray-400 rounded-full opacity-30 animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>

      {/* Main container */}
      <div className="relative z-10 text-center">
        {/* Lightbulb */}
        <div className="absolute -top-20 -left-16 animate-bounce">
          <div className="relative">
            <div className="w-8 h-12 bg-gradient-to-b from-yellow-200 to-yellow-400 rounded-t-full animate-pulse" />
            <div className="w-8 h-3 bg-gray-300 rounded-sm mt-1" />
            <div className="w-6 h-1 bg-gray-400 mx-auto mt-1" />
            {/* Glow effect */}
            <div className="absolute -inset-2 bg-yellow-300 rounded-full opacity-20 animate-ping" />
          </div>
        </div>

        {/* Screw */}
        <div className="absolute -top-16 -right-20 animate-spin-slow">
          <div className="w-6 h-6 bg-gray-400 rounded-full relative">
            <div className="absolute inset-1 bg-gray-500 rounded-full" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-0.5 bg-gray-600" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-4 bg-gray-600" />
          </div>
        </div>

        {/* Book */}
        <div className="absolute -bottom-16 -left-20 animate-float">
          <div className="relative">
            <div className="w-10 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-r-sm shadow-lg transform rotate-12" />
            <div className="absolute top-0 left-0 w-10 h-12 bg-gradient-to-r from-red-400 to-red-500 rounded-r-sm transform -rotate-3" />
            <div className="absolute top-2 left-1 w-6 h-0.5 bg-red-200 opacity-60" />
            <div className="absolute top-4 left-1 w-5 h-0.5 bg-red-200 opacity-60" />
            <div className="absolute top-6 left-1 w-7 h-0.5 bg-red-200 opacity-60" />
          </div>
        </div>

        {/* Gear */}
        <div className="absolute -bottom-12 -right-16 animate-spin-reverse">
          <div className="w-8 h-8 relative">
            <div className="absolute inset-0 bg-gray-500 rounded-full" />
            <div className="absolute inset-2 bg-gray-600 rounded-full" />
            {/* Gear teeth */}
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-2 bg-gray-500"
                style={{
                  left: "50%",
                  top: "-2px",
                  transformOrigin: "50% 18px",
                  transform: `translateX(-50%) rotate(${i * 45}deg)`,
                }}
              />
            ))}
          </div>
        </div>

        {/* Pencil */}
        <div className="absolute top-8 -right-24 animate-wiggle">
          <div className="relative transform rotate-45">
            <div className="w-2 h-16 bg-gradient-to-b from-yellow-400 to-yellow-500" />
            <div className="w-2 h-2 bg-pink-300 -mt-0.5" />
            <div className="w-1.5 h-3 bg-gray-700 mx-auto -mt-0.5" />
          </div>
        </div>

        {/* Main loading text */}
        <div className="mb-8">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-4 animate-pulse">视频正在生成</h1>
          <div className="flex justify-center space-x-2">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="w-3 h-3 bg-blue-500 rounded-full animate-bounce"
                style={{ animationDelay: `${i * 0.2}s` }}
              />
            ))}
          </div>
        </div>

        {/* Progress bar */}
        <div className="w-64 h-2 bg-gray-200 rounded-full mx-auto overflow-hidden">
          <div className="h-full bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-progress" />
        </div>

        {/* Status text */}
        <p className="text-gray-600 mt-4 animate-fade-in-out">正在整理思路和灵感...</p>
      </div>

      {/* Floating elements */}
      <div className="absolute top-1/4 left-1/4 animate-float-delayed">
        <div className="w-4 h-4 bg-blue-500 rounded-full opacity-60" />
      </div>
      <div className="absolute top-3/4 right-1/4 animate-float-delayed-2">
        <div className="w-3 h-3 bg-green-500 rounded-full opacity-60" />
      </div>
      <div className="absolute top-1/2 left-1/6 animate-float-delayed-3">
        <div className="w-2 h-2 bg-yellow-500 rounded-full opacity-60" />
      </div>
    </div>
  )
}