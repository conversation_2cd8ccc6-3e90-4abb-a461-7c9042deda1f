"use client";
import { useEffect } from "react";
import { Form, Input } from "antd";

const FormItem = Form.Item;

export default function SaveToMySpace({ initialValues, form }: any) {
  useEffect(() => {
    if (initialValues && form) {
      form.setFieldsValue(initialValues);
    }
  }, []);

  return (
    <div>
      <div className="text-[#1D2129]">
        <p className="mb-1">耗时可能较长，请您耐心等待!</p>
        <p>完成后，您可以在"素材 - 我的空间"中查看。</p>
      </div>
      <FormItem name="items" noStyle>
        <Input type="hidden" />
      </FormItem>
    </div>
  );
}
