"use client";

import type React from "react";
// import Image from "next/image";
import { useState, useRef, useCallback, useEffect, useMemo } from "react";
import { Play, MoreVertical, Download, Save } from "lucide-react";
import { Dropdown, Checkbox, Tag } from "antd";
import { cn } from "@/lib/utils";
import { IconSvg } from "@/components";

type VideoCardType = {
  id: string;
  name: string;
  url: string;
  status: string;
  aigc_video_type_name_list?: any;
  poster?: string;
};
interface VideoCardProps {
  checked?: boolean;
  video: VideoCardType;
  onChecked: (item: VideoCardType) => void;
  onDownload: (item: VideoCardType[]) => void;
  onSave: (item: VideoCardType[]) => void;
  onPreview?: (list: any, index: number) => void;
  items: any;
  idx: number;
}

const VideoCard: React.FC<VideoCardProps> = ({
  video,
  checked,
  onChecked,
  onDownload,
  onSave,
  onPreview,
  items,
  idx,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [videoError, setVideoError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  // 预加载视频
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.load();
    }
  }, [video.url]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);

    if (videoRef.current) {
      // 确保视频已经加载
      if (videoRef.current.readyState >= 3) {
        // HAVE_FUTURE_DATA
        videoRef.current
          .play()
          .then(() => {
            setIsVideoPlaying(true);
          })
          .catch((error) => {
            console.log("Video play failed:", error);
            setIsVideoPlaying(false);
          });
      } else {
        // 如果视频还没加载完，等待加载完成后播放
        const handleCanPlay = () => {
          if (isHovered && videoRef.current) {
            videoRef.current
              .play()
              .then(() => {
                setIsVideoPlaying(true);
              })
              .catch((error) => {
                console.log("Video play failed after load:", error);
                setIsVideoPlaying(false);
              });
          }
          videoRef.current?.removeEventListener("canplay", handleCanPlay);
        };
        videoRef.current.addEventListener("canplay", handleCanPlay);
      }
    }
  }, [isHovered, isVideoLoaded]);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    setIsVideoPlaying(false);

    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.currentTime = 0;
    }
  }, []);

  const handleVideoLoad = useCallback(() => {
    setIsVideoLoaded(true);
    setVideoError(false);
  }, []);

  const handleVideoError = useCallback(
    (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
      setIsVideoLoaded(false);
      setVideoError(true);
    },
    []
  );

  const handleVideoCanPlay = useCallback(() => {
    setIsVideoLoaded(true);
  }, []);

  /**
   * 点击菜单
   * @param param0
   * @returns
   */
  const handleMenuClick = ({ key, domEvent }: any) => {
    domEvent.preventDefault();
    domEvent.stopPropagation();
    if (key === "1") {
      onDownload([video]);
      return;
    }
    onSave([video]);
  };

  const dropdownItems = useMemo(() => {
    return [
      {
        key: "1",
        label: "下载",
        icon: <Download className="w-4 h-4" />,
      },
      {
        key: "2",
        label: "保存至素材库",
        icon: <Save className="w-4 h-4" />,
      },
    ];
  }, []);

  return (
    <div
      className={cn(
        "bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-solid border-transparent cursor-pointer",
        {
          "border-[#165EFF]": checked,
        }
      )}
      onClick={() => onChecked(video)}
    >
      {/* Video/Image Container */}
      <div
        className="relative h-[400px] overflow-hidden bg-gray-200 group"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* 封面图：未hover时显示，hover时如果视频未加载成功也显示 */}
        {/* eslint-disable-next-line @next/next/no-img-element */}
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          src={video.poster}
          alt={video.name}
          className="absolute inset-0 w-full h-full object-contain transition-opacity duration-500"
          draggable={false}
        />
        {/* 视频：hover时显示，未hover时透明 */}
        {/* {isHovered && (
          <video
            ref={videoRef}
            className={`mx-auto !object-cover absolute inset-0 h-full max-w-full max-h-full transition-opacity duration-500 ${
              isVideoLoaded && !videoError ? "opacity-100" : "opacity-0"
            }`}
            loop
            playsInline
            preload="auto"
            onLoadedData={handleVideoLoad}
            onCanPlay={handleVideoCanPlay}
            onError={handleVideoError}
            onPlay={() => setIsVideoPlaying(true)}
            onPause={() => setIsVideoPlaying(false)}
          >
            <source src={video.url} type="video/mp4" />
            您的浏览器不支持视频播放。
          </video>
        )} */}

        {/* Video Error Fallback */}
        {/* {videoError && isHovered && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <Play className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">视频加载失败</p>
            </div>
          </div>
        )} */}

        {/* Loading Indicator */}
        {/* {isHovered && !isVideoLoaded && !videoError && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/20">
            <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
        )} */}

        {/* Video Playing Indicator */}
        {/* {isVideoPlaying && (
          <div className="absolute top-3 left-3">
            <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              <span>播放中</span>
            </div>
          </div>
        )} */}
        <span
          className={cn(
            "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-10 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity"
          )}
          onClick={(e: any) => {
            onPreview?.(items, idx);
            e.stopPropagation();
          }}
        >
          <IconSvg type={"iconbofang2"} style={{ color: "#111", fontSize: 40 }} />
        </span>
        <div
          className={cn(
            "absolute top-3 right-3 flex items-center justify-center transition-all duration-300",
            {
              "opacity-100 translate-x-0": checked || isHovered, // 选中或hover时都显示
              "opacity-0 translate-x-4": !checked && !isHovered, // 都不是时隐藏
            }
          )}
        >
          <Checkbox checked={checked} />
        </div>
      </div>

      {/* Card Footer */}
      <div className="p-3 bg-white">
        <div className="flex items-center justify-between gap-2">
          <div className="flex-1 overflow-hidden">
            <h3
              className="single-line-ellipsis !text-gray-400"
              title={video.name}
            >
              {video.name}
            </h3>
            {video?.aigc_video_type_name_list?.length ? (
              <h3
                className="single-line-ellipsis !text-gray-600"
                title={video?.aigc_video_type_name_list?.join(",")}
              >
                <Tag>{video?.aigc_video_type_name_list?.join(",")}</Tag>
              </h3>
            ) : null}
          </div>
          <div className="flex-none">
            <Dropdown
              trigger={["click"]}
              placement="bottomCenter"
              arrow={false}
              menu={{ items: dropdownItems, onClick: handleMenuClick }}
            >
              <div
                className="hover:bg-[#ebecf2] transition-all duration-200 rounded-md p-1.5 cursor-pointer"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
              >
                <MoreVertical className="w-4 h-4 text-gray-600" />
              </div>
            </Dropdown>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoCard;
