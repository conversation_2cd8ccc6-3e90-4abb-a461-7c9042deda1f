import React from 'react';
import classNames from 'classnames';
import mime from 'mime';
import { LoadingOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons';
import { Spin, Popconfirm } from 'antd';
import { IconSvg } from '@/components';
import { FileHelper } from '@/common';
import styles from './index.module.scss';

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

interface IProps {
  percent?: number;
  url: string;
  width?: number | string;
  height?: number | string;
  style?: React.CSSProperties;
  className?: string;
  wrapClassName?: string;
  eyeClick?: (e: any) => void;
  remove?: (e: any) => void;
  showRemove?: boolean;
  showDownload?: boolean;
  size?: string;
  cover?: string;
  renderPercent?: (...args: any) => any;
  renderAanalysis?: (...args: any) => any;
  // downloadIconList?: any; // 按照下方的resource 结合传人的判断
  file?: any;
  showPopconfirm?: boolean; // 删除时是否有二次确认
  onDownload: (file: any) => any;
}
const CardItem = (props: any) => {
  const {
    percent,
    url,
    width,
    height,
    style,
    className,
    wrapClassName,
    eyeClick,
    remove,
    showRemove = false,
    showDownload = false,
    size = 'lg',
    cover,
    renderPercent,
    renderAanalysis,
    // downloadIconList,
    file,
    showPopconfirm,
    onDownload,
  } = props;

  const transferUrl = (url: string) => {
    if (!url) {
      return '';
    }
    let mimeType = FileHelper.getType(url);
    const originMimeType = FileHelper.getType(url);
    if (!mimeType) {
      return url;
    }
    mimeType = mimeType.replace(/\?x-oss.*/, '').split('/')[0];
    /**
     * image 大类:png、jpg、jpeg、mimeType 都是
     * 1. 图片类型无法按照ext 扩张名来，扩展的太多了
     */
    // 接口中的ext
    const fileExt = file?.ext || '';
    // 通过mime获取的ext
    const mimeExt = mime.getExtension(originMimeType || '');
    if (mimeType.includes('image')) {
      if (
        fileExt?.includes('psd') ||
        mimeExt?.includes('psd') ||
        originMimeType?.includes('vnd.adobe.photoshop')
      ) {
        return 'iconPSD';
      } else {
        return 'image';
      }
    } else if (mimeType.includes('text')) {
      // text
      if (
        fileExt?.includes('txt') ||
        mimeExt?.includes('txt') ||
        originMimeType?.includes('text/plain')
      ) {
        return 'iconTXT';
      } else {
        // 判断不出就走其他
        return 'iconqitawenjiangeshi';
      }
    } else if (mimeType.includes('application')) {
      // docx、pptx、pdf、zip
      if (
        fileExt?.includes('docx') ||
        mimeExt?.includes('docx') ||
        originMimeType?.includes('officedocument.wordprocessingml.document')
      ) {
        return 'icon-word';
      } else if (
        fileExt?.includes('pptx') ||
        mimeExt?.includes('pptx') ||
        originMimeType?.includes('officedocument.presentationml.presentation')
      ) {
        return 'iconPPT';
      } else if (
        fileExt?.includes('pdf') ||
        mimeExt?.includes('pdf') ||
        originMimeType?.includes('application/pdf')
      ) {
        return 'icon-pdf';
      } else if (
        fileExt?.includes('zip') ||
        mimeExt?.includes('zip') ||
        originMimeType?.includes('application/zip')
      ) {
        return 'iconrar';
      } else if (
        fileExt?.includes('xlsx') ||
        mimeExt?.includes('xlsx') ||
        originMimeType?.includes('.sheet')
      ) {
        return 'iconExcel';
      } else {
        // 判断不出就走其他
        return 'iconqitawenjiangeshi';
      }
    } else if (mimeType.includes('video')) {
      return 'image';
    } else if (mimeType.includes('audio')) {
      return 'iconyinpin';
    } else {
      return 'iconqitawenjiangeshi';
    }
  };

  let localMimeType: any = FileHelper.getType(url?.replace?.(/\?x-oss.*/, ''));
  if (localMimeType) {
    localMimeType = localMimeType.split('/')[0] || '';
  }

  const resource: any = transferUrl(url);

  const renderContainer = () => {
    if (typeof percent === 'number') {
      if (percent < 100) {
        if (renderPercent) {
          return renderPercent(percent);
        }
        return (
          <div
            style={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'rgba(0, 0, 0, 0.65)',
            }}
          >
            <Spin indicator={antIcon} />
            <span style={{ marginTop: 6 }}>{`${Math.ceil(percent)}%`}</span>
          </div>
        );
      }

      // 图片、视频才有封面图，音频没有，前半句判断直接return 了
      if ((!cover || !url) && ['image', 'video'].includes(localMimeType)) {
        if (renderAanalysis) {
          return renderAanalysis();
        }
        return (
          <div
            style={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'rgba(0, 0, 0, 0.65)',
            }}
          >
            <Spin indicator={antIcon} />
            <span style={{ marginTop: 6 }}>解析中...</span>
          </div>
        );
      }
    }
    return (
      <>
        <span
          className={classNames(styles['mask'], {
            [styles['look-img-resource-icon']]: resource?.includes('icon'),
          })}
        >
          {resource === 'image' ? (
            <img src={localMimeType === 'video' ? cover : url} />
          ) : (
            <IconSvg type={resource || ''} />
          )}
        </span>
        <span className={styles['actions-wrap']}>
          {resource === 'image' && (
            <span className={styles['look-img-actions']}>
              {React.cloneElement(<EyeOutlined style={{ color: '#fff' }} />, {
                onClick: eyeClick,
              })}
            </span>
          )}
          {showDownload && (
            <span className={styles['look-img-actions']}>
              <span
                onClick={() => {
                  onDownload?.(file);
                }}
              >
                <IconSvg type={'icon-download'} style={{ fontSize: 15, color: '#fff' }} />
              </span>
            </span>
          )}
          {showRemove && remove && (
            <span className={styles['look-img-actions']}>
              {showPopconfirm ? (
                <Popconfirm
                  placement="top"
                  onConfirm={(e) => {
                    remove(e);
                    e?.stopPropagation();
                  }}
                  onCancel={(e) => {
                    e?.stopPropagation();
                  }}
                  title="确定删除吗？"
                  okText="确定"
                  cancelText="取消"
                  zIndex={10001}
                >
                  <span
                    className={'shanchu'}
                    onClick={(e) => {
                      e?.stopPropagation();
                    }}
                  >
                    <DeleteOutlined style={{ color: '#fff' }} />
                  </span>
                </Popconfirm>
              ) : (
                React.cloneElement(<DeleteOutlined style={{ color: '#fff' }} />, {
                  onClick: remove,
                })
              )}
            </span>
          )}
        </span>
      </>
    );
  };

  return (
    <div className={classNames(styles['look-img-component'], wrapClassName)}>
      <div className={styles['look-img-component_wrap']}>
        <div
          style={{
            width,
            height,
            ...style,
          }}
          className={classNames(styles['bg-img'], className)}
        >
          {renderContainer()}
        </div>
      </div>
    </div>
  );
};

export default CardItem;
