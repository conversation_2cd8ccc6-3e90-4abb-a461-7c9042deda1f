.look-img-component {
  .look-img-component_wrap {
    position: relative;
    display: flex;
    flex-wrap: wrap;
  }
  .bg-img {
    position: relative;
    width: 60px;
    height: 60px;
    padding: 4px;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    cursor: pointer;

    &:hover .mask::before {
      opacity: 1;
    }
    &:hover .look-img-actions {
      opacity: 1;
    }

    .anticon svg {
      font-size: 12px;
    }
  }
  .mask {
    position: relative;
    display: block;
    width: 100%;
    height: 100%;

    &:before {
      position: absolute;
      z-index: 1;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.2);
      opacity: 0;
      transition: all 0.3s;
      content: '';
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .actions-wrap {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    width: 100%;
    height: 100%;
    padding: 0 10%;
    overflow: hidden;
  }
  .look-img-actions {
    position: relative;
    z-index: 10;
    opacity: 0;
    transition: all 0.3s;
    .anticon-eye,
    .anticon-delete {
      color: rgba(0, 0, 0, 0.85);
    }
  }
  .look-img-resource-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 50px;
    :global {
      .icon {
        font-size: 26px;
      }
    }
  }
}
