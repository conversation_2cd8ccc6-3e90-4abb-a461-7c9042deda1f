/**
 * 无聊补充
 */
import { useImperativeHandle, forwardRef, useState, useEffect } from "react";
import { message } from "antd";
import { FileHelper } from "@/common";
import IconSvg from "@/components/Icon/SinoSymbolFont";
import AliyunOSSUpload from "@/components/AliOSSUploadV2";
import useCBPreview from "@/components/useCBPreview";
import CardItem from "./card-item";
import { cn } from "@/lib/utils";

import styles from "./index.module.scss";

function Upload(props: any, ref: any) {
  const [fileList, setFileList] = useState<any[]>([]);

  const { show, render } = useCBPreview();

  useEffect(()=>{
    if('value' in props){ 
      setFileList(props.value || [])
    }
  },[props, props.value])

  useImperativeHandle(ref, () => ({
    fileList,
  }));

  const disabled = props?.disabled || false;
  if (disabled) {
    // 仅查看时，文件列表为空不展示
    return null;
  }

  return (
    <div className={cn('grid grid-cols-4 gap-1',styles.wrapper)}>
      <AliyunOSSUpload
        uploadButtonReverse
        showUploadList={false}
        limit={props.limit}
        getTokenParams={{ isPub: false }}
        multiple
        {...props}
        accept=".jpg,.jpeg,.png,.wmv,.asf,.asx,.rm,.rmvb,.mp4,.3gp,.mov,.m4v,.avi,.dat,.mkv,.flv,.vob"
        renderCustomFileList={({ remove, fileList }: any) => {
          return (
            <>
              {fileList.map((item: any, index: number) => {
                const fileInfo: any =
                  item?.originData ||
                  item?.imageFileInfo ||
                  item?.fileInfo ||
                  item ||
                  {};
                const poster: string =
                  fileInfo?.previewInfo?.resizeImageUrl || item.poster || "";
                const url: string = fileInfo?.url || "";
                return (
                  <CardItem
                    key={item.uid}
                    percent={item.percent}
                    url={url}
                    cover={poster}
                    width={100}
                    height={100}
                    showRemove={!disabled}
                    remove={(e: any) => {
                      if (disabled) return;
                      e.stopPropagation();
                      remove?.(item.uid);
                    }}
                    eyeClick={(e: any) => {
                      e.stopPropagation();
                      if (url) {
                        show({
                          current: index,
                          views: [
                            {
                              uuid: fileInfo.uuid,
                              name: fileInfo.name || "",
                              mimeType:
                                fileInfo.mimeType || FileHelper.getType(url),
                              src: url,
                              poster,
                            },
                          ],
                        });
                      }
                    }}
                    renderPercent={(p: any) => (
                      <div
                        style={{
                          height: "100%",
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          justifyContent: "center",
                          fontSize: "18px",
                          transform: "scale(0.83, 0.83)",
                          color: "rgba(0, 0, 0, 0.65)",
                        }}
                      >
                        {`${Math.ceil(p)}%`}
                      </div>
                    )}
                    renderAanalysis={() => (
                      <div
                        style={{
                          height: "100%",
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          justifyContent: "center",
                          fontSize: "18px",
                          transform: "scale(0.83, 0.83)",
                          color: "rgba(0, 0, 0, 0.65)",
                        }}
                      >
                        <div>解析中...</div>
                      </div>
                    )}
                    file={item}
                    showPopconfirm={false}
                    className={styles["look-img"]}
                  />
                );
              })}
            </>
          );
        }}
        beforeUpload={(file: any) => {
          const fileSize = 1024 * 1024 * 500;
          const isLt = file.size < fileSize;
          if (!isLt) {
            message.destroy();
            message.warning("文件大小不能大于100MB");
            return false;
          }
          const typeList = [
            ".jpg",
            ".jpeg",
            ".png",
            ".wmv",
            ".asf",
            ".asx",
            ".rm",
            ".rmvb",
            ".mp4",
            ".3gp",
            ".mov",
            ".m4v",
            ".avi",
            ".dat",
            ".mkv",
            ".flv",
            ".vob",
          ];
          const suffix = file?.name?.split(".")?.pop?.() || "";
          if (!typeList.includes(`.${suffix}`)) {
            message.destroy();
            message.error("此类型的文件禁止上传");
            return false;
          }
          return true;
        }}
        onProgress={(list: any) => setFileList(list)}
      >
        {!disabled ? (
          <div className={styles["upload-icon"]}>
            <IconSvg type="icon-quzhizuo" />
            <span style={{ fontSize: 14 }}>图片/视频</span>
          </div>
        ) : null}
      </AliyunOSSUpload>
      {render()}
    </div>
  );
}

export default forwardRef(Upload);
