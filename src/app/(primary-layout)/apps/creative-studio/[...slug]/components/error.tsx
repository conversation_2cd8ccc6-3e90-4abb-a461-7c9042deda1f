"use client";

import { Button } from "antd";

export default function ErrorPage({ onBack }: any) {
  return (
    <div className="min-h-full bg-white flex items-center justify-center overflow-hidden relative">
      {/* Background particles - glitchy effect */}
      <div className="absolute inset-0">
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-red-400 rounded-full opacity-40 animate-ping"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${1 + Math.random() * 1}s`,
            }}
          />
        ))}
      </div>

      {/* Main container */}
      <div className="relative z-10 text-center">
        {/* Broken Lightbulb */}
        <div className="absolute -top-20 -left-16 animate-shake">
          <div className="relative">
            <div className="w-8 h-12 bg-gradient-to-b from-gray-300 to-gray-400 rounded-t-full opacity-60" />
            <div className="w-8 h-3 bg-gray-400 rounded-sm mt-1" />
            <div className="w-6 h-1 bg-gray-500 mx-auto mt-1" />
            {/* Crack lines */}
            <div className="absolute top-2 left-1 w-6 h-0.5 bg-red-500 transform rotate-45" />
            <div className="absolute top-4 left-2 w-4 h-0.5 bg-red-500 transform -rotate-45" />
            {/* No glow effect for broken bulb */}
          </div>
        </div>

        {/* Loose Screw */}
        <div className="absolute -top-16 -right-20 animate-wobble">
          <div className="w-6 h-6 bg-gray-300 rounded-full relative opacity-70">
            <div className="absolute inset-1 bg-gray-400 rounded-full" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-0.5 bg-gray-600 rotate-12" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-0.5 h-4 bg-gray-600" />
          </div>
        </div>

        {/* Closed/Damaged Book */}
        <div className="absolute -bottom-16 -left-20 animate-tilt">
          <div className="relative">
            <div className="w-10 h-12 bg-gradient-to-r from-red-300 to-red-400 rounded-r-sm shadow-lg transform rotate-12 opacity-70" />
            <div className="absolute top-0 left-0 w-10 h-12 bg-gradient-to-r from-red-200 to-red-300 rounded-r-sm transform -rotate-3" />
            {/* X mark on book */}
            <div className="absolute top-4 left-2 w-6 h-0.5 bg-red-600 transform rotate-45" />
            <div className="absolute top-4 left-2 w-6 h-0.5 bg-red-600 transform -rotate-45" />
          </div>
        </div>

        {/* Jammed Gear */}
        <div className="absolute -bottom-12 -right-16 animate-stutter">
          <div className="w-8 h-8 relative opacity-60">
            <div className="absolute inset-0 bg-red-400 rounded-full" />
            <div className="absolute inset-2 bg-red-500 rounded-full" />
            {/* Gear teeth */}
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-2 bg-red-400"
                style={{
                  left: "50%",
                  top: "-2px",
                  transformOrigin: "50% 18px",
                  transform: `translateX(-50%) rotate(${i * 45}deg)`,
                }}
              />
            ))}
            {/* Warning symbol */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-xs">
              !
            </div>
          </div>
        </div>

        {/* Broken Pencil */}
        <div className="absolute top-8 -right-24 animate-fall">
          <div className="relative transform rotate-45">
            <div className="w-2 h-8 bg-gradient-to-b from-yellow-300 to-yellow-400" />
            <div className="w-2 h-8 bg-gradient-to-b from-yellow-400 to-yellow-500 mt-2" />
            <div className="w-2 h-2 bg-pink-200 -mt-0.5 opacity-60" />
            <div className="w-1.5 h-1 bg-gray-600 mx-auto -mt-0.5" />
            {/* Break line */}
            <div className="absolute top-8 left-0 w-2 h-0.5 bg-red-500" />
          </div>
        </div>

        {/* Main error content */}
        <div className="mb-8">
          <div className="text-6xl mb-4 animate-pulse text-red-500">⚠️</div>
          <h1 className="text-4xl md:text-6xl font-bold text-red-600 mb-4">
            生成失败
          </h1>
          <div className="flex justify-center space-x-2 mb-4">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="w-3 h-3 bg-red-400 rounded-full animate-pulse opacity-60"
                style={{ animationDelay: `${i * 0.3}s` }}
              />
            ))}
          </div>
          <p className="text-gray-600 mb-6">视频生成过程中遇到了问题</p>
        </div>

        {/* Retry button */}
        <div className="space-y-4">
          <Button type="primary" size="large" onClick={() => onBack?.()}>
            重新生成
          </Button>
        </div>
      </div>

      {/* Floating error elements */}
      <div className="absolute top-1/4 left-1/4 animate-float-delayed">
        <div className="w-4 h-4 bg-red-400 rounded-full opacity-40" />
      </div>
      <div className="absolute top-3/4 right-1/4 animate-float-delayed-2">
        <div className="w-3 h-3 bg-orange-400 rounded-full opacity-40" />
      </div>
      <div className="absolute top-1/2 left-1/6 animate-float-delayed-3">
        <div className="w-2 h-2 bg-yellow-400 rounded-full opacity-40" />
      </div>
    </div>
  );
}
