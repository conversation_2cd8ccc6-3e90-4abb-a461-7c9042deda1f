"use client";

import type React from "react";
import { AlertCircle } from "lucide-react";
import { Button } from "antd";

type VideoCardFailedType = {
  id: string;
  name: string;
  url: string;
  status: string;
};
interface VideoCardProps {
  video: VideoCardFailedType;
}

const VideoCardFailed: React.FC<VideoCardProps> = ({ video }) => {
  return (
    <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-solid border-transparent cursor-pointer">
      {/* Video/Image Container */}
      <div className="`relative aspect-[3/4] bg-red-50 border-red-200 flex flex-col items-center justify-center p-6`">
        <div className="mb-4">
          <AlertCircle className="w-8 h-8 text-red-500" />
        </div>

        {/* Error Title */}
        <h3 className={`text-lg font-semibold text-red-700 mb-2 text-center`}>
          生成失败
        </h3>

        {/* Error Message */}
        <p className="text-sm text-gray-600 text-center mb-4 leading-relaxed">
          AI 模型生成过程中出现错误
        </p>
        <div className="mt-2">
          <Button type="primary" onClick={() => window.location.reload()}>
            刷新
          </Button>
        </div>
      </div>

      {/* Card Footer */}
      <div className="p-3 bg-white">
        <div className="flex items-center justify-between gap-2">
          <div className="flex-1 overflow-hidden">
            <h3 className="font-medium single-line-ellipsis" title={video.name}>
              {video.name || "-"}
            </h3>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoCardFailed;
