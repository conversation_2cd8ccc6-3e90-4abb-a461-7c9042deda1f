/**
 * 提示框
 */
import type React from "react";

interface AlertProps {
  type: "warning" | "info" | "success" | "error";
  icon?: React.ReactNode;
  message: string;
}

const Alert: React.FC<AlertProps> = ({ type, icon, message }) => {
  const getAlertClasses = () => {
    switch (type) {
      case "warning":
        return "bg-amber-50 border border-amber-200 rounded-lg p-2 flex items-center gap-3";
      case "info":
        return "bg-blue-50 border border-blue-200 rounded-lg p-2 flex items-center gap-3";
      case "success":
        return "bg-green-50 border border-green-200 rounded-lg p-2 flex items-center gap-3";
      case "error":
        return "bg-red-50 border border-red-200 rounded-lg p-2 flex items-center gap-3";
      default:
        return "bg-amber-50 border border-amber-200 rounded-lg p-2 flex items-center gap-3";
    }
  };

  const getIconClasses = () => {
    switch (type) {
      case "warning":
        return "text-amber-600 flex-shrink-0";
      case "info":
        return "text-blue-600 flex-shrink-0";
      case "success":
        return "text-green-600 flex-shrink-0";
      case "error":
        return "text-red-600 flex-shrink-0";
      default:
        return "text-amber-600 flex-shrink-0";
    }
  };

  const getTextClasses = () => {
    switch (type) {
      case "warning":
        return "text-amber-800 text-sm font-medium";
      case "info":
        return "text-blue-800 text-sm font-medium";
      case "success":
        return "text-green-800 text-sm font-medium";
      case "error":
        return "text-red-800 text-sm font-medium";
      default:
        return "text-amber-800 text-sm font-medium";
    }
  };

  return (
    <div className={getAlertClasses()}>
      {icon && <div className={getIconClasses()}>{icon}</div>}
      <span className={getTextClasses()}>{message}</span>
    </div>
  );
};

export default Alert;
