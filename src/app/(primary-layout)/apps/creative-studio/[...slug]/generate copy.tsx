"use client";

/**
 * tt 表单信息填写（原）
 */
import { useState, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Tooltip,
  message,
} from "antd";
import IconSvg from "@/components/Icon/SinoSymbolFont";
import {
  VIDEO_GEN_COUNT,
  FULL_CURRENCY_OPTIONS,
  TARGET_LANGUAGE_OPTIONS,
} from "./constants";
import Upload from "./components/upload";
import { FileHelper } from "@/common";
import { generateTask } from "./services";

const FormItem = Form.Item;

export default function Generate() {
  const [loading, setLoading] = useState<boolean>(false);
  const uploadRef: any = useRef<any>(null);

  const [form] = Form.useForm();
  const router = useRouter();

  const getFilesLen = useCallback((fileList: any[]) => {
    const videoList: any = [];
    const imageList: any = [];
    for (const item of fileList) {
      const mt = FileHelper.getType(item.url);
      if (mt?.includes("video")) {
        videoList.push(item);
      }
      if (mt?.includes("image")) {
        imageList.push(item);
      }
    }
    return {
      videoFilesLen: videoList.length,
      imageFilesLen: imageList.length,
    };
  }, []);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      if (loading) {
        return;
      }
      const fileList = uploadRef.current?.fileList || [];
      if (fileList.some((item: any) => item.status !== "done")) {
        message.destroy();
        message.warning("请检查素材上传是否完成");
        setLoading(false);
        return;
      }

      const { files, ...values } = await form.validateFields();
      const { imageFilesLen } = getFilesLen(files);

      if (imageFilesLen > 0 && imageFilesLen < 3) {
        message.destroy();
        message.error("至少上传3张图片素材");
        return false;
      }

      values.product_video_info.uuidList = files.map((item: any) => item.uuid);
      const { data = {} } = await generateTask(values);
      const taskId = data?.result?.[0];
      if (taskId) {
        router.push(`/apps/creative-studio/generate/${taskId}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto px-6 pt-8 bg-white">
      {/* Title */}
      <h1 className="text-xl font-medium text-gray-900 mb-8">
        今天你想创建什么内容？
      </h1>

      <Form layout="vertical" form={form}>
        {/* Product or Service Name */}
        <div className="mb-8">
          <FormItem
            name={["product_video_info", "product_info_list", 0, "title"]}
            label="产品或服务的标题"
            rules={[{ required: true, whitespace: true }]}
          >
            <Input
              size="large"
              placeholder="请输入产品标题"
              className="w-full"
              maxLength={500}
              showCount
            />
          </FormItem>
        </div>

        <div className="mb-8">
          <FormItem
            name={[
              "product_video_info",
              "product_info_list",
              0,
              "product_name",
            ]}
            label="产品或服务的名称"
            rules={[{ required: true, whitespace: true }]}
          >
            <Input
              size="large"
              placeholder="请输入产品名称"
              className="w-full"
              maxLength={500}
              showCount
            />
          </FormItem>
        </div>

        <div className="mb-8">
          <FormItem
            name={[
              "product_video_info",
              "product_info_list",
              0,
              "selling_points",
            ]}
            label="产品或服务的卖点"
            rules={[{ required: true }]}
          >
            <Select
              mode="tags"
              allowClear
              size="large"
              maxCount={10}
              style={{ width: "100%" }}
              placeholder="推荐每个卖点的长度不超过 10 个词"
              options={[]}
            />
          </FormItem>
        </div>

        {/* Brand and Price Row */}
        <div className="grid grid-cols-2 gap-4 mb-2">
          <div>
            <FormItem
              name={["product_video_info", "product_info_list", 0, "brand"]}
              label="品牌（选填）"
            >
              <Input
                size="large"
                placeholder="请输入品牌名称"
                className="w-full"
                maxLength={100}
                showCount
              />
            </FormItem>
          </div>
          <div>
            <FormItem
              name={["product_video_info", "product_info_list", 0, "price"]}
              label="产品或服务价格（选填）"
            >
              <InputNumber
                className="w-full"
                placeholder="0.00"
                size="large"
                addonAfter={
                  <FormItem
                    name={[
                      "product_video_info",
                      "product_info_list",
                      0,
                      "currency",
                    ]}
                    initialValue="USD"
                    noStyle
                  >
                    <Select
                      size="large"
                      placeholder="币种"
                      style={{ width: 80 }}
                      options={FULL_CURRENCY_OPTIONS}
                      allowClear
                      popupMatchSelectWidth={220}
                      showSearch
                      optionLabelProp="value"
                      filterOption={(inputValue, option: any) => {
                        return (
                          option?.label
                            ?.toUpperCase?.()
                            ?.indexOf?.(inputValue.toUpperCase()) !== -1
                        );
                      }}
                    />
                  </FormItem>
                }
                precision={2}
                min={0}
              />
            </FormItem>
          </div>
        </div>

        {/* Product Description */}
        <div className="mb-8">
          <FormItem
            name={["product_video_info", "product_info_list", 0, "description"]}
            label="描述你销售的产品"
            rules={[
              {
                required: true,
                whitespace: true,
                message: "请输入产品或服务的描述",
              },
            ]}
          >
            <Input.TextArea
              placeholder="描述你的产品并分享其独特的卖点。示例：宽松合身的男女通用 T 恤，采用中等重量 100% 柔软针织棉布料制成。这款经典圆领衫饰有丝网印刷图案。"
              rows={4}
              className="w-full resize-none"
              maxLength={300}
              showCount
            />
          </FormItem>
        </div>

        {/* videos & images */}
        <div className="mb-8">
          <FormItem
            name="files"
            label={
              <div className="flex items-center">
                <h3 className="text-sm font-medium text-gray-700">
                  想要出现在视频中的媒体素材
                </h3>
                <Tooltip title="上传能够展示你附品成服务的素材，为了我得最佳效果，请至少上传2个视频，每个积物时长在15秒到1分钟之间，不添加宇落。最多允许使用21 个素材。">
                  <IconSvg
                    type="icon-信息解释"
                    className="text-gray-400 ml-2"
                  />
                </Tooltip>
              </div>
            }
            rules={[{ required: true, message: "请上传媒体素材" }]}
          >
            <Upload ref={uploadRef} />
          </FormItem>
        </div>

        {/* Video Counts */}
        {/* <div className="mb-8">
          <FormItem
            name={["product_video_info", "video_generation_count"]}
            label="要生成的视频数量"
            initialValue={4}
          >
            <Select size="large" options={VIDEO_GEN_COUNT} />
          </FormItem>
        </div> */}

        {/* Language Selection */}
        {/* <div className="mb-10">
          <FormItem
            name={[
              "product_video_info",
              "product_info_list",
              0,
              "source_language",
            ]}
            label={
              <div className="flex items-center">
                <h3>语言</h3>
                <span className="text-xs text-gray-500 ml-2">
                  (更多语言版本制作中)
                </span>
              </div>
            }
            rules={[{ required: true, message: "请选择语言" }]}
            initialValue="en"
          >
            <Select
              size="large"
              placeholder="请选择语言"
              options={TARGET_LANGUAGE_OPTIONS}
            />
          </FormItem>
        </div> */}
        <FormItem name="aigc_video_type" noStyle initialValue="STOCK_VIDEO">
          <Input type="hidden" />
        </FormItem>
        <FormItem
          name={["product_video_info", "video_generation_count"]}
          noStyle
          initialValue={4}
        >
          <Input type="hidden" />
        </FormItem>
      </Form>

      {/* Generate Button */}
      <div className="sticky bottom-0 right-0 z-10 py-4 bg-white">
        <Button
          type="primary"
          size="large"
          className="w-full"
          loading={loading}
          onClick={handleSubmit}
        >
          生成
        </Button>
      </div>
    </div>
  );
}
