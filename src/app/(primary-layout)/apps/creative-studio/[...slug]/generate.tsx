"use client";

/**
 * tt 表单信息填写，创建产品
 */
import { useState, useRef, useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Tooltip,
  message,
} from "antd";
import IconSvg from "@/components/Icon/SinoSymbolFont";
import {
  VIDEO_GEN_COUNT,
  FULL_CURRENCY_OPTIONS,
  TARGET_LANGUAGE_OPTIONS,
} from "./constants";
import Upload from "./components/upload";
import { FileHelper } from "@/common";
import { generateProduct, getProductUpdate } from "./services";
import { GenerateSvg } from "../home/<USER>/LocalSvg";
import {VIDEO_LIMIT,IMAGE_LIMIT} from '../home/<USER>'

const FormItem = Form.Item;

interface IProps {
  // 编辑商品使用的回显数据
  productData?: any;
  onSuccess?: (d?: any, p?: any) => void;
  hideModal: () => void;
}
export default function Generate(props: IProps) {
  const { productData, onSuccess, hideModal } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const uploadRef: any = useRef<any>(null);

  const [form] = Form.useForm();
  const router = useRouter();

  const getFilesLen = useCallback((fileList: any[]) => {
    const videoList: any = [];
    const imageList: any = [];
    for (const item of fileList) {
      const mt = FileHelper.getType(item.url);
      if (mt?.includes("video")) {
        videoList.push(item);
      }
      if (mt?.includes("image")) {
        imageList.push(item);
      }
    }
    return {
      videoFilesLen: videoList.length,
      imageFilesLen: imageList.length,
    };
  }, []);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      if (loading) {
        return;
      }
      const fileList = uploadRef.current?.fileList || [];
      if (fileList.some((item: any) => item.status !== "done")) {
        message.destroy();
        message.warning("请检查素材上传是否完成");
        setLoading(false);
        return;
      }

      const { files, ...values } = await form.validateFields();
      const { imageFilesLen,videoFilesLen } = getFilesLen(files);

      if (imageFilesLen > 0 && imageFilesLen < 3) {
        message.destroy();
        message.error("至少上传3张图片素材");
        return false;
      }
      if (imageFilesLen > IMAGE_LIMIT) {
        message.destroy();
        message.error(`至多上传${IMAGE_LIMIT}张图片素材`);
        return false;
      }

      if (videoFilesLen > VIDEO_LIMIT) {
        message.destroy();
        message.error(`至多上传${VIDEO_LIMIT}个视频素材`);
        return false;
      }

      values.materialFiles = files.map((item: any) => ({
        uuid: item.uuid,
        path: item.path,
      }));
      const productApi = productData?.isEdit
        ? getProductUpdate
        : generateProduct;
      // return console.log(values, "valuesvalues", form.getFieldsValue());
      const { data = {} } = await productApi(values);
      onSuccess?.(data, productData);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (productData?.isEdit) {
      form.setFieldsValue({
        ...productData,
        // 注意这里得是数组
        files: productData.materialFiles?.map((item: any) => ({
          ...item,
          // 防止列表返回了相同的uid ,导致删除错误
          uid: item.uuid,
          status: "done",
        })),
      });
    }
  }, [form, productData]);

  return (
    <div className="max-w-2xl mx-auto px-6 bg-white">
      {/* Title */}
      {!productData?.isEdit ? (
        <h1 className="text-lg font-medium text-gray-900 mb-2">
          今天你想创建什么内容？
        </h1>
      ) : null}

      <Form layout="vertical" form={form}>
        {/* Product or Service Name */}
        <FormItem
          name={"title"}
          label="产品或服务的标题"
          rules={[{ required: true, whitespace: true }]}
        >
          <Input
            placeholder="请输入产品标题"
            className="w-full"
            maxLength={500}
            showCount
          />
        </FormItem>
        <FormItem
          name={"name"}
          label="产品或服务的名称"
          rules={[{ required: true, whitespace: true }]}
        >
          <Input
            placeholder="请输入产品名称"
            className="w-full"
            maxLength={500}
            showCount
          />
        </FormItem>
        <FormItem
          name={"sellingPoints"}
          label="产品或服务的卖点"
          rules={[{ required: true }]}
        >
          <Select
            mode="tags"
            allowClear
            maxCount={10}
            style={{ width: "100%" }}
            placeholder="推荐每个卖点的长度不超过 10 个词"
            options={[]}
            // 限制每个 tag 最多 10 个字符
            onChange={(value) => {
              // 过滤掉超过10字符的tag
              const filtered = value.map((v: string) =>
                v.length > 10 ? v.slice(0, 10) : v
              );
              if (filtered.join() !== value.join()) {
                // 只要有被截断就更新
                form.setFieldsValue({ sellingPoints: filtered });
              }
            }}
            onInputKeyDown={(e) => {
              // 阻止输入超过10字符的tag
              const input = e.target as HTMLInputElement;
              if (
                input.value &&
                input.value.length >= 10 &&
                e.key.length === 1
              ) {
                e.preventDefault();
              }
            }}
          />
        </FormItem>
        {/* Brand and Price Row */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <FormItem name={"brand"} label="品牌（选填）">
              <Input
                placeholder="请输入品牌名称"
                className="w-full"
                maxLength={100}
                showCount
              />
            </FormItem>
          </div>
          <div>
            <FormItem name={"price"} label="产品或服务价格（选填）">
              <InputNumber
                className="w-full"
                placeholder="0.00"
                addonAfter={
                  <FormItem name={"currency"} initialValue="USD" noStyle>
                    <Select
                      placeholder="币种"
                      style={{ width: 80 }}
                      options={FULL_CURRENCY_OPTIONS}
                      allowClear
                      popupMatchSelectWidth={220}
                      showSearch
                      optionLabelProp="value"
                      filterOption={(inputValue, option: any) => {
                        return (
                          option?.label
                            ?.toUpperCase?.()
                            ?.indexOf?.(inputValue.toUpperCase()) !== -1
                        );
                      }}
                    />
                  </FormItem>
                }
                precision={2}
                min={0}
              />
            </FormItem>
          </div>
        </div>

        {/* Product Description */}
        <FormItem
          name={"description"}
          label="描述你销售的产品"
          rules={[
            {
              required: true,
              whitespace: true,
              message: "请输入产品或服务的描述",
            },
          ]}
        >
          <Input.TextArea
            placeholder="描述你的产品并分享其独特的卖点。示例：宽松合身的男女通用 T 恤，采用中等重量 100% 柔软针织棉布料制成。这款经典圆领衫饰有丝网印刷图案。"
            rows={4}
            className="w-full resize-none"
            maxLength={300}
            showCount
          />
        </FormItem>

        {/* videos & images */}
        <FormItem
          name="files"
          label={
            <div className="flex items-center">
              <h3 className="text-sm font-medium text-gray-700">
                想要出现在视频中的媒体素材
              </h3>
              <Tooltip title="为获得最佳成效，请上传至少 15 秒的视频，可以使用一个或多个视频。">
                <IconSvg type="icon-信息解释" className="text-gray-400 ml-2" />
              </Tooltip>
            </div>
          }
          rules={[{ required: true, message: "请上传媒体素材" }]}
        >
          <Upload ref={uploadRef} limit={productData?.limit || VIDEO_LIMIT}/>
        </FormItem>

        {/* Video Counts */}
        {/* <div className="mb-8">
          <FormItem
            name={["product_video_info", "video_generation_count"]}
            label="要生成的视频数量"
            initialValue={4}
          >
            <Select size="large" options={VIDEO_GEN_COUNT} />
          </FormItem>
        </div> */}

        {/* Language Selection */}
        <FormItem
          name={"language"}
          label={
            <div className="flex items-center">
              <h3>语言</h3>
              <span className="text-xs text-gray-500 ml-2">
                (更多语言版本制作中)
              </span>
            </div>
          }
          rules={[{ required: true, message: "请选择语言" }]}
          initialValue="en"
        >
          <Select placeholder="请选择语言" options={TARGET_LANGUAGE_OPTIONS} />
        </FormItem>
        {/* 编辑时的id */}
        <FormItem name="id" noStyle initialValue={undefined}>
          <Input type="hidden" />
        </FormItem>
        {/* 要创建的 AI 生成视频的类型 */}
        {/* <FormItem name="aigc_video_type" noStyle initialValue="STOCK_VIDEO">
          <Input type="hidden" />
        </FormItem> */}
        {/* 要生成的视频数量  */}
        {/* <FormItem
          name={["product_video_info", "video_generation_count"]}
          noStyle
          initialValue={4}
        >
          <Input type="hidden" />
        </FormItem> */}
      </Form>

      {/* Generate Button */}
      <div className="sticky bottom-0 right-0 z-10 py-4 bg-white flex items-center justify-center">
        <Button size="large" onClick={hideModal} className="w-52 mr-4">
          取消
        </Button>
        <Button
          type="primary"
          size="large"
          className="w-52"
          loading={loading}
          onClick={handleSubmit}
          // icon={<GenerateSvg />}
        >
          保存
        </Button>
      </div>
    </div>
  );
}
