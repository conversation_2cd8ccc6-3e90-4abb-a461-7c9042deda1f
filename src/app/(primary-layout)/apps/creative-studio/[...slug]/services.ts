/*
 * @Date: 2025-06-24 09:15:53
 * @Author: miroku.yang
 * @Description:
 */
import requestHelper from "@/utils/requestHelper";

/**
 * 产品创建
 */
export function generateProduct(data = {}) {
  return requestHelper.post("v4/demand/aigc/product/create", data);
}

/**
 * 产品编辑
 */
export function getProductUpdate(data = {}) {
  return requestHelper.post("v4/demand/aigc/product/update", data);
}

/**
 *
 * @param data 任务详情列表
 * @returns
 */
export function getTaskList(data = {}) {
  // return requestHelper.post('v4/demand/aigc/video/task/list', data);
  return requestHelper.post("v4/demand/aigc/video/result/list", data);
}

/**
 * 保存至我的作品库
 */
export function saveToMySpace(data = {}) {
  return requestHelper.post(
    "v4/demand/user/works/collect/upload/work/personal",
    data
  );
}

// 短剧剪辑故事线结果查询
export function getShortDramaList(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/task/story/line/info",
    data
  );
}

// 短剧剪辑故事线结果重新生成
export function getShortDramaGenerate(data = {}) {
  return requestHelper.post(
    "v4/demand/short/drama/highlights/story/line/regenerate",
    data
  );
}
