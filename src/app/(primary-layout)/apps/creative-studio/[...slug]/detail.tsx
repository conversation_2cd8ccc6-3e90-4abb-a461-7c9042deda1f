"use client";

/**
 * tt 生成
 */
import { useState, useEffect, useMemo, useRef, useCallback } from "react";
import {
  AlertTriangle,
  Download,
  Save,
  ChevronLeft,
  Scroll,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { But<PERSON>, Divider, message, Spin, Checkbox } from "antd";
import { useModalWrap } from "@/components";
import { FileDownloadTask } from "@/components/FileDownload";
import VideoCard from "./components/video-card";
import VideoCardFailed from "./components/video-card-failed";
import Alert from "./components/alert";
import Loader from "./components/loader";
import Error from "./components/error";
import SaveToMySpace from "./components/save-to-my-space";
import DownloadFiles from "./components/download";
import { uploadByUrl } from "@/services/upload";
import { getTaskList, saveToMySpace } from "./services";
import useMoveModal from "@/app/(primary-layout)/space/components/cloudDisk/components/useMoveModal";
import {
  getCompanyShareSpaceDirList,
  getCompanyShareSpaceMove,
  getCreativityCollectList,
  getCreativityCollectAdd,
  getSpaceDirCopy,
} from "@/app/(primary-layout)/space/components/cloudDisk/components/useMoveModal/services";
import { getCompanyShareSpaceList } from "@/app/(primary-layout)/space/components/cloudDisk/components/CloudMenu/services";
import { setSpaceDirAdd } from "@/app/(primary-layout)/space/components/cloudDisk/service";
import globalStore from "@/mobx/global-store";
import { observer, useLocalObservable } from "mobx-react-lite";
import { cn } from "@/lib/utils";
import styles from "./detail.module.scss";
import usePreview from "@/components/usePreview";
import { FileHelper } from "@/lib/common";

interface VideoData {
  id: string;
  name: string;
  aigc_video_type: string;
  url: string;
  status: string;
}

const Detail = ({ taskId }: any) => {
  const globalMobx = useLocalObservable(() => globalStore);
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<any>({});
  const [checkedValues, setCheckedValues] = useState<any>([]);
  // 显示错误页面
  const showErrorRef = useRef<any>(null);

  const router = useRouter();

  const productName = data?.productName || "未命名";

  const isPersonal = globalMobx.getIsPersonal();

  // 数量
  const [numberObj, setNumberObj] = useState<any>({});

  const { show: showPreview, render: renderPreview } = usePreview();

  async function fetchList(params: any = {}) {
    try {
      setLoading(true);
      const { data = {} } = await getTaskList(params);
      if (data?.code === 0) {
        const result = data?.result || {};
        const list = result?.list || [];
        // 数量相对且无数据才展示失败
        showErrorRef.current =
          !result?.list &&
          result?.completeNumber &&
          result?.totalNumber &&
          result?.completeNumber == result?.totalNumber;
        setNumberObj({
          completeNumber: result?.completeNumber,
          totalNumber: result?.totalNumber,
        });
        setData({
          productName: result?.productName,
          items: list.map((item: any) => ({
            id: item.video_id,
            url: item.preview_url,
            name: item.video_id,
            status: item.status,
            aigc_video_type_name_list: item.aigc_video_type_name_list || [],
            uuid: item.uuid || "",
            path: item.previewPath || "",
            // 防止转存到共享空间根目录
            category: 1,
            poster: item?.previewImageUrl,
          })),
        });
        setLoading(false);
        // 部分任务
        if (
          result?.completeNumber > 0 &&
          result?.completeNumber < result?.totalNumber
        ) {
          message.destroy();
          message.warning("有部分任务还在生成中...");
        }
        return;
      }
    } catch (error) {
      console.log(error);
    }
  }

  useEffect(() => {
    async function getData(params: any) {
      await fetchList(params);
    }
    if (taskId) {
      getData({ groupId: taskId });
    }
    return () => {
      showErrorRef.current = false;
    };
  }, []);

  // 加载转存弹窗
  const { showMoveModal, renderMoveModal } = useMoveModal({
    getCompanyShareSpaceListApi: getCompanyShareSpaceList,
    moveFileApi: getCompanyShareSpaceMove,
    dirList: getCompanyShareSpaceDirList,
    moveSuccess: (data?: any) => {
      if (data?.isSave) {
        // 取消选中
        setCheckedValues([]);
        message.warning(
          <div>
            <div>耗时可能较长，请您耐心等待！</div>
            <div>完成后，您可以在“素材“页面中查看。</div>
          </div>
        );
      }
    },
    // spaceId: spaceRecord.id,
    spaceId: 0,
    mySpaceDirList: getCreativityCollectList,
    mySpaceDirAdd: getCreativityCollectAdd,
    shareSpaceDirAdd: setSpaceDirAdd,
    copyFileApi: getSpaceDirCopy,
    isPersonal,
    isTransferFile: true,
  });

  /**
   * 选中
   * @param item
   * @returns
   */
  const handleCheck = (item: any) => {
    const newCheckedValues = [...checkedValues];
    const idx = newCheckedValues.findIndex((v: any) => v.id === item.id);
    if (idx > -1) {
      // 删除
      newCheckedValues.splice(idx, 1);
      setCheckedValues(newCheckedValues);
      return;
    }
    // 追加
    newCheckedValues.push(item);
    setCheckedValues(newCheckedValues);
  };

  const uploadPromise = (url: string) => {
    return new Promise(async (resolve) => {
      try {
        const { data = {} } = await uploadByUrl({ url });
        if (data?.code === 0) {
          const result = data?.result || {};
          if (result.uuid && result.url) {
            resolve({
              status: "done",
              percent: 100,
              uuid: result.uuid,
              path: result.path,
              url: result.url,
              name: result.name,
            });
          }
          return;
        }
        resolve({ status: "error" });
      } catch (error) {
        resolve({ status: "error" });
      }
    });
  };

  /**
   * 保存提交
   */
  const handleSaveOk = async ({ items }: any) => {
    if (!Array.isArray(items) || items?.length === 0) {
      return false;
    }
    try {
      const fileList = await Promise.all(
        items.map(async (item: any) => uploadPromise(item.url))
      );
      if (fileList.some((item: any) => item.status === "error")) {
        message.destroy();
        message.error("保存失败，请重试");
        return false;
      }

      const { data = {} } = await saveToMySpace({
        collectId: null,
        personalWorkDT: {
          designerProductionDTO: {
            desc: "",
            name: "",
            productionList: fileList
              .map((file: any) => ({
                path: file.path,
                uuid: file.uuid,
                url: file.url,
                name: file.name,
              }))
              .filter((file: any) => file.uuid),
          },
        },
      });
      if (data?.code === 0) {
        message.destroy();
        message.success("保存成功");
        setCheckedValues([]);
        return true;
      }
      return false;
    } catch (error) {
      message.destroy();
      message.error("保存失败");
      return false;
    }
  };

  /**
   * 下载提交
   * @returns
   */
  const handleDownloadOk = async ({ items }: any) => {
    if (!Array.isArray(items) || items?.length === 0) {
      return false;
    }
    try {
      await FileDownloadTask.add({
        files: items.map((file: any, index: number) => ({
          name: `${productName}（${index + 1}）.mp4`,
          url: file.url,
        })),
      });
      setCheckedValues([]);
      return true;
    } catch (error) {
      message.destroy();
      message.error("下载失败");
      return false;
    }
  };

  /**
   * 保存至我的空间
   */
  const { showModal: showSaveModal, render: renderSaveModal } = useModalWrap({
    modalProps: {
      width: 480,
      title: "保存至素材库",
      onOk: handleSaveOk,
    },
    children: ({ showParams, form }: any) => {
      return (
        <SaveToMySpace
          initialValues={showParams ? showParams : null}
          form={form}
        />
      );
    },
  });

  const { showModal: showDownloadModal, render: renderDownloadModal } =
    useModalWrap({
      modalProps: {
        width: 480,
        title: "下载",
        onOk: handleDownloadOk,
      },
      children: ({ showParams, form }: any) => {
        return (
          <DownloadFiles
            initialValues={showParams ? showParams : null}
            form={form}
          />
        );
      },
    });

  /**
   * 保存
   * @param items
   */
  const handleSave = (items: any) => {
    if (!Array.isArray(items) || items?.length === 0) {
      return;
    }
    showMoveModal(items);
    // showSaveModal({ items });
  };

  /**
   * 返回
   */
  const handleBack = () => {
    router.push("/apps/creative-studio/home");
  };

  /**
   * 下载
   */
  const handleDownload = (items: any) => {
    if (!Array.isArray(items) || items?.length === 0) {
      return;
    }
    showDownloadModal({ items });
  };

  const items = useMemo(() => {
    if (Array.isArray(data?.items)) {
      return data.items;
    }
    return [];
  }, [data]);

  const checkedIds = useMemo(() => {
    return checkedValues.map((v: any) => v.id);
  }, [checkedValues]);

  const isEmpty: boolean = checkedIds.length === 0;

  // 复用的过滤函数：只选成功的数据
  const getSuccessItems = (items: any[]) =>
    items.filter((video: any) => video.status === "SUCCEED" && video.url);

  // 全选处理
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setCheckedValues(getSuccessItems(items));
    } else {
      setCheckedValues([]);
    }
  };

  const headerDom = (
    <div className="flex justify-between">
      <div className="flex items-center flex-1 overflow-hidden">
        <button
          onClick={handleBack}
          className="flex items-center text-blue-500 hover:text-blue-600 transition-colors"
        >
          <ChevronLeft className="h-5 w-5 text-[18px]" />
          <span className="font-medium text-[18px] whitespace-nowrap">
            返回
          </span>
        </button>
        <Divider type="vertical" className="!mx-4" />
        <h1
          title={productName}
          className="text-[18px] font-bold text-gray-900 text-ellipsis overflow-hidden whitespace-nowrap mb-0"
        >
          {productName}
        </h1>
      </div>
      <div className="flex items-center justify-end gap-4">
        <Checkbox
          checked={
            checkedValues.length > 0 &&
            checkedValues.length === getSuccessItems(items).length
          }
          indeterminate={
            checkedValues.length > 0 &&
            checkedValues.length < getSuccessItems(items).length
          }
          onChange={(e) => handleSelectAll(e.target.checked)}
        >
          全选
        </Checkbox>
        <Button
          icon={<Download className="w-4 h-4 relative top-0.5" />}
          disabled={isEmpty}
          onClick={() => handleDownload(checkedValues)}
        >
          下载
        </Button>
        <Button
          type="primary"
          icon={<Save className="w-4 h-4 relative top-0.5" />}
          disabled={isEmpty}
          onClick={() => handleSave(checkedValues)}
        >
          保存至素材库
        </Button>
      </div>
    </div>
  );

  // 卡片hover
  const handlePreview = (list: any, idx: number) => {
    const newFiles = list.map((item: any) => {
      // 有的数据结构是有fileInfo 这层，有的之间在item 中
      const file = item;
      return {
        url: file.url,
        uuid: file.uuid,
        name: file.name || "",
        mimeType: FileHelper.getType(file.url),
        src: file.url,
        poster: file?.poster,
        previewUrl: file?.url,
        hideFullScreenDownloadIcon: false,
      };
    });
    showPreview({
      views: newFiles,
      current: idx,
      useXgPlayer: true,
    });
  };

  return (
    <div className="w-full h-full pt-10 overflow-hidden">
      {/* Header */}
      <div className="px-10 2xl:px-20 pb-6">{headerDom}</div>
      <div className="mx-auto w-full h-full flex flex-col  px-10 2xl:px-20 overflow-y-auto pb-10">
        {/* <div className="pb-6">{headerDom}</div> */}
        <Spin
          spinning={loading}
          wrapperClassName={cn("flex-1", styles["spin-wrap"])}
        >
          {!loading && showErrorRef.current ? (
            <Error onBack={handleBack} className="flex-1" />
          ) : (
            <>
              {/* Warning Alert */}
              {/* <Alert
                type="warning"
                icon={<AlertTriangle className="w-5 h-5" />}
                message="本次生成结果6小时内有效"
              /> */}
              {/* Video Cards Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                {items.map((video: VideoData, index: number) => {
                  const id = video.id;
                  if (video.status === "FAILED" || !video.url) {
                    return <VideoCardFailed key={id} video={video} />;
                  }
                  return (
                    <VideoCard
                      key={id}
                      video={video}
                      checked={checkedIds.includes(id)}
                      onChecked={handleCheck}
                      onDownload={handleDownload}
                      onSave={handleSave}
                      onPreview={handlePreview}
                      items={items}
                      idx={index}
                    />
                  );
                })}
              </div>
              <div className="h-4"></div>
            </>
          )}
          {numberObj?.completeNumber > 0 &&
            numberObj?.completeNumber < numberObj?.totalNumber && (
              <div className="col-span-full flex items-center justify-center py-4">
                <Spin size="small" />
                <span className="ml-2 text-gray-400">
                  有部分任务还在生成中...
                </span>
              </div>
            )}
        </Spin>
        <div className="overflow-hidden h-0 pb-10">
          {renderSaveModal()}
          {renderDownloadModal()}
          {/* 保存至素材库 */}
          {renderMoveModal()}
          {/* 卡片hover */}
          {renderPreview()}
        </div>
      </div>
    </div>
  );
};

export default observer(Detail);
