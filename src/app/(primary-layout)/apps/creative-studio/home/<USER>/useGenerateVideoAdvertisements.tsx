/*
 * @Date: 2025-06-19 10:51:47
 * @Author: miroku.yang
 * @Description: 生成广告视频
 */
import React, { useEffect, useRef, useState } from "react";
import { Select, Button, Form, Checkbox, Image, Input, message } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { useModalWrap } from "@/components";
import {
  TARGET_LANGUAGE_OPTIONS,
  VIDEO_GENERATION_COUNT,
} from "../../[...slug]/constants";
import { useRouter } from "next/navigation";
import { getBatchTaskCreate } from "../services";
import ProductSelect from "./ProductSelect";
import { GenerateSvg, DeleteSvg } from "./LocalSvg";
import find from "lodash/find";
import pick from "lodash/pick";
import { FileHelper } from "@/lib/common";
import styles from "./index.module.scss";
import { VIDEO_LIMIT, IMAGE_LIMIT } from "../util";

const FormItem = Form.Item;

interface IProps {
  // 操作成功后的回到
  onSuccessCallback?: (p: any, a: any) => void;
  // 渲染新建产品弹窗
  showAddGenerateModal?: () => void;
  hideAddGenerateModal?: () => void;
  // 渲染数字人抽屉
  showDigitalHumans?: () => void;
  // 选中的数字人数据
  humanData?: any;
  upodateSelectedCard: (r: any) => void;
}
const useGenerateVideoAdvertisements = (props?: IProps) => {
  const {
    onSuccessCallback,
    showAddGenerateModal,
    hideAddGenerateModal,
    showDigitalHumans,
    humanData,
    upodateSelectedCard,
  } = props || {};
  const showParamsRef = useRef<any>({});
  const formRef = useRef<any>(null);
  const [showParams, setShowParams] = useState<any>({});
  const [submitLoading, setSubmitLoading] = useState(false);
  const [currentHumanData, setCurrentHumanData] = useState<any>({});
  const productSelectRef = useRef<any>({});
  const videoRef = useRef<any>(null);

  const router = useRouter();

  const checkboxOption: any[] = [
    { label: "数字人", value: "AVATAR_PRODUCT", className: "label-2" },
    { label: "增加版权素材", value: "STOCK_VIDEO", className: "label-1" },
    { label: "原视频混剪", value: "VOICEOVER", className: "label-3" },
  ];

  useEffect(() => {
    setCurrentHumanData(humanData);
    if (humanData?.id) {
      formRef.current.setFieldsValue({
        product_video_info: {
          avatar_info: {
            avatar_id: humanData?.id,
          },
        },
      });
    }
  }, [humanData]);

  const handleSubmit = async () => {
    setSubmitLoading(true);
    try {
      if (submitLoading) {
        return;
      }
      const { ...values } = await formRef.current?.validateFields();
      const product = find(productSelectRef.current?.products, {
        id: formRef.current.getFieldValue("product_id"),
      });
      if (product) {
        // 产品列表参数同生成接口参数不一致，需要转换
        product.product_name = product.name;
        product.selling_points = product.sellingPoints;
        product.source_language = product.language;
        values.product_video_info.uuidList = product?.materialFiles?.map(
          (item: any) => item.uuid
        );
        const newProduct = pick(product, [
          "brand",
          "currency",
          "description",
          "price",
          "product_name",
          "selling_points",
          "title",
          "source_language",
        ]);
        // 找到产品对应的详细信息，整理为入参
        values.product_video_info.product_info_list = [newProduct];
      }
      // return console.log(values,'values')
      const { data = {} } = await getBatchTaskCreate(values);
      hideAddGenerateModal?.();
      onSuccessCallback?.(data, showParams);
    } finally {
      setSubmitLoading(false);
    }
  };

  const {
    showModal: showGenerateVideoAdvertisements,
    hideModal,
    render: rendeGenerateVideoAdvertisements,
    form: any,
  } = useModalWrap({
    modalProps: {
      title: (
        <div>
          <div className="text-2xl">生成广告视频</div>
          <div className="text-xl mt-4">选择要推广的产品</div>
        </div>
      ),
      width: 800,
      footer: null,
      maskClosable: false,
      afterClose: () => {
        setCurrentHumanData({});
        formRef.current?.resetFields?.();
        if (videoRef.current) {
          videoRef.current?.pause?.();
        }
      },
      destroyOnClose: true,
    },
    children: ({ form }: any) => {
      formRef.current = form;
      return (
        <div>
          <Form
            form={form}
            onValuesChange={(changedValues, allValues) => {
              // 如果当前编辑的是产品，需要对数字人进行重新校验
              if ("product_id" in changedValues) {
                form.validateFields();
              }
            }}
          >
            <FormItem
              name={"product_id"}
              layout="vertical"
              label={<div className="flex items-center">产品</div>}
              rules={[{ required: true, message: "请选择产品" }]}
              className="!h-[103px]"
            >
              <ProductSelect
                showAddGenerateModal={showAddGenerateModal}
                hideAddGenerateModal={hideAddGenerateModal}
                ref={productSelectRef}
                form={form}
              />
            </FormItem>
            <FormItem
              name={["product_video_info", "target_language"]}
              layout="vertical"
              label={
                <div className="flex items-center">
                  <h3>语言</h3>
                  <span className="text-xs text-gray-500 ml-2">
                    (更多语言版本制作中)
                  </span>
                </div>
              }
              rules={[{ required: true, message: "请选择语言" }]}
              initialValue="en"
              className="!h-[68px]"
            >
              <Select
                size="large"
                placeholder="请选择语言"
                options={TARGET_LANGUAGE_OPTIONS}
              />
            </FormItem>
            <FormItem
              noStyle
              shouldUpdate={(prevValues: any, curValues: any) => {
                if (!prevValues.aigcVideoTypeList?.includes("AVATAR_PRODUCT")) {
                  upodateSelectedCard?.({});
                }
                return (
                  prevValues.aigcVideoTypeList != curValues.aigcVideoTypeList
                );
              }}
            >
              {() => (
                <FormItem
                  name={"aigcVideoTypeList"}
                  layout="vertical"
                  label={<div className="flex items-center">任务类型</div>}
                  rules={[
                    { required: true, message: "请选择任务类型" },
                    () => ({
                      validator(_, value) {
                        if (!value?.length) {
                          return Promise.resolve();
                        }
                        const hasStockVideoOrVoiceover =
                          value?.includes("STOCK_VIDEO") ||
                          value?.includes("VOICEOVER");
                        const product = find(
                          productSelectRef.current?.products,
                          {
                            id: formRef.current.getFieldValue("product_id"),
                          }
                        );
                        // 验证视频
                        const videos = product?.materialFiles
                          ?.map((item: any) => item.url)
                          ?.filter((url: string) =>
                            FileHelper.getType(url)?.includes("video")
                          );

                        if (hasStockVideoOrVoiceover) {
                          if (!videos?.length) {
                            const errorText =
                              "选择“增加版权素材”或“原视频混剪”时，所选产品下的媒体素材至少需要上传1个视频类型素材，请对该产品进行编辑，补充所需的素材。";
                            message?.destroy();
                            message.error(errorText);
                            return Promise.reject();
                          }
                          if (videos?.length > VIDEO_LIMIT) {
                            const text = `选择“增加版权素材”或“原视频混剪”时，所选产品下的媒体素材不能超过${VIDEO_LIMIT}个视频类型素材，请对该产品进行编辑，调整所需的素材。`;
                            message?.destroy();
                            message.error(text);
                            return Promise.reject();
                          }
                        }
                        // 验证图片
                        const images = product?.materialFiles
                          ?.map((item: any) => item.url)
                          ?.filter((url: string) =>
                            FileHelper.getType(url)?.includes("image")
                          );
                        if (value?.includes("AVATAR_PRODUCT")) {
                          // 上限
                          if (images?.length > IMAGE_LIMIT) {
                            const text = `选择“数字人”时，所选产品下的媒体素材不能超过${IMAGE_LIMIT}个图片类型素材，请对该产品进行编辑，调整所需的素材。`;
                            message?.destroy();
                            message.error(text);
                            return Promise.reject();
                          }
                          // 下限
                          if (images?.length >= 3) {
                            return Promise.resolve();
                          } else {
                            const text =
                              "选择“数字人”时，所选产品下的媒体素材至少需要上传3个图片类型素材，请对该产品进行编辑，补充所需的素材。";
                            message?.destroy();
                            message.error(text);
                            return Promise.reject();
                          }
                        }
                        return Promise.resolve();
                      },
                    }),
                  ]}
                  initialValue={["STOCK_VIDEO", "AVATAR_PRODUCT", "VOICEOVER"]}
                  className="!h-[64px]"
                >
                  <Checkbox.Group
                    options={checkboxOption}
                    onChange={(a: any) => {
                      // 如果没有数字人
                      if (!a.includes("AVATAR_PRODUCT")) {
                        formRef.current.setFieldsValue({
                          product_video_info: {
                            avatar_info: {
                              avatar_id: undefined,
                            },
                          },
                        });
                        setCurrentHumanData({});
                      }
                    }}
                  />
                </FormItem>
              )}
            </FormItem>

            <Form.Item
              noStyle
              shouldUpdate={(prevValues: any, curValues: any) => {
                if (!prevValues.aigcVideoTypeList?.includes("AVATAR_PRODUCT")) {
                  upodateSelectedCard?.({});
                }
                return (
                  prevValues.aigcVideoTypeList != curValues.aigcVideoTypeList
                );
              }}
            >
              {({ getFieldValue }) => {
                return getFieldValue("aigcVideoTypeList")?.includes(
                  "AVATAR_PRODUCT"
                ) ? (
                  <FormItem
                    // name={"human"}
                    name={["product_video_info", "avatar_info", "avatar_id"]}
                    layout="vertical"
                    label={
                      <div className="flex items-center">
                        <span>数字人</span>
                      </div>
                    }
                    // initialValue={["1", "2", "3"]}
                    className="!h-[190px]"
                    rules={[{ required: true, message: "请选择数字人" }]}
                  >
                    <div className="w-[120px] h-[160px] bg-gray-300 rounded-8">
                      {currentHumanData?.previewUrl ? (
                        <div className="relative w-full h-full">
                          <Image
                            src={currentHumanData?.thumbnail}
                            wrapperClassName="w-full h-full overflow-hidden"
                            rootClassName={styles["image-root-class"]}
                            className="!w-full !h-full object-contain"
                            alt="数字人"
                            preview={{
                              mask: (
                                <div className="flex flex-col items-center justify-between">
                                  <span className="mb-2">预览</span>
                                  <span
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      showDigitalHumans?.();
                                    }}
                                  >
                                    重新选择
                                  </span>
                                </div>
                              ),
                              imageRender: () => (
                                <video
                                  width="100%"
                                  className="h-full"
                                  controls
                                  src={currentHumanData?.previewUrl}
                                  ref={videoRef}
                                />
                              ),
                            }}
                          />
                          <span
                            className="absolute top-[-12px] right-[-12px] z-10 cursor-pointer bg-zinc-300/80 rounded-full p-1 hover:bg-red-100 transition"
                            onClick={() => {
                              formRef.current.setFieldsValue({
                                product_video_info: {
                                  avatar_info: {
                                    avatar_id: undefined,
                                  },
                                },
                              });
                              setCurrentHumanData({});
                            }}
                          >
                            <DeleteSvg />
                          </span>
                        </div>
                      ) : (
                        <div
                          className="w-full h-full flex flex-col items-center justify-center bg-[#000]/50 text-white cursor-pointer"
                          onClick={showDigitalHumans}
                        >
                          <PlusOutlined
                            style={{ fontSize: 32, color: "#fff" }}
                          />
                          <div className="mt-2">请选择数字人</div>
                        </div>
                      )}
                    </div>
                  </FormItem>
                ) : null;
              }}
            </Form.Item>
            {/* <Form.Item
              label="视频时长"
              name={"duration"}
              layout="vertical"
              className="!h-[64px]"
              initialValue="推荐"
            >
              <Radio.Group optionType="button" buttonStyle="solid">
                <Radio value="推荐">推荐</Radio>
                <Radio value="15秒">15 秒 </Radio>
                <Radio value="30秒">30 秒</Radio>
              </Radio.Group>
            </Form.Item> */}
            <Form.Item
              name={"number"}
              label="输出条数"
              layout="vertical"
              className="!h-[64px]"
              initialValue={30}
            >
              <Select
                size="large"
                placeholder="请选择输出条数"
                options={VIDEO_GENERATION_COUNT}
              />
            </Form.Item>
            {/* 要生成的视频数量  */}
            <FormItem name={["product_video_info", "video_generation_count"]} noStyle initialValue={30}>
              <Input type="hidden" />
            </FormItem>
            <Form.Item
              className="bottom-0 right-0 z-10 py-4 bg-white mt-8"
              shouldUpdate={(prevValues, curValues) =>
                prevValues.product_id !== curValues.product_id
              }
            >
              {({ getFieldValue }) => (
                <div className="flex items-center justify-center">
                  <Button
                    size="large"
                    onClick={hideModal}
                    className="w-52 mr-4"
                  >
                    取消
                  </Button>
                  <Button
                    type="primary"
                    size="large"
                    className="w-52"
                    disabled={!getFieldValue("product_id")}
                    loading={submitLoading}
                    onClick={handleSubmit}
                    // icon={<GenerateSvg />}
                  >
                    {submitLoading ? "生成中..." : "生成"}
                  </Button>
                </div>
              )}
            </Form.Item>
          </Form>
        </div>
      );
    },
  });

  const handleShowModal = (showModalParams: any = {}) => {
    showParamsRef.current = showModalParams || {};
    setShowParams(showModalParams);
    showGenerateVideoAdvertisements(showModalParams);
  };

  return {
    showGenerateVideoAdvertisements: handleShowModal,
    hideGenerateVideoAdvertisements: hideModal,
    rendeGenerateVideoAdvertisements,
  };
};
export default useGenerateVideoAdvertisements;
