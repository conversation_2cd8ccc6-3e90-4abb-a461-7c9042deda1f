/*
 * @Date: 2025-06-19 10:51:47
 * @Author: miroku.yang
 * @Description: 新建产品
 */
import { message } from "antd";
import { useModalWrap } from "@/components";
import { useRef, useState } from "react";
import Generate from "../../[...slug]/generate";
import eventBus from "@/utils/eventBus";
import { EVENT_NAME } from "../util";

interface IProps {
  // 操作成功后的回到
  onSuccessCallback?: (p?: any, isEdit?: boolean) => void;
}
const useAddGenerate = (props?: IProps) => {
  const { onSuccessCallback } = props || {};
  const showParamsRef = useRef<any>({});
  const [showParams, setShowParams] = useState<any>({});

  const {
    showModal: showAddGenerateModal,
    hideModal,
    render: renderAddGenerateModal,
    form: any,
  } = useModalWrap({
    modalProps: {
      title: (
        <div className="text-center text-2xl">
          {!showParamsRef.current?.isEdit ? "添加" : "编辑"}产品或服务
        </div>
      ),
      width: 800,
      footer: null,
      maskClosable: false,
      classNames: {
        header: "!pt-5 !pb-3",
        body: "!pb-4",
      },
    },
    children: ({ form }: any) => {
      return (
        <Generate
          productData={showParams}
          hideModal={hideModal}
          onSuccess={(data?: any, p?: any) => {
            if (data?.code === 0) {
              onSuccessCallback?.(data);
              message.success(
                !showParamsRef.current?.isEdit ? "添加成功" : "编辑成功"
              );
              hideModal?.();
              if (p?.isOtherAdd) {
                // 在其他地方进行的新建，如生成广告视频弹窗上的新建，派发更新
                eventBus.emit(EVENT_NAME.SELECT_PRODUCT_ADD_SUCCESS, {
                  data,
                  showParams: p,
                });
              }
            }
          }}
        />
      );
    },
  });

  const handleShowModal = (showModalParams: any = {}) => {
    showParamsRef.current = showModalParams || {};
    setShowParams(showModalParams);
    showAddGenerateModal(showModalParams);
  };

  return {
    showAddGenerateModal: handleShowModal,
    hideAddGenerateModal: hideModal,
    renderAddGenerateModal,
  };
};
export default useAddGenerate;
