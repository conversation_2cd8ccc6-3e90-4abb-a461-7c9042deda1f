/*
 * @Date: 2025-06-19 10:51:47
 * @Author: miroku.yang
 * @Description: 选择数字人
 */
import React, { useRef, useState, useEffect } from "react";
import { <PERSON><PERSON>, Drawer, Spin, Tooltip } from "antd";
import clsx from "clsx";
import { getDigitalAvatarList } from "../../services";
import styles from "./index.module.scss";
import { ScrollArea } from "@/components/ui/scroll-area";
import { getInitialPagination } from "@/constants/table";
import pick from "lodash/pick";
import { FileHelper } from "@/lib/common";
import LookImg from "@/components/LookImg";
import usePreview from "@/components/usePreview";
import { IconSvg } from "@/components";

interface IProps {
  // 操作成功后的回到
  onSuccessCallback?: (p?: any, isEdit?: boolean) => void;
}
const useDigitalHumans = (props?: IProps) => {
  const { onSuccessCallback } = props || {};
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [showParams, setShowParams] = useState<any>({});
  const [selectedCard, setSelectedCard] = useState<any>({});
  const [loading, setLoading] = useState<any>({});
  const [fetching, setFetching] = useState<any>({});
  const [hasMore, setHasMore] = useState<any>({});
  const INITIAL_PAGINATION = getInitialPagination({ pageSize: 20 });
  const [pagination, setPagination] = useState({ ...INITIAL_PAGINATION });
  const [digitalHumans, setDigitalHumans] = useState<any>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  const showParamsRef = useRef<any>({});

  const { show: showPreview, render: renderPreview } = usePreview();

  const handleShowDrawer = (showModalParams: any = {}) => {
    showParamsRef.current = showModalParams || {};
    setShowParams(showModalParams);
    setDrawerOpen(true);
  };

  const handleHideDrawer = () => {
    setDrawerOpen(false);
  };

  const upodateSelectedCard = (r: any) => {
    setSelectedCard(r);
  };

  const handleOk = () => {
    onSuccessCallback?.({ humanData: selectedCard });
    setDrawerOpen(false);
  };

  const fetchDigitalAvatarList = async (params: any = {}) => {
    try {
      const isLoading = params?.isLoading ?? true;
      setLoading(isLoading);
      const newParams = pick(params, ["pageNo", "pageSize"]);
      const { data } = await getDigitalAvatarList(newParams);
      if (data?.code === 0) {
        setDigitalHumans(
          params.pageNo > 1
            ? [...digitalHumans, ...data?.result?.items]
            : data?.result?.items || []
        );
        setPagination({
          ...pagination,
          current: params?.pageNo || INITIAL_PAGINATION.current,
          pageSize: params?.pageSize || INITIAL_PAGINATION.pageSize,
          total: data?.total || 0,
        });
        setHasMore(data?.result?.hasNext);
      }
    } catch (error) {
      console.error("获取数字人列表失败", error);
    } finally {
      setLoading(false);
    }
  };

  const { current, pageSize } = pagination;

  useEffect(() => {
    if (drawerOpen) {
      fetchDigitalAvatarList({
        pageNo: current,
        pageSize: pageSize,
      }).finally(() => {
        setFetching(false);
      });
    }
  }, [drawerOpen]);

  // 滚动加载
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    const handleScroll = () => {
      const scrollTop = container.scrollTop;
      const clientHeight = container.clientHeight;
      const scrollHeight = container.scrollHeight;
      if (
        scrollTop + clientHeight >= scrollHeight - 100 &&
        !loading &&
        !fetching &&
        hasMore &&
        drawerOpen
      ) {
        setFetching(true);
        fetchDigitalAvatarList({
          isLoading: false,
          pageNo: pagination.current + 1,
          pageSize: pagination.pageSize,
        }).finally(() => {
          setFetching(false);
        });
      }
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [loading, fetching, hasMore, drawerOpen]);

  // 卡片hover
  const handlePreview = (list: any, idx: number) => {
    const newFiles = list.map((item: any) => {
      // 有的数据结构是有fileInfo 这层，有的之间在item 中
      const file =
        item.fileInfo && "url" in item.fileInfo ? item.fileInfo : item;
      return {
        url: file.previewUrl,
        uuid: file.previewUuid,
        name: file.name || "",
        mimeType: FileHelper.getType(file.previewUrl),
        src: file.previewUrl,
        poster: file?.thumbnail,
        previewUrl: file?.previewUrl,
        hideFullScreenDownloadIcon: false,
      };
    });
    showPreview({
      views: newFiles,
      current: idx,
      useXgPlayer: true,
    });
  };

  return {
    showDigitalHumans: handleShowDrawer,
    hideDigitalHumans: handleHideDrawer,
    rendeDigitalHumans: () => {
      return (
        <>
          <Drawer
            open={drawerOpen}
            width={408}
            onClose={() => {
              setSelectedCard({});
              setDrawerOpen(false);
            }}
            title="数字人"
            className={clsx(styles["digital-humans-drawer"])}
            footer={
              <>
                <Button onClick={handleHideDrawer} className="mr-3">
                  取消
                </Button>
                <Button type="primary" onClick={handleOk}>
                  确定
                </Button>
              </>
            }
            zIndex={1005}
            destroyOnClose
          >
            <ScrollArea ref={containerRef} className="h-full w-full">
              <div className="grid grid-cols-2 gap-4 p-4">
                {digitalHumans.map((item: any, index: number) => {
                  const isVideo = FileHelper.getType(item.previewUrl)?.includes(
                    "video"
                  );
                  return (
                    <div key={item.id} className="flex flex-col items-center">
                      <div
                        className={`rounded-lg overflow-hidden bg-white cursor-pointer border transition-all duration-200 w-full h-full
                        ${
                          selectedCard?.id === item.id
                            ? "border-1 border-blue-500 shadow-[0_0_0_2px_#bae7ff]"
                            : "border border-gray-200"
                        }
                      `}
                        onClick={() => {
                          setSelectedCard(item);
                        }}
                      >
                        {/* eslint-disable-next-line @next/next/no-img-element */}
                        {isVideo ? (
                          <div className="relative group">
                            <span
                              className={clsx(
                                styles["preview-icon"],
                                "absolute top-2 right-2 z-10 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity"
                              )}
                              onClick={(e: any) => {
                                handlePreview(digitalHumans, index);
                                e.stopPropagation();
                              }}
                            >
                              <IconSvg
                                type={"iconyulan"}
                                style={{ color: "#111", fontSize: 14 }}
                              />
                            </span>
                            <LookImg
                              className={styles["human-lookimg"]}
                              url={item.previewUrl}
                              poster={item.thumbnail}
                              width={"100%"}
                              height={320}
                              showDelete={false}
                              showPlayIcon={false}
                              showPreviewIcon={false}
                            />
                          </div>
                        ) : (
                          // eslint-disable-next-line @next/next/no-img-element
                          <img
                            src={item.previewUrl}
                            alt={item.name}
                            className="w-full h-full object-cover rounded-xl"
                            onError={(error: any) => {
                              console.error("数字人图片加载失败", error);
                            }}
                          />
                        )}
                      </div>
                      <Tooltip title={item.name}>
                        <div className="px-2 py-3 text-center text-sm text-gray-900 min-h-[44px] w-full truncate">
                          {item.name}
                        </div>
                      </Tooltip>
                    </div>
                  );
                })}
              </div>
              {/* 占位卡片 */}
              {/* <div className="">
              <div className="rounded-xl border border-dashed border-gray-200 bg-gray-50 h-80 flex flex-col items-center "></div>
              <div className="px-2 py-3 text-center text-sm text-gray-900 min-h-[44px] w-full truncate">
                无数字人
              </div>
            </div> */}
              {/* 底部加载中 */}
              {fetching && (
                <div className="col-span-full flex items-center justify-center py-4">
                  <Spin size="small" />
                  <span className="ml-2 text-gray-400">加载中...</span>
                </div>
              )}
            </ScrollArea>
          </Drawer>
          <div style={{ height: 0, overflow: "hidden" }}>
            {/* 卡片hover */}
            {renderPreview()}
          </div>
        </>
      );
    },
    upodateSelectedCard,
  };
};
export default useDigitalHumans;
