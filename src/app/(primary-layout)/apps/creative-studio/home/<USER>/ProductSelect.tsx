import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Select, Input, Avatar, Space, Popconfirm, message } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { getProductList, getDeleteProduct } from "../services";
import eventBus from "@/utils/eventBus";
import { EVENT_NAME, IMAGE_LIMIT, VIDEO_LIMIT } from "../util";
import styles from './index.module.scss';
import clsx from "clsx";

interface IProps {
  showAddGenerateModal?: (p?: any) => void;
  hideAddGenerateModal?: () => void;
  onChange?: (v: any, option: any) => void;
  value?: any;
  form: any;
}

const ProductSelect = (props: IProps, ref: any) => {
  const { showAddGenerateModal, hideAddGenerateModal, form } = props;
  const [listLoading, setListLoading] = useState(false);
  const [productOpen, setProductOpen] = useState(false);
  const [search, setSearch] = useState("");
  const [value, setValue] = useState(undefined);
  const [products, setProducts] = useState([]);

  useEffect(() => {
    setValue(props.value);
  }, [props.value]);

  const fetchProductList = async (params: any) => {
    const isLoading = params?.isLoading ?? true;
    const loadMore = params?.loadMore ?? false;
    setListLoading(isLoading);
    try {
      const { data } = await getProductList(params);
      if (data?.code === 0) {
        setProducts(data?.result?.items || []);
      }
    } catch (error) {
      console.log(
        error,
        loadMore ? "滚动加载获取产品列表失败" : "获取产品列表失败"
      );
    } finally {
      setListLoading(false);
    }
  };

  useEffect(() => {
    fetchProductList({ pageNo: 1, pageSize: 99999 });
    // 监听刷新事件
    const fn = ({ data, showParams }: any) => {
      hideAddGenerateModal?.();
      // 重新请求
      fetchProductList({ pageNo: 1, pageSize: 99999 });
    };
    eventBus.on(EVENT_NAME.SELECT_PRODUCT_ADD_SUCCESS, fn);
    return () => {
      eventBus.off(EVENT_NAME.SELECT_PRODUCT_ADD_SUCCESS, fn);
    };
  }, []);

  const getLimit = () => {
    const hasStockVideoOrVoiceover =
      form.getFieldValue("aigcVideoTypeList")?.includes("STOCK_VIDEO") ||
      form.getFieldValue("aigcVideoTypeList")?.includes("VOICEOVER");

    const isAvatar = form
      .getFieldValue("aigcVideoTypeList")
      ?.includes("AVATAR_PRODUCT");

    return (
      (hasStockVideoOrVoiceover ? VIDEO_LIMIT : 0) +
      (isAvatar ? IMAGE_LIMIT : 0)
    );
  };

  // 新建按钮
  const addOption = (
    <div
      className="!h-[72px] !flex !items-center px-4 cursor-pointer hover:bg-[#f1f2f2] rounded-[4px]"
      onClick={() => {
        setProductOpen(false);
        showAddGenerateModal?.({
          isOtherAdd: true,
          limit: getLimit(),
        });
      }}
    >
      <Avatar
        shape="square"
        size={32}
        style={{ background: "#f8f8f9", marginRight: 8, color: "#d3d4d5" }}
      >
        <PlusOutlined />
      </Avatar>
      <span className="font-medium">新增产品信息</span>
    </div>
  );

  const handleDelete = async (record: any) => {
    const product: any = products.find((item: any) => item.id === record.id);
    if (!product) {
      message.error("产品不存在");
      return;
    }
    try {
      const { data } = await getDeleteProduct({ id: product.id });
      if (data?.code === 0) {
        message.success("删除成功");
        // 如果当前选中的与删除的是同一个，则需要将选中的值为空
        if (value == product.id) {
          if (props.onChange) {
            props.onChange(undefined, undefined);
          }
          setValue(undefined);
        }
        // 重新请求
        fetchProductList({ pageNo: 1, pageSize: 99999 });
      } else {
        message.error(data?.message || "删除失败");
      }
    } catch (error) {
      console.error("删除产品失败", error);
    }
  };

  // 产品选项
  const filtered = products.filter(
    (item: any) => item.name.includes(search) || item.brand.includes(search)
  );

  useImperativeHandle(ref, () => ({
    products,
  }));

  return (
    <Select
      value={value}
      placeholder="请选择产品"
      className="w-fll !h-[72px]"
      open={productOpen}
      onDropdownVisibleChange={(visible) => setProductOpen(visible)}
      dropdownRender={(menu) => (
        <>
          <div className="p-2 border border-solid border-[#f0f0f0]">
            <Input
              allowClear
              placeholder="搜索"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="!border-[#c7c7d6] !rounded-lg"
            />
          </div>
          {addOption}
          <div>{menu}</div>
        </>
      )}
      filterOption={false}
      onSearch={setSearch}
      onChange={(e: any, b: any) => {
        setValue(e);
        if (props.onChange) {
          props.onChange(e, b);
        }
      }}
      optionLabelProp="label"
      allowClear={true}
      loading={listLoading}
    >
      {filtered.map((item: any) => (
        <Select.Option
          className="!h-[72px] !flex !items-center"
          key={item.id}
          value={item.id}
          label={
            <Space className={clsx('w-full',styles['space-component'])}>
              <Avatar
                shape="square"
                size={48}
                src={
                  item.materialFiles?.length
                    ? item.materialFiles[0]?.previewInfo?.resizeImageUrl
                    : ""
                }
              />
              <div className="leading-5 w-full">
                <div className="font-medium text-xs text-gray-700 truncate">{item.name}</div>
                <div className="text-xs text-gray-500 truncate">
                  {item.brand}
                </div>
              </div>
            </Space>
          }
        >
          <div className="flex items-center justify-between">
            <Space className={clsx('overflow-hidden',styles['space-component'])}>
              <Avatar
                shape="square"
                size={48}
                src={
                  item.materialFiles?.length
                    ? item.materialFiles[0]?.previewInfo?.resizeImageUrl
                    : ""
                }
              />
              <div className="leading-5">
                <div className="font-medium text-xs text-gray-700 truncate">{item.name}</div>
                <div className="text-xs text-gray-500 truncate">
                  {item.brand}
                </div>
              </div>
            </Space>
            <div
              onMouseDown={(e) => {
                // 防止二次确认弹窗上的操作收起了下拉选项
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <EditOutlined
                key="edit"
                className="p-2 rounded-md mr-6 hover:bg-gray-300"
                onClick={(e) => {
                  setProductOpen(false);
                  e.stopPropagation();
                  // 编辑
                  showAddGenerateModal?.({
                    ...item,
                    isOtherAdd: true,
                    isEdit: true,
                    limit: getLimit(),
                  });
                }}
              />
              <Popconfirm
                title="确认删除吗？"
                onCancel={(e: any) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onConfirm={(e: any) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleDelete(item);
                }}
              >
                <DeleteOutlined
                  key="delete"
                  className="p-2 rounded-md hover:bg-gray-300 mr-2"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                />
              </Popconfirm>
            </div>
          </div>
        </Select.Option>
      ))}
    </Select>
  );
};

export default forwardRef(ProductSelect);
