/*
 * @Date: 2025-06-11 16:27:05
 * @Author: miroku.yang
 * @Description: 
 */
import requestHelper from '@/utils/requestHelper'

/**
 * 创建任务
 */
export function getBatchTaskCreate(data = {}) {
  return requestHelper.post('v4/demand/aigc/video/batch/task/create', data);
}

/**
 * 获取任务列表
 * @param data 
 * @returns 
 */
export function getTaskList(data = {}) {
  return requestHelper.post('v4/demand/aigc/video/task/page', data)
}

/**
 * 获取数字人列表
 * @param data 
 * @returns 
 */
export function getDigitalAvatarList(data = {}) {
  return requestHelper.post('v4/demand/aigc/digital/avatar/list', data)
}

/**
 * 获取产品列表
 * @param data 
 * @returns 
 */
export function getProductList(data = {}) {
  return requestHelper.post('v4/demand/aigc/product/list', data)
}

/**
 * 产品删除
 */
export function getDeleteProduct(data = {}) {
  return requestHelper.post('v4/demand/aigc/product/delete', data);
}


