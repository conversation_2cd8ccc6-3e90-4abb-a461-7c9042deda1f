"use client";
import React, { useState, useEffect, useRef } from "react";
import { Tabs, Button } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { useRouter, useSearchParams } from "next/navigation";
import useAddGenerate from "./components/useAddGenerate";
import useGenerateVideoAdvertisements from "./components/useGenerateVideoAdvertisements";
import useDigitalHumans from "./components/useDigitalHumans";
import clsx from "clsx";
import styles from "./index.module.scss";
import { getInitialPagination } from "@/constants/table";
import { getTaskList, getProductList } from "./services";
import TableList from "./components/TableList";
import CardList from "./components/CardList";
import pick from "lodash/pick";
import isNil from "lodash/isNil";
import eventBus from "@/utils/eventBus";
import { EVENT_NEMES } from "@/components/Mqtt/consts";

const TaskProductTabs = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeKey, setActiveKey] = useState(searchParams.get("tab") || "1");
  // 选中的数据人信息
  const [humanData, setHumanData] = useState({});
  // 产品列表
  const [products, setProducts] = useState<any[]>([]);
  const [fetchProductLoading, setFetchProductLoading] = useState(false);
  const [fetchProductHasMore, setFetchProductHasMore] = useState(true);
  // 任务列表
  const [tasks, setTasks] = useState<any[]>([]);
  const [listLoading, setListLoading] = useState(false);
  const INITIAL_PAGINATION = getInitialPagination({ pageSize: 20 });
  const [pagination, setPagination] = useState({ ...INITIAL_PAGINATION });
  const { current, pageSize } = pagination;
  // 表头排序
  const [sortedInfo, setSortedInfo] = useState<any>({});
  const sortedInfoRef = useRef<any>(sortedInfo);

  const generateVideoAdvertisementsSuccess = (data: any) => {
    hideGenerateVideoAdvertisements();
    if (data?.code == 0) {
      // 刷新任务列表
      fetchTaskList({
        pageNo: 1,
        pageSize: pageSize,
      });
    }
  };

  // 创建产品
  const { showAddGenerateModal, hideAddGenerateModal, renderAddGenerateModal } =
    useAddGenerate({
      onSuccessCallback: () => {
        fetchProductList({
          pageNo: 1,
          pageSize: pageSize,
        });
      },
    });
  // 数字人抽屉
  const { showDigitalHumans, rendeDigitalHumans, upodateSelectedCard } =
    useDigitalHumans({
      onSuccessCallback: (params: any) => {
        setHumanData(params.humanData || {});
      },
    });
  // 生成视频
  const {
    showGenerateVideoAdvertisements,
    hideGenerateVideoAdvertisements,
    rendeGenerateVideoAdvertisements,
  } = useGenerateVideoAdvertisements({
    showAddGenerateModal,
    hideAddGenerateModal,
    showDigitalHumans,
    humanData,
    upodateSelectedCard,
    onSuccessCallback: generateVideoAdvertisementsSuccess,
  });

  // 表格翻页
  const handleTableChange = (
    { current, pageSize }: any,
    filters: any,
    sorter: any,
    extra: any
  ) => {
    // 切换分页会触发这个函数，仅排序时更新
    if (extra.action == "sort") {
      // 更新表格筛选状态
      // 不能直接赋值sorter，里面有dom 节点,orderByAsc 需要考虑取消排序
      const newSortInfo = {
        columnKey: sorter.columnKey,
        field: sorter.field,
        order: sorter.order,
        orderByAsc:
          sorter.order !== undefined ? sorter.order === "ascend" : null,
        orderByColumn: sorter.order !== undefined ? sorter.field : null,
        id: sorter?.column?.id,
        name: sorter?.column?.name,
        type: sorter?.column?.type,
        code: sorter.field,
      };
      setSortedInfo(newSortInfo);
      sortedInfoRef.current = newSortInfo;
    }
    setPagination({
      ...pagination,
      current: pageSize !== pagination.pageSize ? 1 : current,
      pageSize,
    });
  };

  // 获取任务列表
  const fetchTaskList = async (params?: any) => {
    setListLoading(true);
    try {
      const sortObj = pick(sortedInfoRef.current, [
        "orderByAsc",
        "orderByColumn",
      ]);
      const currentSort =
        sortObj?.orderByAsc === null || !Object.keys(sortObj)?.length
          ? {}
          : sortObj;
      const { data } = await getTaskList({ ...params, ...currentSort });
      if (data?.code === 0) {
        setTasks(data?.result?.items || []);
        setPagination({
          ...pagination,
          current: params?.pageNo || INITIAL_PAGINATION.current,
          pageSize: params?.pageSize || INITIAL_PAGINATION.pageSize,
          total: data?.result?.total || 0,
        });
      }
    } catch (error) {
      console.log(error, "获取任务列表失败");
    } finally {
      setListLoading(false);
    }
  };

  // 获取产品列表
  const fetchProductList = async (params: any) => {
    const isLoading = params?.isLoading ?? true;
    const loadMore = params?.loadMore ?? false;
    setListLoading(isLoading);
    try {
      const { data } = await getProductList(params);
      if (data?.code === 0) {
        setProducts(
          params.pageNo > 1
            ? [...products, ...data?.result?.items]
            : data?.result?.items || []
        );
        setPagination({
          ...pagination,
          current: params?.pageNo || INITIAL_PAGINATION.current,
          pageSize: params?.pageSize || INITIAL_PAGINATION.pageSize,
          total: data?.total || 0,
        });
        setFetchProductHasMore(data?.result?.hasNext);
      }
    } catch (error) {
      console.log(
        error,
        loadMore ? "滚动加载获取产品列表失败" : "获取产品列表失败"
      );
    } finally {
      setListLoading(false);
    }
  };

  useEffect(() => {
    if (current && pageSize) {
      if (activeKey === "1") {
        fetchTaskList({
          pageNo: current,
          pageSize: pageSize,
        });
      } else {
        // 产品列表，如果当前是第一页，则获取产品列表，否则滚动加载，在CardList中处理
        if (activeKey === "2" && current == 1) {
          fetchProductList({
            pageNo: current,
            pageSize: pageSize,
          });
        }
      }
    }
  }, [current, pageSize, activeKey, sortedInfo]);

  const tab = searchParams.get("tab");
  useEffect(() => {
    if (tab) {
      setActiveKey(tab as string);
    }
  }, [tab]);

  const updateFetchProductLoading = (bool: boolean) => {
    setFetchProductLoading(bool);
  };

  const items = [
    {
      key: "1",
      label: "任务记录",
      children: (
        <div className="h-full">
          <div className="h-10 flex justify-end">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                // 这里可以添加逻辑处理生成广告视频
                showGenerateVideoAdvertisements();
              }}
            >
              生成广告视频
            </Button>
          </div>
          <TableList
            dataSource={tasks}
            pagination={pagination}
            loading={listLoading}
            onChange={handleTableChange}
            sortedInfoRef={sortedInfoRef}
          />
        </div>
      ),
    },
    {
      key: "2",
      label: "产品",
      children: (
        <CardList
          pagination={pagination}
          loading={listLoading}
          fetching={fetchProductLoading}
          showAddGenerateModal={showAddGenerateModal}
          products={products}
          hasMore={fetchProductHasMore}
          updateFetchProductLoading={updateFetchProductLoading}
          fetchProductList={fetchProductList}
        />
      ),
    },
  ];

  const onChange = (key: string) => {
    router.replace(`/apps/creative-studio/home?tab=${key}`);
    // 可根据需要处理 tab 切换事件
    setActiveKey(key);
    setPagination({ ...INITIAL_PAGINATION });
    sortedInfoRef.current = {};
    setSortedInfo({});
  };

  useEffect(() => {
    // 监听mqtt
    const fn = (data: any) => {
      const { id, status, statusName, completeNumber, totalNumber } = data;
      const numberObj =
        !isNil(completeNumber) && !isNil(totalNumber)
          ? {
              totalNumber,
              completeNumber,
            }
          : {};
      setTasks((prev: any) => {
        const idx = prev.findIndex((item: any) => item.id === id);
        if (idx > -1) {
          const newDataSource = [...prev];
          newDataSource[idx] = {
            ...newDataSource[idx],
            status: status,
            statusName: statusName,
            ...numberObj,
          };
          return newDataSource;
        }
        return prev;
      });
    };
    eventBus.on(EVENT_NEMES.AIGC_TASK_STATUS_CHANGE, fn);
    return () => {
      eventBus.off(EVENT_NEMES.AIGC_TASK_STATUS_CHANGE, fn);
    };
  }, []);

  return (
    <div className="h-full xl:h-full py-10 px-10 2xl:px-20">
      <Tabs
        activeKey={activeKey}
        items={items}
        onChange={onChange}
        className={clsx("h-full", styles["task-product-tabs"])}
      />
      <div className="overflow-hidden h-0">
        {renderAddGenerateModal()}
        {rendeGenerateVideoAdvertisements()}
        {rendeDigitalHumans()}
      </div>
    </div>
  );
};

export default TaskProductTabs;
