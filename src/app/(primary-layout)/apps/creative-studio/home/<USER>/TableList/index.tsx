/*
 * @Date: 2025-06-25 12:31:20
 * @Author: miroku.yang
 * @Description: 任务记录列表
 */
import { Button, Table, Tooltip, Tag, Progress } from "antd";
import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import dayjs from "dayjs";
import styles from "./index.module.scss";
import isNil from "lodash/isNil";
import { cn } from "@/lib/utils";

interface TableListProps {
  dataSource: any[];
  pagination: any;
  loading: boolean;
  onChange: (pagination: any, filters: any, sorter: any, extra: any) => void;
  sortedInfoRef: any;
}
const empty = "-";
const TableList = ({
  dataSource,
  pagination,
  loading,
  onChange,
  sortedInfoRef,
}: TableListProps) => {
  const router = useRouter();
  const [height, setHeight] = useState(500);
  const tableWrapperRef = useRef<any>(null);

  useEffect(() => {
    const tableWrapper: any = tableWrapperRef.current;
    if (tableWrapper) {
      const tableWrapperHeight = tableWrapper.offsetHeight;
      // 减去表头和分页器高度
      setHeight(tableWrapperHeight - 114);
    }
  }, []);

  // 任务表格列
  const taskColumns = [
    {
      title: "任务ID",
      dataIndex: "id",
      key: "id",
      width: "12%",
      // sorter: true,
      // sortOrder:
      //   sortedInfoRef.current?.field === "id"
      //     ? sortedInfoRef.current?.order
      //     : undefined,
    },
    {
      title: "任务类型",
      dataIndex: "typeNameList",
      key: "typeNameList",
      width: "20%",
      // sorter: true,
      // sortOrder:
      //   sortedInfoRef.current?.field === "typeNameList"
      //     ? sortedInfoRef.current?.order
      //     : undefined,
      render: (typeNameList: any) =>
        typeNameList?.length ? typeNameList.join(",") : empty,
    },
    {
      title: "任务名称",
      dataIndex: "name",
      key: "name",
      width: "22%",
      ellipsis: {
        showTitle: false,
      },
      className:
        "w-full overflow-hidden text-ellipsis truncate whitespace-nowrap h-[55px] flex items-center",
      // sorter: true,
      // sortOrder:
      //   sortedInfoRef.current?.field === "name"
      //     ? sortedInfoRef.current?.order
      //     : undefined,
      render: (name: any) => {
        return (
          <Tooltip placement="topLeft" title={name}>
            <span className="w-full overflow-hidden text-ellipsis truncate whitespace-nowrap ">
              {name}
            </span>
          </Tooltip>
        );
      },
    },
    {
      title: "状态",
      dataIndex: "statusName",
      key: "statusName",
      width: "20%",
      // sorter: true,
      // sortOrder:
      //   sortedInfoRef.current?.field === "statusName"
      //     ? sortedInfoRef.current?.order
      //     : undefined,
      render: (statusName: any, record: any) => {
        const percent =
          record.completeNumber >= record.totalNumber
            ? 100
            : Math.floor((record.completeNumber / record.totalNumber) * 100);

        const isSuccess = record.completeNumber >= record.totalNumber;
        const statusToName: any = {
          1: <Tag color="warning">{statusName}</Tag>,
          2: <Tag color="success">{statusName}</Tag>,
          "-1": <Tag color="error">{statusName}</Tag>,
        };
        return (
          <div className="flex flex-row items-center">
            <span className="flex-shrink-0 text-left w-20">
              {statusToName[record.status] || statusToName[1]}
            </span>
            {isNil(record.completeNumber) ||
            isNil(record.completeNumber) ||
            [2, -1].includes(record.status) ? null : (
              <Progress
                percent={percent}
                status={
                  record.status === -1
                    ? "exception"
                    : isSuccess
                    ? "success"
                    : "active"
                }
                showInfo={true}
                format={() => `${percent}%`}
                className={cn('!w-2/4',styles["progress-component"])}
              />
            )}
          </div>
        );
      },
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: "16%",
      // sorter: true,
      // sortOrder:
      //   sortedInfoRef.current?.field === "createTime"
      //     ? sortedInfoRef.current?.order
      //     : undefined,
      render: (createTime: number) => {
        return createTime
          ? dayjs(createTime).format("YYYY-MM-DD HH:mm:ss")
          : empty;
      },
    },
    {
      title: "操作",
      key: "action",
      width: "10%",
      render: (_: string, record: any) =>
        record.status != -1 ? (
          <Button
            type="link"
            size="small"
            onClick={() => {
              router.push(`/apps/creative-studio/generate/${record.id}`);
            }}
            className="!px-0"
          >
            查看详情
          </Button>
        ) : (
          empty
        ),
    },
  ];

  return (
    <div
      className="w-full h-full overflow-hidden relative pb-5"
      ref={tableWrapperRef}
    >
      <Table
        columns={taskColumns}
        dataSource={dataSource}
        pagination={{
          ...pagination,
          showTotal: (total) => `共${total}条数据`,
        }}
        scroll={dataSource?.length ? { x: "100%", y: height } : {}}
        rowKey="id"
        loading={loading}
        onChange={onChange}
        className={styles["task-table"]}
      />
    </div>
  );
};

export default TableList;
