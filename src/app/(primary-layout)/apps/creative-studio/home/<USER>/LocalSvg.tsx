/*
 * @Date: 2025-06-25 10:46:17
 * @Author: miroku.yang
 * @Description:生成icon
 */
import React from "react";

// 生成任务、产品按钮左侧icon
export const GenerateSvg = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 48 48"
      fill="none"
    >
      <g opacity=".9">
        <g fill="currentColor">
          <use href="#B"></use>
          <use href="#C"></use>
        </g>
        <g stroke="currentColor" stroke-width="4.5">
          <use href="#B"></use>
          <use href="#C"></use>
        </g>
      </g>
      <defs>
        <path
          id="B"
          d="M32.497 5.421c.053-.242.535-.243.589-.001.378 1.694 1.154 4.303 2.545 5.684 1.381 1.372 3.975 2.135 5.655 2.505.241.053.242.532.001.585-1.68.374-4.275 1.142-5.656 2.517-1.391 1.385-2.169 4-2.547 5.689-.054.241-.532.24-.585-.001-.37-1.689-1.136-4.302-2.522-5.687-1.374-1.374-3.956-2.139-5.646-2.513-.242-.053-.242-.54.001-.593 1.69-.37 4.271-1.13 5.645-2.501 1.385-1.382 2.15-3.989 2.52-5.683z"
        ></path>
        <path
          id="C"
          d="M18.726 17.697c.077-.353.782-.355.86-.002.552 2.474 1.686 6.285 3.717 8.303 2.018 2.004 5.807 3.118 8.261 3.659.353.078.353.776.001.855-2.455.546-6.244 1.668-8.262 3.676-2.032 2.023-3.168 5.843-3.72 8.31-.079.352-.777.351-.855-.002-.541-2.467-1.66-6.284-3.684-8.308-2.007-2.007-5.779-3.124-8.247-3.67-.354-.078-.353-.789.001-.867 2.468-.541 6.238-1.65 8.246-3.653 2.023-2.019 3.14-5.828 3.681-8.301z"
        ></path>
      </defs>
    </svg>
  );
};

// 数字人选中后右上角的删除icon 
export const DeleteSvg = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="w-3 h-3 text-red-500"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M6 18L18 6M6 6l12 12"
      />
    </svg>
  );
};
