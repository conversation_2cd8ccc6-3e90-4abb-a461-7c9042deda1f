"use client";

import type React from "react";
import { useState, useRef, useCallback, useEffect, useMemo } from "react";
import { Play } from "lucide-react";

type VideoCardType = {
  id: string;
  name: string;
  typeName: string;
  url: string;
  status: string;
  poster?: string;
};
interface VideoCardProps {
  video: VideoCardType;
}

const VideoCard: React.FC<VideoCardProps> = ({ video }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [videoError, setVideoError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  // 预加载视频
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.load();
    }
  }, [video.url]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);

    if (videoRef.current) {
      // 确保视频已经加载
      if (videoRef.current.readyState >= 3) {
        // HAVE_FUTURE_DATA
        videoRef.current
          .play()
          .then(() => {
            setIsVideoPlaying(true);
          })
          .catch((error) => {
            console.log("Video play failed:", error);
            setIsVideoPlaying(false);
          });
      } else {
        // 如果视频还没加载完，等待加载完成后播放
        const handleCanPlay = () => {
          if (isHovered && videoRef.current) {
            videoRef.current
              .play()
              .then(() => {
                setIsVideoPlaying(true);
              })
              .catch((error) => {
                console.log("Video play failed after load:", error);
                setIsVideoPlaying(false);
              });
          }
          videoRef.current?.removeEventListener("canplay", handleCanPlay);
        };
        videoRef.current.addEventListener("canplay", handleCanPlay);
      }
    }
  }, [isHovered, isVideoLoaded]);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    setIsVideoPlaying(false);

    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.currentTime = 0;
    }
  }, []);

  const handleVideoLoad = useCallback(() => {
    setIsVideoLoaded(true);
    setVideoError(false);
  }, []);

  const handleVideoError = useCallback(
    (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
      setIsVideoLoaded(false);
      setVideoError(true);
    },
    []
  );

  const handleVideoCanPlay = useCallback(() => {
    setIsVideoLoaded(true);
  }, []);

  return (
    <div
      className="relative aspect-[3/4] overflow-hidden bg-gray-200 group"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 封面图：未hover时显示，hover时如果视频未加载成功也显示 */}
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        src={video.poster}
        alt={video.name}
        className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-500 ${
          isHovered && isVideoLoaded && !videoError
            ? "opacity-0"
            : "opacity-100"
        }`}
        draggable={false}
      />

      {/* 视频：hover时显示，未hover时透明 */}
      <video
        ref={videoRef}
        className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-500 ${
          isVideoLoaded && !videoError ? "opacity-100" : "opacity-0"
        }`}
        loop
        playsInline
        preload="auto"
        onLoadedData={handleVideoLoad}
        onCanPlay={handleVideoCanPlay}
        onError={handleVideoError}
        onPlay={() => setIsVideoPlaying(true)}
        onPause={() => setIsVideoPlaying(false)}
      >
        <source src={video.url} type="video/mp4" />
        您的浏览器不支持视频播放。
      </video>

      {/* Video Error Fallback */}
      {videoError && isHovered && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="text-center">
            <Play className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">视频加载失败</p>
          </div>
        </div>
      )}

      {/* Loading Indicator */}
      {isHovered && !isVideoLoaded && !videoError && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20">
          <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {/* Video Playing Indicator */}
      {isVideoPlaying && (
        <div className="absolute top-3 left-3">
          <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span>播放中</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoCard;
