import { Card, message, Spin, Modal } from "antd";
import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import clsx from "clsx";
import styles from "./index.module.scss";
import { PlusOutlined } from "@ant-design/icons";
import { ScrollArea } from "@/components/ui/scroll-area";
import { getDeleteProduct } from "../../services";
import SinoSymbolFont from "@/components/Icon/SinoSymbolFont";

interface CardListProps {
  pagination: any;
  loading: boolean;
  showAddGenerateModal: (params?: any) => void;
  products: any[];
  fetching: boolean;
  hasMore: boolean;
  updateFetchProductLoading: (bool: boolean) => void;
  fetchProductList: (params: any) => Promise<void>;
}

const { confirm } = Modal;

const CardList = ({
  pagination,
  loading,
  showAddGenerateModal,
  products,
  fetching,
  hasMore,
  updateFetchProductLoading,
  fetchProductList,
}: CardListProps) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // 滚动加载
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const scrollTop = container.scrollTop;
      const clientHeight = container.clientHeight;
      const scrollHeight = container.scrollHeight;
      if (
        scrollTop + clientHeight >= scrollHeight - 100 &&
        !loading &&
        !fetching &&
        hasMore
      ) {
        updateFetchProductLoading(true);
        fetchProductList({
          isLoading: false,
          pageNo: pagination.current + 1,
          pageSize: pagination.pageSize,
        }).finally(() => {
          updateFetchProductLoading(false);
        });
      }
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [loading, fetching, hasMore]);

  const handleDeleteConfirm = (e: any, record: any) => {
    e.stopPropagation();
    confirm({
      title: "确认删除?",
      content: "此产品删除后将无法恢复。",
      onOk() {
        return handleDelete(record);
      },
    });
  };

  const handleDelete = async (record: any) => {
    try {
      const { data } = await getDeleteProduct({ id: record.id });
      if (data?.code == 0) {
        message.success("删除成功");
        fetchProductList({
          pageNo: 1,
          pageSize: pagination.pageSize,
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error("删除产品失败", error);
      return false;
    }
  };

  return (
    <ScrollArea className="w-full h-full" ref={containerRef}>
      <Spin spinning={loading}>
        <div
          className="
            grid gap-5
            grid-cols-2
            sm:grid-cols-2
            md:grid-cols-3
            lg:grid-cols-4
            xl:grid-cols-5
            2xl:grid-cols-7
          "
        >
          {/* 添加商品卡片 */}
          <div>
            <Card
              hoverable
              className={clsx(
                "w-full h-[220px] flex flex-col items-center justify-center border !border-dashed !border-[#d9d9d9]",
                styles["add-product-card"]
              )}
              onClick={() => {
                // 添加产品
                showAddGenerateModal();
              }}
            >
              <PlusOutlined style={{ fontSize: 32, color: "#aaa" }} />
              <div className="mt-2">点击新建产品</div>
            </Card>
            <div className="mt-3">添加产品</div>
          </div>
          {/* 商品卡片 */}
          {products.map((item) => (
            <div key={item.id}>
              <Card
                hoverable
                className="w-full h-[220px] block"
                classNames={{
                  cover: "!w-full !h-full !ms-0 !me-0 !mt-0 relative group",
                }}
                cover={
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      // 编辑
                      showAddGenerateModal({ ...item, isEdit: true });
                    }}
                    className="!w-full !h-full overflow-hidden !rounded"
                  >
                    <span
                      className="absolute !flex items-center justify-center !w-[32px] h-[32px] right-2 top-2 bg-[#eee] !rounded opacity-0 group-hover:opacity-100 transform translate-x-4 group-hover:translate-x-0 transition-all duration-300"
                      onClick={(e: any) => handleDeleteConfirm(e, item)}
                    >
                      <SinoSymbolFont type="icon-shanchu" />
                    </span>
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      alt={item.name}
                      src={
                        item.materialFiles?.length
                          ? item.materialFiles[0]?.previewInfo?.resizeImageUrl
                          : ""
                      }
                      className="!w-full !h-full object-contain"
                    />
                  </div>
                }
              ></Card>
              <div className="mt-3 text-ellipsis overflow-hidden whitespace-nowrap">{item.name}</div>
            </div>
          ))}
          {/* 底部加载中 */}
          {fetching && (
            <div className="col-span-full flex items-center justify-center py-4">
              <Spin size="small" />
              <span className="ml-2 text-gray-400">加载中...</span>
            </div>
          )}
        </div>
      </Spin>
    </ScrollArea>
  );
};

export default CardList;
