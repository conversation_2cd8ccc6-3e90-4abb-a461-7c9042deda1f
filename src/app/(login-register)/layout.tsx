"use client";
import { Suspense } from "react";
import Image from "next/image";
import { ConfigProvider } from "antd";
import Loading from "@/components/loader";
export default function LoginAndRegister({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Suspense fallback={<Loading />}>
      <ConfigProvider
        theme={{
          components: {
            Input: {
              borderRadius: 6,
              activeShadow: "",
              controlHeight: 40,
              colorBgContainer: "#f5f5f5",
              warningActiveShadow: "",
              errorActiveShadow: "",
              hoverBorderColor: "rgb(255,255,255)",
              activeBorderColor: "rgb(37,95,230)",
              hoverBg: "rgb(229,230,235)",
              activeBg: "rgb(255,255,255)",
              borderRadiusSM: 8,
              colorBorder: "rgba(255,255,255,0)",
            },
            Button: {
              colorPrimary: "rgb(22,93,255)",
              borderRadius: 6,
              lineWidth: 0,
              colorBgContainer: "#F2F3F5",
              controlHeight: 40,
              colorText: "#1D2129",
            },
            Tabs: {
              colorPrimary: "#1D2129",
              inkBarColor: "rgb(0,85,254)",
              itemColor: "rgb(134,144,156)",
              itemHoverColor: "rgb(29,33,41)",
              itemSelectedColor: "rgb(29,33,41)",
              margin: 32,
              lineWidth: 0,
              itemActiveColor: "rgb(29,33,41)",
            },
          },
          token: {
            borderRadius: 6,
            colorPrimary: "#165dff",
            colorInfo: "#165dff",
          },
        }}
      >
        <div className="w-full h-full flex items-stretch">
          <div className="w-0 h-full xl:w-[384px] 2xl:w-[468px] 3xl:w-[482px] 4xl:w-[576px] overflow-hidden bg-[#EEF0FC] pt-[20px] 2xl:pt-[40px] 3xl:pt-[60px] pb-[20px] 2xl:pb-[40px] flex flex-col justify-between">
            <div>
              <div className="flex flex-col px-[40px] 2xl:px-[60px] mb-[30px] xl:mb-[40px] 3xl:mb-[60px] 4xl:mb-[100px]">
                <Image
                  src="https://static.creativebooster.com/common/logo-d.png"
                  alt="logo"
                  width={145}
                  height={40}
                  className="mb-[32px] 3xl:mb-[40px]"
                />
                <h3 className="font-montserrat text-[18px] leading-[32px] 3xl:text-[24px] 3xl:leading-[40px] text-left">
                  "Command Your Digital Assets, Unleash Creative Potential."​
                </h3>
              </div>
              <div className="px-[38px]">
                <div className="w-full relative aspect-square">
                  <Image
                    src="https://static.creativebooster.com/cb-static/website/images/login.png"
                    alt="login"
                    fill
                    priority
                    className="outline-none"
                    fetchPriority="high"
                  />
                </div>
              </div>
            </div>
            <div className="px-[38px] text-[12px]">
              <div className="mb-3 text-[#86909C] text-center">
                飞书深诺战略合作伙伴© 2020-2023 酷悟酷博数字科技（上海）有限公司
                版权所有
              </div>
              <div className="flex items-center justify-center flex-wrap">
                <a
                  href="https://beian.miit.gov.cn"
                  rel="noreferrer"
                  target="_blank"
                  className="text-[#86909C]"
                >
                  沪ICP备2023020416号-3
                </a>
                <a
                  href="https://beian.mps.gov.cn/#/query/webSearch?code=31010502006700"
                  rel="noreferrer"
                  target="_blank"
                  className="text-[#86909C] ml-1"
                >
                  沪公网安备31010502006700
                </a>
              </div>
            </div>
          </div>
          <div className="flex-1 h-full overflow-hidden">
            <div className="w-full h-full relative">{children}</div>
          </div>
        </div>
      </ConfigProvider>
    </Suspense>
  );
}
