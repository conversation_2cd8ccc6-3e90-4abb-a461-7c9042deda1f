"use client";
import React, { useRef, useState, useEffect, use } from "react";
import {
  Button,
  Divider,
  Form,
  Input,
  message,
  Tabs,
  Checkbox,
  Modal,
} from "antd";
import classNames from "classnames";
import trim from "lodash/trim";
import { observer, useLocalObservable } from "mobx-react-lite";
import { useRouter, useSearchParams } from "next/navigation";
import { COOKIES_MAP, LOGIN_TAB_TYPE } from "@/constants";
import globalStore from "@/mobx/global-store";
import CountDown from "@/components/CountDown";
import { sendVerifyCode } from "@/services/common";
import { goSSO, openUrl } from "@/utils";
import { setStorage, getStorage, getCookie } from "@/utils/storage";
import { emailReg, loginMobileReg, verifyCodeReg } from "@/utils/validate";
import Captcha from "@/components/yundun-captcha";
import styles from "../common.module.scss";

const Login = () => {
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [lastLoginTab, setLastLoginTab] = useState<string>(LOGIN_TAB_TYPE.CODE);
  const [isNewDevice, setIsNewDevice] = useState<boolean>(false);
  const [mobileForm] = Form.useForm();
  const [accountForm] = Form.useForm();
  const globalMobx: any = useLocalObservable(() => globalStore);
  const captchaInsRef = useRef<any>(null);

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const activeTabKey = getStorage("lastLoginTab");
    if (activeTabKey) {
      setLastLoginTab(activeTabKey);
    }
  }, []);
  const checkValidateFetch = () => {
    return globalMobx.checkValidateAjax();
  };

  const changeLoginTab = (val: string, isNewDevice: boolean = false) => {
    setStorage("lastLoginTab", val);
    setIsNewDevice(isNewDevice && val === LOGIN_TAB_TYPE.CODE);
    setLastLoginTab(val);
  };

  /**
   * 登录后回调
   * @param res
   * @param type
   * @returns
   */
  const handleCallback = (res: any) => {
    if (!res) {
      // 网络异常
      setConfirmLoading(false);
      return;
    }
    const code = res?.code;
    if (code === 0) {
      // todo: 跳转引导页
      const redirectUrl = searchParams.get('redirect');
      if (redirectUrl) {
        router.replace(
          `/corporations?redirect=${encodeURIComponent(redirectUrl)}`
        );
        return;
      }
      router.replace("/corporations");
      return;
    }
    if (code === 201011063) {
      // 新设备，切换验证码登录，并展示风险提示文案
      changeLoginTab(LOGIN_TAB_TYPE.CODE, true);
    }
    setConfirmLoading(false);
  };

  const loginFetch = async (paramsData: any, type: any) => {
    const result = await globalMobx.login(paramsData, type);
    handleCallback(result);
  };

  const handleSubmit = async (values: any, type: string) => {
    if (confirmLoading || !captchaInsRef.current) return;
    setConfirmLoading(true);
    let origin: any = getCookie(COOKIES_MAP.GIVEAWAY_CHANNEL) || "";
    if (origin?.toLocaleUpperCase() === "SSO") {
      origin = "";
    }
    const assignData: any = {};
    if (origin) {
      assignData.origin = origin;
    }
    const paramsData: any = Object.assign(assignData, values);
    paramsData.account = trim(paramsData.account);
    checkValidateFetch().then(async (isValidate: boolean) => {
      if (!isValidate) {
        await loginFetch(paramsData, type);
        return;
      }
      try {
        const { validate } = await captchaInsRef.current.verify();
        paramsData.validate = validate;
        if (validate) {
          await loginFetch(paramsData, type);
          if (captchaInsRef.current?.open) {
            setConfirmLoading(false);
          }
        }
      } catch (error: any) {
        if (error && "open" in error && !error.open) {
          setConfirmLoading(false);
        }
        console.error("登录提交失败: ", error);
      }
    });
  };

  /**
   * 登录提交
   */
  const handleOk = async (form: any, type: string) => {
    try {
      const res = await form.validateFields();
      const { protocol, ...values } = res;
      if (!protocol) {
        Modal.confirm({
          title: "友情提示",
          centered: true,
          content: (
            <div>
              我已阅读并同意
              <span
                className="link-button text-[#006CFF]"
                onClick={() => {
                  window.open("/agreement");
                }}
              >
                用户协议
              </span>
              和
              <span
                className="link-button text-[#006CFF]"
                onClick={() => {
                  window.open("/policy");
                }}
              >
                隐私政策
              </span>
            </div>
          ),
          onOk: async () => {
            form.setFieldsValue({ protocol: true });
            await handleSubmit(values, type);
          },
          okText: "同意并登录",
          cancelText: "取消",
        });
        return;
      }
      await handleSubmit(values, type);
    } catch (e: any) {
      console.log("登录提交失败: ", e);
    }
  };

  /**
   * 发送验证码
   * @param startFn
   */
  const handleSendCode = async (startFn: () => void) => {
    try {
      const values = await mobileForm.validateFields(["account"]);
      if (!captchaInsRef.current) return;
      const account = values.account;
      if (loginMobileReg.test(account) || emailReg.test(account)) {
        // 人机校验
        const { validate } = await captchaInsRef.current.verify();
        startFn();
        const { data = {} } = await sendVerifyCode({ account, validate });
        if (data && data.code === 0) {
          message.success("验证码发送成功");
          if (process.env.NODE_ENV !== "production" && data?.result) {
            mobileForm.setFieldsValue({ captcha: data.result });
          }
        }
      } else {
        mobileForm.setFields([
          {
            name: ["account"],
            errors: ["请输入有效手机号或邮箱"],
            touched: false,
          },
        ]);
      }
    } catch (error) {
      console.error("发送验证码: ", error);
    }
  };

  // 手机验证码登录
  const MobileLogin = () => {
    return (
      <div>
        <Form
          layout="vertical"
          requiredMark={false}
          className={styles.main}
          form={mobileForm}
        >
          {isNewDevice && (
            <div style={{ color: "#ff0000", marginBottom: 20 }}>
              【新设备安全验证】您正在使用新设备登录。为保障账号安全，需要使用验证码登录，以确保是本人操作。
            </div>
          )}
          <Form.Item
            style={{ marginBottom: 30 }}
            label=""
            name="account"
            rules={[
              {
                required: true,
                message: "手机号或邮箱不能为空",
                whitespace: true,
              },
              () => ({
                validator(_, value) {
                  const v = (value || "").trim();
                  if (v && !loginMobileReg.test(v) && !emailReg.test(v)) {
                    return Promise.reject(new Error("请输入有效手机号或邮箱"));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <Input
              suffix=" "
              placeholder="请输入手机号或邮箱"
              // className={"cover-autofill-bg-color"}
              // className={'custom-input'}
            />
          </Form.Item>
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <Form.Item
              className={styles.password}
              style={{
                flex: 1,
                display: "inline-block",
                marginBottom: 30,
                marginRight: 16,
              }}
              label=""
              name="captcha"
              rules={[
                { required: true, message: "验证码不能为空", whitespace: true },
                () => ({
                  validator(_, value) {
                    const v = (value || "").trim();
                    if (v && !verifyCodeReg.test(v)) {
                      return Promise.reject(new Error("请输入有效验证码"));
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Input
                style={{ width: "100%" }}
                suffix=" "
                placeholder="请输入验证码"
                className="cover-autofill-bg-color"
              />
            </Form.Item>
            <Form.Item
              style={{
                width: "130px",
                textAlign: "right",
                display: "inline-block",
                marginBottom: 30,
              }}
            >
              {" "}
              <div className="psd-label">
                <CountDown
                  seconds={60}
                  render={(onStart, disabled, current) => {
                    if (disabled) {
                      return (
                        <Button
                          className={classNames(
                            styles["verify-code-disabled"],
                            styles["get-code"]
                          )}
                          disabled={disabled}
                        >{`${current}s 重新发送`}</Button>
                      );
                    }
                    return (
                      <Button
                        onClick={() => handleSendCode(onStart)}
                        className={styles["get-code"]}
                      >
                        获取验证码
                      </Button>
                    );
                  }}
                />
              </div>
            </Form.Item>
          </div>
          <Form.Item
            style={{ marginBottom: 28 }}
            name="protocol"
            valuePropName="checked"
            // rules={[
            //   {
            //     validator: (_, value) =>
            //       value ? Promise.resolve() : Promise.reject(new Error(t('user:t32'))),
            //   },
            // ]}
          >
            <Checkbox className={styles["read-protocol-wrap"]}>
              {/* id 探活使用，不可删除 */}
              <div>
                我已阅读并同意
                <span
                  className="link-button text-[#006CFF]"
                  onClick={(e) => {
                    e.stopPropagation();
                    openUrl("/agreement");
                  }}
                >
                  用户协议
                </span>
                和
                <span
                  className="link-button text-[#006CFF]"
                  onClick={(e) => {
                    e.stopPropagation();
                    openUrl("/policy");
                  }}
                >
                  隐私政策
                </span>
              </div>
            </Checkbox>
          </Form.Item>
          <div className={styles["submit-form-item"]}>
            <Button
              block
              type="primary"
              className={styles["submit-button"]}
              loading={confirmLoading}
              onClick={() => handleOk(mobileForm, "mobile")}
            >
              登录/注册
            </Button>
          </div>
          <div className={styles["sso-wrapper"]}>
            <Divider plain className={styles["divider-line"]}>
              更多登录方式
            </Divider>
            <Button
              className="w-full"
              onClick={() => {
                goSSO();
              }}
            >
              SSO登录
            </Button>
          </div>
        </Form>
      </div>
    );
  };

  // 账号密码登录
  const AccountLogin = () => {
    return (
      <div>
        <div className={styles.register}>
          <span className={styles["new-user"]}>新用户？</span>
          <span
            className={classNames("link-button", styles["register-button"])}
            onClick={() => router.push("/register")}
          >
            立即注册
          </span>
        </div>
        <div className={styles["lrf-main"]}>
          <Form
            layout="vertical"
            requiredMark={false}
            name="normal_login"
            className={styles.main}
            form={accountForm}
          >
            <Form.Item
              name="account"
              style={{ marginBottom: 30 }}
              rules={[
                {
                  required: true,
                  message: "手机号或邮箱不能为空",
                  whitespace: true,
                },
                () => ({
                  validator(_, value) {
                    const v = (value || "").trim();
                    if (v && !loginMobileReg.test(v) && !emailReg.test(v)) {
                      return Promise.reject(
                        new Error("请输入有效手机号或邮箱")
                      );
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Input
                suffix=" "
                placeholder="请输入手机号或邮箱"
                className={classNames(
                  "cover-autofill-bg-color",
                  styles["cmp-input"]
                )}
              />
            </Form.Item>
            <Form.Item
              className={styles.password}
              style={{ marginBottom: 30 }}
              name="password"
              rules={[
                {
                  required: true,
                  message: "密码不能为空",
                  whitespace: true,
                },
              ]}
            >
              <Input.Password
                placeholder="请输入密码"
                className={classNames(
                  "cover-autofill-bg-color",
                  styles["cmp-input"]
                )}
                type="password"
                maxLength={16}
              />
            </Form.Item>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                paddingBottom: 28,
              }}
            >
              <Form.Item
                name="protocol"
                valuePropName="checked"
                noStyle
                // rules={[
                //   {
                //     validator: (_, value) =>
                //       value ? Promise.resolve() : Promise.reject(new Error(t('user:t32'))),
                //   },
                // ]}
              >
                <Checkbox className={styles["read-protocol-wrap"]}>
                  {/* id 探活使用，不可删除 */}
                  <div id="d8e362">
                    我已阅读并同意
                    <span
                      className="link-button text-[#006CFF]"
                      onClick={(e) => {
                        e.stopPropagation();
                        openUrl("/agreement");
                      }}
                    >
                      用户协议
                    </span>
                    和
                    <span
                      className="link-button text-[#006CFF]"
                      onClick={(e) => {
                        e.stopPropagation();
                        openUrl("/policy");
                      }}
                    >
                      隐私政策
                    </span>
                  </div>
                </Checkbox>
              </Form.Item>
              <div
                className={styles["forgot-password"]}
                style={{ marginTop: 0 }}
              >
                <span
                  className={classNames(
                    "link-button",
                    styles["register-button"]
                  )}
                  onClick={() => router.push("/reset-password")}
                >
                  忘记密码?
                </span>
              </div>
            </div>
            <div className={styles["submit-form-item"]}>
              <Button
                id="d8e361" // 探活使用，不可删除
                type="primary"
                className={styles["submit-button"]}
                loading={confirmLoading}
                onClick={() => handleOk(accountForm, "account")}
              >
                立即登录
              </Button>
            </div>
            <div className={styles["sso-wrapper"]}>
              <Divider plain className={styles["divider-line"]}>
                更多登录方式
              </Divider>
              <Button
                className="w-full"
                onClick={() => {
                  goSSO();
                }}
              >
                SSO登录
              </Button>
            </div>
          </Form>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="w-[320px] sm:w-[420px] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]">
        <div className="min-h-[510px]">
          <Tabs
            activeKey={lastLoginTab}
            onChange={(val: string) => changeLoginTab(val, false)}
            items={[
              {
                key: LOGIN_TAB_TYPE.CODE,
                label: "验证码登录",
                children: <MobileLogin />,
              },
              {
                key: LOGIN_TAB_TYPE.ACCOUNT,
                label: "密码登录",
                children: <AccountLogin />,
              },
            ]}
          />
        </div>
      </div>

      <Captcha ref={captchaInsRef} />
    </>
  );
};
export default observer(Login);
