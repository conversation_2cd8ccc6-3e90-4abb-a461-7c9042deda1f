"use client";
import React, { useRef, useState } from "react";
import { Button, Form, message, Input, Divider } from "antd";
import classNames from "classnames";
import { observer, useLocalObservable } from "mobx-react-lite";
import { useRouter } from "next/navigation";
import Captcha from "@/components/yundun-captcha";
import { validateAccount } from "@/services/user";
import globalStore from "@/mobx/global-store";
import CountDown from "@/components/CountDown";
import { sendVerifyCode } from "@/services/common";
import { goSSO } from "@/utils";
import {
  loginMobileReg,
  verifyCodeReg,
  loginPwdReg,
  emailReg,
} from "@/utils/validate";
import styles from "../common.module.scss";

const Forgot = () => {
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const captchaInsRef = useRef<any>(null);
  const globalMobx = useLocalObservable(() => globalStore);

  const router = useRouter();

  /**
   * 发送验证码
   * @param startFn
   */
  const handleSendCode = async (startFn: () => void) => {
    try {
      const values = await form.validateFields(["account"]);
      const account = values.account;
      if (loginMobileReg.test(account) || emailReg.test(account)) {
        // 人机校验
        const { validate } = await captchaInsRef.current.verify();
        startFn();
        const { data = {} } = await sendVerifyCode({ account, validate });
        if (data && data.code === 0) {
          message.success("验证码发送成功");
          if (process.env.NODE_ENV !== "production" && data?.result) {
            form.setFieldsValue({ code: data.result });
          }
        }
      } else {
        form.setFields([
          {
            name: ["account"],
            errors: ["请输入有效手机号或邮箱"],
            touched: false,
          },
        ]);
      }
    } catch (error) {
      console.error("发送验证码失败: ", error);
    }
  };

  // 获取resetCode
  const getResetCode = async () => {
    try {
      // 获取resetCode
      const { data = {} } = await validateAccount({
        code: form.getFieldValue("code"),
        account: form.getFieldValue("account"),
      });
      if (data?.code === 0 && data?.success) {
        form.setFieldsValue({ resetCode: data?.result?.resetCode });
      }
      return Promise.resolve({
        code: data?.code,
        message: data?.message || "校验失败",
        resetCode: data?.result?.resetCode || 0,
      });
    } catch (err) {
      console.error("获取resetCode失败: ", err);
      return Promise.resolve({
        code: -1,
        message: "校验失败",
        resetCode: -1,
      });
    }
  };

  /**
   * 重置密码提交
   */
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      if (confirmLoading) return;
      setConfirmLoading(true);
      const { code, resetCode } = await getResetCode();
      if (code !== 0) {
        setConfirmLoading(false);
        return;
      }
      const result: any = await globalMobx.resetPassword({
        password: values.password,
        resetCode,
      });
      if (result?.code === 0) {
        message.success("密码修改成功");
        router.replace("/login");
        return;
      }
      setConfirmLoading(false);
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <>
      <div className="w-[320px] sm:w-[420px] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]">
        <div className="min-h-[510px]">
          <h3 className={styles["lrf-title"]}>忘记密码</h3>
          <div className={styles.register}>
            <span
              className={classNames("link-button", styles["register-button"])}
              onClick={() => router.push("/login")}
            >
              返回登录
            </span>
          </div>
          <div className={styles["lrf-main"]}>
            <Form
              layout="vertical"
              requiredMark={false}
              className={styles.main}
              form={form}
            >
              <Form.Item
                style={{ marginBottom: 30 }}
                label=""
                name="account"
                rules={[
                  {
                    required: true,
                    message: "手机号或邮箱不能为空",
                    whitespace: true,
                  },
                  () => ({
                    validator(_, value) {
                      const v = (value || "").trim();
                      if (v && !loginMobileReg.test(v) && !emailReg.test(v)) {
                        return Promise.reject(
                          new Error("请输入有效手机号或邮箱")
                        );
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <Input
                  suffix=" "
                  placeholder="请输入手机号或邮箱"
                  className="cover-autofill-bg-color"
                />
              </Form.Item>
              <div style={{ display: "flex", justifyContent: "space-between" }}>
                <Form.Item
                  className={styles.password}
                  style={{
                    flex: 1,
                    display: "inline-block",
                    marginBottom: 30,
                    marginRight: 16,
                  }}
                  label=""
                  name="code"
                  rules={[
                    {
                      required: true,
                      message: "验证码不能为空",
                      whitespace: true,
                    },
                    () => ({
                      validator(_, value) {
                        const v = (value || "").trim();
                        if (v && !verifyCodeReg.test(v)) {
                          return Promise.reject(new Error("请输入有效验证码"));
                        }
                        return Promise.resolve();
                      },
                    }),
                  ]}
                >
                  <Input
                    style={{ width: "100%" }}
                    suffix=" "
                    placeholder={"请输入验证码"}
                    className="cover-autofill-bg-color"
                  />
                </Form.Item>
                <Form.Item
                  style={{
                    width: "94px",
                    textAlign: "right",
                    display: "inline-block",
                    marginBottom: 30,
                  }}
                >
                  {" "}
                  <div className="psd-label">
                    <CountDown
                      seconds={60}
                      render={(onStart, disabled, current) => {
                        if (disabled) {
                          return (
                            <Button
                              className={classNames(
                                styles["verify-code-disabled"],
                                styles["get-code"]
                              )}
                              disabled={disabled}
                            >{`${current}s 重新发送`}</Button>
                          );
                        }
                        return (
                          <Button
                            onClick={() => handleSendCode(onStart)}
                            className={styles["get-code"]}
                          >
                            获取验证码
                          </Button>
                        );
                      }}
                    />
                  </div>
                </Form.Item>
              </div>
              <Form.Item
                style={{ marginBottom: 40 }}
                label=""
                name="password"
                // dependencies={['password']}
                rules={[
                  {
                    required: true,
                    message: "密码不能为空",
                    whitespace: true,
                  },
                  () => ({
                    validator(_, value) {
                      const v = (value || "").trim();
                      if (v && !loginPwdReg.test(v) && !emailReg.test(v)) {
                        return Promise.reject(
                          new Error("8-16位，大写字母、小写字母、数字各1种")
                        );
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <Input.Password
                  type="password"
                  placeholder="请输入密码"
                  className="cover-autofill-bg-color"
                />
              </Form.Item>
              <div className={styles["submit-form-item"]}>
                <Button
                  type="primary"
                  className={styles["submit-button"]}
                  loading={confirmLoading}
                  onClick={handleOk}
                >
                  确定
                </Button>
              </div>
              <div className={styles["sso-wrapper"]}>
                <Divider plain className={styles["divider-line"]}>
                  更多登录方式
                </Divider>
                <Button
                  className="w-full"
                  onClick={() => {
                    goSSO();
                  }}
                >
                  SSO登录
                </Button>
              </div>
              <Form.Item name="resetCode" noStyle>
                <Input type="hidden" />
              </Form.Item>
            </Form>
          </div>
        </div>
      </div>
      <Captcha ref={captchaInsRef} />
    </>
  );
};

export default observer(Forgot);
