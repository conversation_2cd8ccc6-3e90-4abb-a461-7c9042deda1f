$primary-color: #006cff;

@mixin flex($f: row) {
  display: flex;
  flex-direction: $f;
}

@mixin commonTextStyle() {
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 400;
  text-align: center;
  opacity: 0.85;
  line-height: 24px;
}

.lrf-title {
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 30px;
  font-family: PingFangSC-Semibold, PingFang SC;
  line-height: 38px;

  &-en {
    font-family: "Montserrat";
  }
}

.lrf-main {
  width: 100%;

  :global(.ant-form-item) {
    position: relative;

    .ant-form-item-explain {
      position: absolute;
      // bottom: -24px;
    }
  }

  :global(.ant-input-affix-wrapper) {
    & > input.ant-input {
      border-radius: 0 !important;
    }
  }

  .forgot-password {
    margin-top: 16px;
    text-align: right;
  }

  .submit-form-item {
    margin-bottom: 0;
    // padding-top: 12px;
  }

  .submit-button {
    width: 100%;
    height: 38px !important;
    background: $primary-color;
    border-color: $primary-color;
  }
}

.sso-wrapper {
  margin-top: 54px;
}

.divider-line {
  margin-bottom: 30px !important;

  span {
    color: #86909c;
    font-weight: 400;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 20px;
  }

  &::after,
  &::before {
    border-top-color: #979797 !important;
    opacity: 0.2;
  }
}

.register {
  @include flex();
  align-items: center;
  margin-bottom: 32px;

  .new-user {
    margin-right: 8px;
    font-size: 16px;
    @include commonTextStyle();
  }

  &-button {
    color: $primary-color;
    font-weight: 600;
    font-size: 16px;
    font-family: PingFangSC-Semibold, PingFang SC;
    line-height: 24px;
  }
}

.register-button {
  @include commonTextStyle();
  color: $primary-color !important;
  font-weight: 600;
  font-size: 16px !important;
  opacity: 1;
}

.get-code {
  color: #ffffff;
  font-weight: 500;
  font-size: 14px !important;
  font-family: PingFangSC-Medium, PingFang SC;
  line-height: 20px;
  padding: 6px 11px !important;
  width: 100%;
  height: 40px !important;

  &:hover {
    color: #ffffff;
  }
}

@media screen and (max-width: 1440px) {
  .title-wrap {
    margin: 58px auto 54px;
  }

  .new-user,
  .register-button {
    font-size: 14px !important;
  }

  .material-dl {
    margin-top: 40px;
    margin-bottom: 40px;
  }

  .register {
    margin-bottom: 24px;
  }

  .lrf-main {
    .submit-form-item {
      margin-bottom: 0px !important;
    }

    .register-captcha-form-item {
      margin-top: 0px !important;
    }
  }

  .sso-wrapper {
    margin-top: 36px;

    .divider-line {
      margin-top: 12px !important;
      margin-bottom: 16px !important;
    }
  }

  .sso-register-wrapper {
    margin-top: 24px !important;
  }
}
