"use client";
import React, { useState, useEffect, useRef } from "react";
import { Button, Form, Input, message, Checkbox, Divider } from "antd";
import classNames from "classnames";
import { observer, useLocalObservable } from "mobx-react-lite";
import { getCookie } from "@/utils/storage";
import { useRouter } from "next/navigation";
import Captcha from "@/components/yundun-captcha";
import globalStore from "@/mobx/global-store";
import CountDown from "@/components/CountDown";
import { sendVerifyCode } from "@/services/common";
import { goSSO } from "@/utils";
import { COOKIES_MAP } from "@/constants";
import {
  loginMobileReg,
  verifyCodeReg,
  loginPwdReg,
  emailReg,
} from "@/utils/validate";
import styles from "../common.module.scss";

const Register = () => {
  const globalMobx: any = useLocalObservable(() => globalStore);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [confirmDisabled, setConfirmDisabled] = useState(false);
  const [form] = Form.useForm();
  const captchaInsRef = useRef<any>(null);

  const router = useRouter();

  useEffect(() => {
    setConfirmDisabled(!form.getFieldValue("protocol"));
  }, []);

  /**
   * 获取验证码
   * @param starTiming
   */
  const handleStart = async (starTiming: () => void) => {
    try {
      const values = await form.validateFields(["account"]);
      const account = values.account;
      // 滑块校验
      const { validate } = await captchaInsRef.current.verify();
      starTiming();
      const { data = {} } = await sendVerifyCode({ account, validate });
      if (data && data.code === 0) {
        message.success("验证码发送成功");
        if (process.env.NODE_ENV !== "production" && data?.result) {
          form.setFieldsValue({ captcha: data.result });
        }
      }
    } catch (err) {
      console.error("发送验证码: ", err);
    }
  };

  /**
   * 提交注册
   */
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      if (confirmLoading) return;
      setConfirmLoading(true);
      let origin: any = getCookie(COOKIES_MAP.GIVEAWAY_CHANNEL) || "";
      if (origin?.toLocaleUpperCase() === "SSO") {
        origin = "";
      }
      const params: any = Object.assign(origin ? { origin } : {}, values);
      const result = await globalMobx.register(params);
      const code = result?.code;
      if (code === 0) {
        // 注册成功
        router.replace("/corporations");
        return;
      }
      setConfirmLoading(false);
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <>
      <div className="w-[320px] sm:w-[420px] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]">
        <div className="min-h-[510px]">
          <h3 className={styles["lrf-title"]}>立即注册</h3>
          <div className={styles.register}>
            <span className={styles["new-user"]}>已经有账号了？</span>
            <span
              className={classNames("link-button", styles["register-button"])}
              onClick={() => router.push("/login")}
            >
              立即登录
            </span>
          </div>
          <div className={styles["lrf-main"]}>
            <Form
              layout="vertical"
              requiredMark={false}
              className={styles.main}
              form={form}
            >
              <Form.Item
                label=""
                name="account"
                style={{ marginBottom: 30 }}
                rules={[
                  {
                    required: true,
                    message: "手机号或邮箱不能为空",
                    whitespace: true,
                  },
                  () => ({
                    validator(_, value) {
                      const v = (value || "").trim();
                      if (v && !loginMobileReg.test(v) && !emailReg.test(v)) {
                        return Promise.reject(
                          new Error("请输入有效手机号或邮箱")
                        );
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <Input
                  suffix=" "
                  placeholder="请输入手机号或邮箱"
                  className="cover-autofill-bg-color"
                />
              </Form.Item>
              <Form.Item
                className={styles.password}
                style={{ marginBottom: 30 }}
                label=""
                name="password"
                rules={[
                  {
                    required: true,
                    message: "密码不能为空",
                    whitespace: true,
                  },
                  () => ({
                    validator(_, value) {
                      const v = (value || "").trim();
                      if (v && !loginPwdReg.test(v)) {
                        return Promise.reject(
                          new Error("8-16位，大写字母、小写字母、数字各1种")
                        );
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <Input.Password
                  type="password"
                  placeholder="请输入密码"
                  className="cover-autofill-bg-color"
                />
              </Form.Item>
              <div
                style={{ display: "flex", justifyContent: "space-between" }}
                className={styles["register-captcha-form-item"]}
              >
                <Form.Item
                  className={styles.code}
                  style={{
                    flex: 1,
                    // width: 'calc(100% - 146px)',
                    // display: 'inline-block',
                    marginBottom: 30,
                    marginRight: 16,
                  }}
                  name="captcha"
                  rules={[
                    {
                      required: true,
                      message: "验证码不能为空",
                      whitespace: true,
                    },
                    () => ({
                      validator(_, value) {
                        const v = (value || "").trim();
                        if (v && !verifyCodeReg.test(v)) {
                          return Promise.reject(new Error("请输入有效验证码"));
                        }
                        return Promise.resolve();
                      },
                    }),
                  ]}
                >
                  <Input
                    suffix=" "
                    placeholder="请输入验证码"
                    className="cover-autofill-bg-color"
                  />
                </Form.Item>
                <Form.Item
                  style={{
                    width: "130px",
                    textAlign: "right",
                    display: "inline-block",
                    marginBottom: 30,
                  }}
                >
                  <div className={styles["psd-label"]}>
                    <CountDown
                      seconds={60}
                      render={(onStart, disabled, current) => {
                        if (disabled) {
                          return (
                            <Button
                              className={classNames(
                                styles["verify-code-disabled"],
                                styles["get-code"]
                              )}
                              disabled={disabled}
                            >{`${current}s重新发送`}</Button>
                          );
                        }
                        return (
                          <Button
                            onClick={() => handleStart(onStart)}
                            className={styles["get-code"]}
                          >
                            获取验证码
                          </Button>
                        );
                      }}
                    />
                  </div>
                </Form.Item>
              </div>
              <Form.Item
                style={{ marginBottom: 28 }}
                name="protocol"
                valuePropName="checked"
                rules={[
                  {
                    validator: (_, value) =>
                      value
                        ? Promise.resolve()
                        : Promise.reject(new Error("请阅读协议后勾选")),
                  },
                ]}
              >
                <Checkbox
                  className={styles["read-protocol-wrap"]}
                  onChange={(checkedValue) => {
                    setConfirmDisabled(!checkedValue?.target?.checked);
                  }}
                >
                  <div>
                    我已阅读并同意
                    <span
                      className="link-button text-[#006CFF]"
                      onClick={() => {
                        window.open("/agreement");
                      }}
                    >
                      用户协议
                    </span>
                    和
                    <span
                      className="link-button text-[#006CFF]"
                      onClick={() => {
                        window.open("/policy");
                      }}
                    >
                      隐私政策
                    </span>
                  </div>
                </Checkbox>
              </Form.Item>
              <div className={styles["submit-form-item"]}>
                <Button
                  type="primary"
                  className={styles["submit-button"]}
                  loading={confirmLoading}
                  onClick={handleOk}
                  disabled={confirmDisabled}
                >
                  确定
                </Button>
              </div>
              <div
                className={classNames(
                  styles["sso-wrapper"],
                  styles["sso-register-wrapper"]
                )}
              >
                <Divider plain className={styles["divider-line"]}>
                  更多登录方式
                </Divider>
                <Button
                  className="w-full"
                  onClick={() => {
                    goSSO();
                  }}
                >
                  SSO登录
                </Button>
              </div>
            </Form>
          </div>
        </div>
      </div>

      <Captcha ref={captchaInsRef} />
    </>
  );
};
export default observer(Register);
