/**
 * Desc:获取appId
 * Date:20240618
 */
const axios = require('axios')

const config = {
  dev: {
    config_server_url: 'http://o-dev-sino-apollo-configservice.meetsocial.cn',
    app_id: 'sino-fe-cb',
    cluster_name: 'dev',
    namespace: 'TD.sino-fe-ad-web',
  },
  test: {
    config_server_url: 'http://o-dev-sino-apollo-configservice.meetsocial.cn',
    app_id: 'sino-fe-cb',
    cluster_name: 'test',
    namespace: 'TD.sino-fe-ad-web',
  },
  pre: {
    config_server_url: 'https://pre-sino-apollo-configservice.meetsocial.cn',
    app_id: 'sino-fe-cb',
    cluster_name: 'pre',
    namespace: 'TD.sino-fe-ad-web',
  },
  prod: {
    config_server_url: 'https://sino-apollo-configservice.meetsocial.cn',
    app_id: 'sino-fe-cb',
    cluster_name: 'pro',
    namespace: 'TD.sino-fe-ad-web',
  },
}

async function getOSSConfig(env) {
  const envData = config[env]
  if (!envData) {
    console.log(`未找到${env}，apollo配置`)
    process.exit(1)
  }
  try {
    const { data } = await axios.get(
      `${envData.config_server_url}/configs/${envData.app_id}/${envData.cluster_name}/${envData.namespace}`,
    )
    const { APP_ID } = data?.configurations
    return APP_ID || null
  } catch (e) {
    console.log('读取apollo失败：', e)
    return null
  }
}

module.exports = getOSSConfig
