const path = require('path')
const fs = require('fs')
// eslint-disable-next-line import/no-extraneous-dependencies
const nunjucks = require('nunjucks')
const getOSSConfig = require('./apollo')

async function generate() {
  const appId = await getOSSConfig(process.env.APP_ENV)
  if (!appId) {
    console.log(`未读取到APP_ID`)
    process.exit(1)
  }
  const config = {
    appId,
    cloudType: 'aliyun',
    rule: {
      from: '.next/static',
      to: `./website/dam/${process.env.APP_ENV}/_next/static`,
      ignore: '**/.DS_store',
      noCache: '**/*.html',
    },
  }
  // 生成cdn.config.js文件
  nunjucks.configure('scripts/cdn')
  const res = nunjucks.render('generate.njk', { config })
  fs.writeFileSync(path.resolve(process.cwd(), 'cdn.config.js'), res)
  console.log('生成cdn.config.js文件完成')
}

generate()
